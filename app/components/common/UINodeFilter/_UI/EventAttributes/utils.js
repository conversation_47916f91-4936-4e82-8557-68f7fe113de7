import { OrderedMap } from 'immutable';

import {
  validateItemCondition,
  buildValueConditionFromAPI,
  getOperatorByValue,
  getOperatorByProperty,
  getErrorNumber,
} from '../../../../../containers/Filters/utils';
import { mapItemEventPropertyForStory } from '../../../../../services/map';
import { generateKey, safeParse } from '../../../../../utils/common';

export function validateRulesEventAttributes(conditions = OrderedMap({})) {
  const res = {
    total: 0,
    miss: 0,
    status: false,
    errors: [],
  };

  conditions.forEach((condition, groupKey) => {
    condition.forEach((item, itemKey) => {
      res.total += 1;

      if (item.get('isInit') !== true) {
        if (
          item.get('property') === null ||
          item.get('property') === undefined
        ) {
          res.miss += 1;

          res.errors.push({
            path: [groupKey, itemKey, 'error'],
            error: 1,
          });
        } else {
          const refineWithProperties = safeParse(
            item.get('refineWithProperties'),
            null,
          );

          if (refineWithProperties !== null && refineWithProperties.size > 0) {
            const isInit = refineWithProperties.first().get('isInit');
            if (isInit !== true) {
              refineWithProperties.forEach((refine, refineKey) => {
                if (!validateItemCondition(refine, undefined)) {
                  res.miss += 1;
                  const error = getErrorNumber(
                    refine.get('property'),
                    refine.get('operator'),
                  );
                  res.errors.push({
                    path: [
                      groupKey,
                      itemKey,
                      'refineWithProperties',
                      refineKey,
                      'error',
                    ],
                    error,
                  });
                }
              });
            }
          } else {
            res.miss += 1;
            res.errors.push({
              path: [groupKey, itemKey, 'error'],
              error: 2,
            });
          }
        }
      }
    });
  });

  res.status = res.errors.length === 0;
  return res;
}

function toConditionCompPropUI(
  item,
  conditionType,
  mapGroupItemAttributes,
  mapInfo = {},
) {
  const keyLookup = `${item.column}-${safeParse(item.itemTypeId, 0)}`;
  let property = mapGroupItemAttributes[keyLookup];
  if (property !== undefined) {
    const objValue = buildValueConditionFromAPI(item, property);
    const tempt = OrderedMap({
      conditionType,
      ...objValue,
      property,
      operator: getOperatorByValue(`${property.dataType}-${item.operator}`),
      operators: getOperatorByProperty(property),
      dataType: property.dataType,
      backup: item,
    });

    return tempt;
    // eslint-disable-next-line no-else-return
  } else {
    property = mapInfo[keyLookup];
    if (property !== undefined) {
      property.dataType = safeParse(property.dataType, item.dataType);
      const objValue = buildValueConditionFromAPI(item, property);
      const tempt = OrderedMap({
        conditionType,
        ...objValue,
        property,
        statusItemCode: property.statusItemCode,
        operator: getOperatorByValue(`${property.dataType}-${item.operator}`),
        operators: getOperatorByProperty(property),
        dataType: property.dataType,
        backup: item,
      });

      return tempt;
    }
  }
  return null;
}

export function toComponentAudienceAttrs(objRules, data) {
  let conditions = OrderedMap({});
  const rules = [];
  const ruleOR = safeParse(objRules.OR, []);
  ruleOR.forEach(itemOr => {
    const ruleAND = safeParse(itemOr.AND, []);
    if (ruleAND.length > 0) {
      rules.push(ruleAND);
    }
  });
  if (rules.length === 0) {
    return conditions;
  }

  rules.forEach(rule => {
    let condition = OrderedMap({});
    rule.forEach(item => {
      const conditionType = data.map.conditionType.comp_attr;
      const tempt = toConditionCompPropUI(
        item,
        conditionType,
        data.map.itemAttribute,
        // data.info.itemAttribute,
        {},
      );

      if (tempt !== null) {
        condition = condition.set(generateKey(), tempt);
      }
    });
    if (condition.size > 0) {
      conditions = conditions.setIn([generateKey()], condition);
    }
  });

  return conditions;
}

export const toEntryFE = (data, type) => {
  if (type === 'eventProperties') {
    return mapItemEventPropertyForStory(data);
  }
};

export function getLookupEventAttributesWithProperties(refine) {
  const res = [];
  //   refine.forEach(item => {
  //     const rules = item.get('backup');
  //     res.push({
  //       eventPropertyName: rules.property_name,
  //       itemTypeId: rules.item_type_id,
  //     });
  //   });
  const ruleOR = safeParse(refine.OR, []);

  if (ruleOR.length > 0) {
    ruleOR.forEach(itemRuleOR => {
      const ruleAND = safeParse(itemRuleOR.AND, []);
      if (ruleAND.length > 0) {
        ruleAND.forEach(item => {
          res.push({
            eventPropertyName: item.column,
            itemTypeId: item.itemTypeId,
          });
        });
      }
    });
  }

  return res;
}

export function toRefinePropertiesUI(
  objRules,
  mapItem,
  mapInfoEventProperty,
  eventDetail,
) {
  // console.log('mapItem, mapInfoEventProperty', mapItem, mapInfoEventProperty);
  let conditions = OrderedMap({});

  const rules = [];
  const ruleOR = safeParse(objRules.OR, []);
  ruleOR.forEach(itemOr => {
    const ruleAND = safeParse(itemOr.AND, []);
    if (ruleAND.length > 0) {
      rules.push(ruleAND);
    }
  });

  if (rules.length === 0) {
    return conditions;
  }

  rules.forEach(rule => {
    let condition = OrderedMap({});
    rule.forEach(item => {
      let property = mapItem[`${item.column}-${safeParse(item.itemTypeId, 0)}`];
      if (property === undefined) {
        property =
          mapInfoEventProperty[
            `${item.column}-${safeParse(item.itemTypeId, 0)}`
          ];
      }
      if (property !== undefined) {
        const objValue = buildValueConditionFromAPI(item, property);
        const tempt = OrderedMap({
          ...objValue,
          property: {
            ...property,
            eventTrackingCode: eventDetail.eventTrackingCode,
          },
          statusItemCode: property.statusItemCode,
          operator: getOperatorByValue(`${property.dataType}-${item.operator}`),
          operators: getOperatorByProperty(property),
          dataType: property.dataType,
          extendValue: item.extendValue,
          backup: item,
        });
        if (tempt !== null) {
          condition = condition.set(generateKey(), tempt);
        }
      }
    });
    if (condition.size > 0) {
      conditions = conditions.setIn([generateKey()], condition);
    }
  });
  return conditions;
}

export function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

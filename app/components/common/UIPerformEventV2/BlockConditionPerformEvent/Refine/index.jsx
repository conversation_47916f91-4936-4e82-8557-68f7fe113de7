/* eslint-disable react/no-array-index-key */
/* eslint-disable react/prop-types */
import React, { memo, useCallback, useEffect, useMemo } from 'react';
import { isEmpty, isEqual, pick } from 'lodash';
import { useSelector } from 'react-redux';

// Selectors
import { makeSelectTriggerEventBaseInfo } from '../../../../../modules/Dashboard/MarketingHub/Journey/Create/selectors';

import { Icon, useDeepCompareEffect } from '@antscorp/antsomi-ui';
import {
  UIWrapperDisable as WrapperDisable,
  UILoading as Loading,
} from '@xlab-team/ui-components';

import HavingCondition from '../../HavingCondition';
import { useFetchDataByEvent } from './useFetchDataByEvent';
import Block from './Block';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import {
  DivLoading,
  DataSourceBlockContent,
  WrapperLabelRFM,
  ButtonWithIcon,
} from '../../styled';
import { filterSelectAchirve } from './utils';
import { extracAttribute } from '../../../../../containers/Segment/Content/Condition/utils';
import { NODE_TYPE } from '../../../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/constant';

const labelWhere = getTranslateMessage(TRANSLATE_KEY._TITL_WHERE, 'Where');
const labelAnd = getTranslateMessage(TRANSLATE_KEY._TITL_AND, 'And');
const labelRefineAttr = getTranslateMessage(
  TRANSLATE_KEY._ACT_REFINE_ATTRIBUTE,
  'Refine by attribute',
);

const Refine = props => {
  const {
    refineWithProperties,
    groupIndex,
    itemIndex,
    version,
    eventValue,
    sourcesSelected,
    isRFM = false,
    isAttributeBo = false,
    isStyledRFM = false,
    limit = Infinity,
    triggerInfo = {},
    useHavingCondition,
    isInitHavingCondition,
    havingAttributes,
    eventPropertyService,
    isUsingJourneyTemplate,
    onChangeHavingAttribute,
    onInitHavingCondition,
  } = props;

  const { type: triggerType } = triggerInfo;

  const triggerEventInfo = useSelector(
    makeSelectTriggerEventBaseInfo(props.moduleConfig),
  );
  const isEventBase = triggerType === NODE_TYPE.EVENT_BASED;
  const waitEventInfo = useMemo(
    () => pick(eventValue, ['eventActionId', 'eventCategoryId']),
    [eventValue],
  );

  let isDisable = true;
  if (eventValue !== null && typeof eventValue === 'object') {
    if (Object.keys(eventValue).length > 0) {
      isDisable = false;
    }
  }

  let isInit = false;
  let backup = {};
  if (refineWithProperties.size > 0) {
    isInit = refineWithProperties.first().get('isInit');
    backup = refineWithProperties.first().get('backup');
  }

  const { group, isLoading, conditions } = useFetchDataByEvent(
    eventValue,
    isInit,
    backup,
    refineWithProperties,
    props.moduleConfig,
    sourcesSelected,
    isAttributeBo,
    eventPropertyService,
    isUsingJourneyTemplate,
  );

  const changeRefineProperties = data => {
    props.callback('PERF_EVENT_CHANGE_REFINE_PROPERTIES', data);
  };

  useEffect(() => {
    if (isLoading === false) {
      changeRefineProperties({
        type: 'INIT',
        data: {
          groupIndex,
          itemIndex,
          version,
          conditions,
        },
      });
    }
  }, [isLoading]);

  // Init having attributes
  useDeepCompareEffect(() => {
    if (!useHavingCondition || !isEventBase) return;

    if (isInitHavingCondition && isEmpty(havingAttributes) && group?.map) {
      const initAttributeIdKey = 'user_id-0';
      const initAttribute = group?.map[initAttributeIdKey];

      if (initAttribute && groupIndex && itemIndex) {
        const initExtracted = extracAttribute(initAttribute);

        onInitHavingCondition({
          groupIndex,
          itemIndex,
          initAttribute: {
            waitEvent: {
              ...initExtracted,
              eventActionId: waitEventInfo?.eventActionId,
              eventCategoryId: waitEventInfo?.eventCategoryId,
            },
            triggerEvent: {
              ...initExtracted,
              eventActionId: triggerEventInfo?.eventActionId,
              eventCategoryId: triggerEventInfo?.eventCategoryId,
            },
          },
        });
      }
    }
  }, [
    isEventBase,
    triggerEventInfo,
    waitEventInfo,
    useHavingCondition,
    havingAttributes,
    isInitHavingCondition,
    groupIndex,
    itemIndex,
    group?.map,
    onInitHavingCondition,
  ]);

  const callback = (type, data) => {
    changeRefineProperties({ version, type, data });
    // props.changeRefineProperties({ version, type, data });
    props.callback('UPDATE_CONDITIONS', data);
  };

  const addRefineProperties = () => {
    const data = {
      groupIndex: props.groupIndex,
      itemIndex: props.itemIndex,
    };
    changeRefineProperties({ version, type: 'ADD_ITEM', data });

    // props.changeRefineProperties({ version, type: 'ADD_ITEM', data });
  };

  const showRefineWithProperties = useCallback(() => {
    const result = [];
    // const refineWithProperties = props.item.get('refineWithProperties');
    const firstKey = refineWithProperties.keySeq().first();

    // let listEventProperty = [];
    // const eventSchema = safeParse(props.item.get('property'), null);
    // if (eventSchema !== null) {
    //   const mapEventProperty = safeParse(group.map[eventSchema.value], null);
    //   if (mapEventProperty !== null) {
    //     listEventProperty = safeParse(mapEventProperty.list, []);
    //   }
    // }
    refineWithProperties.forEach((itemRefine, key) => {
      if (itemRefine.get('isInit') !== true) {
        result.push(
          <Block
            first={firstKey === key}
            moduleConfig={props.moduleConfig}
            key={`item-${groupIndex}-${key}`}
            options={isRFM ? filterSelectAchirve(group.list) : group.list}
            groupIndex={groupIndex}
            itemIndex={itemIndex}
            eventValue={eventValue}
            refineIndex={key}
            item={itemRefine}
            callback={callback}
            title={firstKey === key ? labelWhere : labelAnd}
            isViewMode={props.isViewMode}
            isRFM={isRFM}
            sourceTags={props.sourceTags}
            isUsingCustomerJourney={props.isUsingCustomerJourney}
            // operatorLabel={props.operatorLabel}
          />,
        );
      }
    });
    return result;
  }, [
    group.list,
    callback,
    props.moduleConfig,
    groupIndex,
    itemIndex,
    props.isViewMode,
    eventValue,
    refineWithProperties,
    isRFM,
    props.sourceTags,
    props.isUsingCustomerJourney,
  ]);

  const showTitleRefine = () => {
    const result = [];

    if (refineWithProperties.size === 0) {
      result.push(labelWhere);
    } else if (!props.isViewMode) {
      result.push(labelAnd);
    }
    return result;
  };

  if (props.hidden) {
    return null;
  }

  if (isLoading)
    return (
      <DivLoading>
        <Loading isLoading={isLoading} size={20} />
      </DivLoading>
    );

  if (props.isViewMode && refineWithProperties.size === 0) {
    if (useHavingCondition && isEventBase) {
      return (
        <HavingCondition
          isViewMode={props.isViewMode}
          waitEventInfo={waitEventInfo}
          triggerEventInfo={triggerEventInfo}
          selected={havingAttributes}
          group={group}
          mapEventSchema={props.mapEventSchema}
          onChange={onChangeHavingAttribute}
        />
      );
    }

    return null;
  }

  return (
    <>
      {isLoading === false && showRefineWithProperties()}

      {!props.isViewMode && refineWithProperties.size < limit && (
        <DataSourceBlockContent isRFM={isRFM}>
          {isRFM ? (
            <WrapperLabelRFM>{showTitleRefine()}</WrapperLabelRFM>
          ) : (
            <div className="title">
              <div className="refine-by-attr">{showTitleRefine()}</div>
            </div>
          )}
          <div className="content">
            <WrapperDisable
              disabled={isDisable || props.disabledEventConditions}
            >
              <ButtonWithIcon
                onClick={addRefineProperties}
                disabled={refineWithProperties.size >= 20}
                icon={<Icon type="icon-ants-add" />}
              >
                {labelRefineAttr}
              </ButtonWithIcon>
            </WrapperDisable>
          </div>
        </DataSourceBlockContent>
      )}

      {useHavingCondition && isEventBase ? (
        <HavingCondition
          isViewMode={props.isViewMode}
          selected={havingAttributes}
          waitEventInfo={waitEventInfo}
          triggerEventInfo={triggerEventInfo}
          group={group}
          mapEventSchema={props.mapEventSchema}
          onChange={onChangeHavingAttribute}
        />
      ) : null}
    </>
  );
};

export default memo(Refine, isEqual);

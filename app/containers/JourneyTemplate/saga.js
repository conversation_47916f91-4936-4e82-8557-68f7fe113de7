/* eslint-disable no-param-reassign */
/* eslint-disable no-plusplus */
/* eslint-disable indent */
/* eslint-disable func-names */
/* eslint-disable object-shorthand */
import produce from 'immer';
import {
  get,
  isArray,
  isEmpty,
  isFunction,
  map,
  pick,
  set,
  uniqWith,
} from 'lodash';
import isEqual from 'react-fast-compare';
import { call, put, select, takeLatest } from 'redux-saga/effects';
import { addMessageToQueue } from '../../utils/web/queue';
import { iterateObject, safeParseArray } from '../../utils/web/utils';
import {
  changeActiveObj,
  changeIsApplying,
  nextObject,
  updateApplyState,
  updateJourney,
  updateObj,
  updateVerification,
} from './actions';
import {
  ACTION_TYPES,
  JOURNEY_OBJECT_TYPE,
  MODE,
  OBJECT_TYPE,
} from './constant';
import {
  selectActiveObjDetail,
  selectActiveObject,
  selectAllowApplyJT,
  selectAllowDirection,
  selectJourney,
  selectJourneyTemplate,
  selectMode,
  selectObjects,
  selectOrderedObjects,
  selectVerification,
} from './selector';
import { serializeJourneyTemplate } from './utils';
import {
  findObject,
  getBasicObject,
  getNextIndex,
  getObjectsUseSameDOAttr,
  getPrevIndex,
  getRelatedObjects,
  isEqualObject,
  objectIsUseSameBOAttr,
} from './utils/helper';
import { toStorySettings } from './utils/journeyObjects';
import { JourneyObject, createObjectService } from './utils/service';
import { addNotification } from '../../redux/actions';

const PATH =
  'modules/Dashboard/MarketingHub/Journey/Create/_UI/JourneyTemplate/saga';

const errorNotify = error => ({
  id: 'error',
  message: error.message || 'Something went wrong',
  timeout: 5000,
  timestamp: new Date().getTime(),
  type: 'danger',
});

export default function* journeyTemplateMiddleware() {
  yield takeLatest(ACTION_TYPES.init, handleInit);
  yield takeLatest(ACTION_TYPES.replaceWithExisting, handleReplaceWithExisting);
  yield takeLatest(ACTION_TYPES.createObject, handleCreateObject);
  yield takeLatest(ACTION_TYPES.applyJT, handleApplyJT);
  yield takeLatest(ACTION_TYPES.nextObject, handleNextObject);
  yield takeLatest(ACTION_TYPES.prevObject, handlePrevObject);
}

function* handleInit(action) {
  const { data, mode } = action.payload;

  if (mode === MODE.Save) {
    const serializeResult = yield call(serializeJourneyTemplate, {
      ...data,
      onError: function*(error) {
        yield put(addNotification(errorNotify(error)));
      },
    });

    if (serializeResult) {
      const {
        objects = [],
        objectSettings = [],
        workflowSetting,
      } = serializeResult;

      yield put(
        updateVerification({
          objects,
          templateObjSettings: objectSettings,
        }),
      );

      const journey = yield select(selectJourney);

      yield put(
        updateJourney({
          ...journey,
          workflow_setting: workflowSetting,
        }),
      );
    }
  }

  if (mode === MODE.Use) {
    const { configObject } = data;

    const { templateObjSettings, objects } = configObject;

    yield put(
      updateVerification({
        templateObjSettings,
        objects: objects.map(obj => ({
          ...obj,
          isCreating: false,
          isLoadExisting: false,
        })),
      }),
    );

    yield call(handleVerifyObjects, {
      payload: {
        verifyObjs: templateObjSettings,
      },
    });
  }

  yield put({
    type: ACTION_TYPES.initDone,
  });
}

function* handleVerifyObjects(args) {
  try {
    const { verifyObjs } = args.payload;

    const { objects, templateObjSettings } = yield select(selectVerification);

    if (isEmpty(verifyObjs) || isEmpty(templateObjSettings)) return;

    const verifiedObjects = yield call(JourneyObject.validateTemplateObjects, {
      objects,
      verifyObjs,
      templateObjSettings,
    });

    const HELPER_KEY = 'object';

    const updatedObjs = produce(objects, draft => {
      iterateObject(
        {
          [HELPER_KEY]: objects,
        },
        (value, pathValue) => {
          verifiedObjects.forEach(verifiedObj => {
            const { verify: newVerify } = verifiedObj;

            if (!isEqualObject(value, verifiedObj)) return;

            const path = pathValue.slice(`${HELPER_KEY}.`.length);

            if (get(draft, path)) {
              const verifyPath = `${path}.verify`;

              const currentVerify = get(draft, verifyPath);

              if (currentVerify?.isExist && !newVerify.isExist) {
                set(draft, verifyPath, {
                  ...currentVerify,
                  dataReplace: null,
                  isExist: false,
                });

                return;
              }

              set(draft, verifyPath, verifiedObj.verify);
            }
          });
        },
      );
    });

    yield put(
      updateVerification({
        objects: updatedObjs,
      }),
    );
  } catch (error) {
    addMessageToQueue({
      error,
      args,
      func: 'handleVerifyObjects',
    });
  }
}

function* handleReplaceWithExisting(action) {
  const { activeObj } = yield select(selectVerification);

  let { existingObj } = action.payload;

  const replacedObj = action.payload.replacedObj || activeObj;

  try {
    yield put(updateObj(replacedObj, { isLoadExisting: true }));

    const { objectSettings = [] } = yield call(
      JourneyObject.getJourneyObjectsDetail,
      {
        objects: [existingObj],
        withSubObjs: false,
      },
    );

    existingObj = findObject(existingObj, objectSettings);

    if (!existingObj.isExist) {
      throw new Error();
    }

    existingObj = JourneyObject.HelperFn.mappingDetail(existingObj);

    const tempVerify = pick(existingObj, ['isExist', 'type', 'settings']);

    yield put(
      updateObj(replacedObj, {
        verify: tempVerify,
        errors: [],
      }),
    );

    const { objects } = yield select(selectVerification);

    const objsNeedReVerify = [replacedObj];

    switch (replacedObj.type) {
      case OBJECT_TYPE.dataObjectAttr:
      case OBJECT_TYPE.eventAttribute: {
        const { itemTypeId } = replacedObj;

        if (!itemTypeId) break;

        const updatedObjects = produce(objects, draft => {
          objects.forEach((object, idx) => {
            if (isEqualObject(replacedObj, object)) return;

            if (objectIsUseSameBOAttr(replacedObj, object)) {
              objsNeedReVerify.push(object);

              draft[idx].verify = tempVerify;
            }
          });
        });

        yield put(
          updateVerification({
            objects: updatedObjects,
          }),
        );
        break;
      }
      default:
        objsNeedReVerify.push(...getRelatedObjects(replacedObj, objects));
        break;
    }

    yield call(handleVerifyObjects, {
      payload: {
        verifyObjs: map(objsNeedReVerify, getBasicObject),
      },
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);

    addMessageToQueue({
      func: 'handleReplaceWithExisting',
      action,
    });

    yield put(
      addNotification({
        id: 'error',
        message: 'Failed to replace object',
        type: 'danger',
      }),
    );
  } finally {
    yield put(updateObj(replacedObj, { isLoadExisting: false }));
  }
}

function* handleCreateObject(action) {
  const activeObj = yield select(selectActiveObjDetail);

  let { object: creatingObj = activeObj } = action.payload;

  try {
    const { templateObjSettings, objects } = yield select(selectVerification);

    const { options = {} } = action.payload;

    const { onSuccess } = options;

    creatingObj = findObject(creatingObj, objects);

    const { settings } = findObject(creatingObj, templateObjSettings) || {};

    if (!creatingObj || !settings) return;

    creatingObj.objects.forEach(({ pathConfig, verify }) => {
      const { dataReplace } = verify;

      pathConfig.forEach(({ path, value: valueKey }) => {
        const replaceValue = dataReplace[valueKey];

        set(settings, path, replaceValue);
      });
    });

    yield put(updateObj(creatingObj, { isCreating: true }));

    const { success, ...rest } = yield call(createObjectService, {
      newObject: creatingObj,
      settings,
    });

    if (success) {
      const objsNeedReVerify = [creatingObj];

      switch (creatingObj.type) {
        case JOURNEY_OBJECT_TYPE.dataObject: {
          const relatedObjs = getRelatedObjects(creatingObj, objects);

          relatedObjs.forEach(obj => {
            if (obj.type === OBJECT_TYPE.dataObjectAttr) {
              objsNeedReVerify.push(obj);
              objsNeedReVerify.push(...getObjectsUseSameDOAttr(obj, objects));
            }
          });

          break;
        }
        case JOURNEY_OBJECT_TYPE.dataObjectAttr: {
          objsNeedReVerify.push(
            ...getObjectsUseSameDOAttr(creatingObj, objects),
          );
          break;
        }
        default:
          break;
      }

      yield call(handleVerifyObjects, {
        payload: { verifyObjs: objsNeedReVerify },
      });

      if (isFunction(onSuccess)) {
        onSuccess(rest.res);
      }

      yield put(nextObject());

      return;
    }

    const { error = [] } = rest;

    yield call(handleAddErrors, {
      payload: {
        object: creatingObj,
        errors: isArray(error) ? error : [error],
      },
    });

    yield put(
      addNotification({
        id: 'error',
        message: get(rest, 'error.message') || 'Create fail',
        type: 'danger',
      }),
    );
  } catch (error) {
    addMessageToQueue({
      func: 'handleCreateObject',
      error,
      path: PATH,
    });
  } finally {
    yield put(updateObj(creatingObj, { isCreating: false }));
  }
}

function* handleApplyJT() {
  const mode = yield select(selectMode);
  const allowApplyJT = yield select(selectAllowApplyJT);

  if (mode !== MODE.Use && !allowApplyJT) return;

  try {
    yield put(changeIsApplying(true));

    const verification = yield select(selectVerification);
    const journeyTemplate = yield select(selectJourneyTemplate);

    const { objects, templateObjSettings } = verification;

    const { result: storySettings } = yield call(toStorySettings, {
      objects,
      templateObjSettings,
      journeyTemplate,
    });

    yield put(
      updateApplyState({
        isApply: true,
        data: storySettings,
      }),
    );

    yield put(changeIsApplying(false));
  } catch (error) {
    addMessageToQueue({
      func: 'handleApplyJT',
      error,
      path: PATH,
    });
  }
}

export function* handleNextObject() {
  const { orderedObjects, activeObjIdx } = yield select(selectOrderedObjects);

  const { allow, idx } = yield select(selectAllowDirection(getNextIndex));

  if (!allow) return;

  if (activeObjIdx < orderedObjects.length - 1) {
    yield put(changeActiveObj(orderedObjects[idx]));
  }
}

export function* handlePrevObject() {
  const { orderedObjects, activeObjIdx } = yield select(selectOrderedObjects);

  const { allow, idx } = yield select(selectAllowDirection(getPrevIndex));

  if (!allow) return;

  if (activeObjIdx > 0) {
    yield put(changeActiveObj(orderedObjects[idx]));
  }
}

export function* handleAddErrors(action) {
  const activeObj = yield select(selectActiveObject);
  const objects = yield select(selectObjects);

  const { errors, object } = action.payload;

  const updatedObject =
    objects.find(checkingObj => isEqualObject(checkingObj, object)) ||
    activeObj;

  const temp = [...safeParseArray(updatedObject.errors)];

  if (isArray(errors)) {
    temp.push(...errors);
  } else {
    temp.push(errors);
  }

  yield put(
    updateObj(updatedObject, {
      errors: uniqWith(temp, isEqual),
    }),
  );
}

/* eslint-disable no-cond-assign */
/* eslint-disable camelcase */
/* eslint-disable indent */
import { every, get, isEmpty, uniqWith } from 'lodash';
import isEqual from 'react-fast-compare';
import { SYSTEM_BO } from '../../../utils/constants';
import { OBJECT_TYPE } from '../constant';
import { concatPathToListObj } from './helper';

export const getObjectsFromTemplateData = args => {
  const { data, keyName = '' } = args;

  let result = [];

  const { properties, template_setting } = data;

  if (!properties || !template_setting) return [];

  const { journeySettings } = properties;

  try {
    result.push(
      ...getObjectsFromProperties({
        properties,
        templateSettings: template_setting,
      }),
    );

    result.push(
      ...getObjectsFromTemplateSettings({
        templateSettings: template_setting,
        journeySettings,
      }),
    );
  } catch (error) {
    return [];
  }

  result = concatPathToListObj(result, keyName);

  return uniqWith(result, isEqual);
};

export const getObjectsFromProperties = args => {
  const result = [];

  const { properties, keyName = 'properties', templateSettings } = args;

  const { journeySettings, viewPages } = properties;

  if (journeySettings?.triggerEvent?.eventActionId) {
    const { eventActionId, eventCategoryId } = journeySettings.triggerEvent;

    const path = 'journeySettings.triggerEvent';

    result.push({
      type: OBJECT_TYPE.event,
      eventActionId,
      eventCategoryId,
      pathConfig: [
        {
          path: `${path}.eventActionId`,
          value: 'eventActionId',
        },
        {
          path: `${path}.eventCategoryId`,
          value: 'eventCategoryId',
        },
        {
          path: `${path}.insightPropertyIds`,
          value: 'insightPropertyIds',
        },
      ],
    });
  }

  result.push(
    ...getObjectsFromViews({
      views: viewPages,
      templateSettings,
      journeySettings,
    }),
  );

  result.push(
    ...getObjectsFromContentSources({
      contentSources: properties.contentSources.groups,
      keyName: 'contentSources.groups',
    }),
  );

  return concatPathToListObj(result, keyName);
};

export const getObjectsFromViews = args => {
  const {
    keyName = 'viewPages',
    views,
    templateSettings,
    journeySettings,
  } = args;

  if (!views || !templateSettings || !journeySettings) return [];

  const result = [];

  views.forEach((viewPage, idx) => {
    const { html } = viewPage;

    const path = `${keyName}.${idx}`;

    const objsFromHTML = getObjectsFromHTMLContent({
      html,
      journeySettings,
      templateSettings,
      keyName: `${path}.html`,
    });

    result.push(...objsFromHTML);

    result.push(
      ...getObjectsFromBlocks({
        blocks: viewPage.blocks,
        keyName: `${path}.blocks`,
        journeySettings,
      }),
    );
  });

  return result;
};

export const getObjectsFromBlocks = args => {
  const { blocks, keyName = 'blocks', journeySettings } = args;

  if (!blocks) return [];

  const result = [];

  Object.entries(blocks).forEach(([blockKey, block]) => {
    const path = `${keyName}.${blockKey}`;

    result.push(
      ...getObjectsFromBlock({
        block,
        keyName: path,
        journeySettings,
      }),
    );
  });

  return result;
};

const addObjectsFromDynDict = ({ journeySettings, dynamicDict, keyName }) => {
  if (isEmpty(dynamicDict)) return [];

  const result = [];

  Object.entries(dynamicDict).forEach(([dynFieldKey, dynField]) => {
    result.push(
      ...getObjectsFromDynField({
        field: dynField,
        keyName: `${keyName}.${dynFieldKey}`,
        journeySettings,
      }),
    );
  });

  return result;
};

export const getObjectsFromBlock = args => {
  const { block, keyName = 'block', journeySettings } = args;

  if (!block) return [];

  const result = [];

  result.push(
    ...getObjectsFromDisplayCondition({
      displayCondition: block.settings?.blockStylesSettings?.displayCondition,
      keyName: `${keyName}.settings.blockStylesSettings.displayCondition`,
      journeySettings,
    }),
  );

  const { type } = block;

  switch (type) {
    case 'button':
    case 'rating':
    case 'video':
    case 'optin_fields':
    case 'image': {
      const { dynamic } = block.settings;

      result.push(
        ...addObjectsFromDynDict({
          dynamicDict: dynamic,
          keyName: `${keyName}.settings.dynamic`,
          journeySettings,
        }),
      );
      break;
    }
    case 'yes/no': {
      const { yesDynamic, noDynamic } = block.settings;

      result.push(
        ...addObjectsFromDynDict({
          dynamicDict: yesDynamic,
          keyName: `${keyName}.settings.yesDynamic`,
          journeySettings,
        }),

        ...addObjectsFromDynDict({
          dynamicDict: noDynamic,
          keyName: `${keyName}.settings.noDynamic`,
          journeySettings,
        }),
      );

      break;
    }
    case 'text': {
      const { dynamic, link } = block.settings;

      result.push(
        ...addObjectsFromDynDict({
          dynamicDict: dynamic?.data,
          keyName: `${keyName}.settings.dynamic.data`,
          journeySettings,
        }),

        ...addObjectsFromDynDict({
          dynamicDict: link?.data,
          keyName: `${keyName}.settings.link.data`,
          journeySettings,
        }),
      );
      break;
    }
    case 'table': {
      const { columns, filters, boId } = block.settings;

      result.push({
        type: OBJECT_TYPE.dataObject,
        itemTypeId: boId,
        pathConfig: [
          {
            path: `${keyName}.settings.boId`,
            value: 'itemTypeId',
          },
        ],
      });

      columns.forEach((col, idx) => {
        if (!col.value) return;

        result.push({
          type: OBJECT_TYPE.dataObjectAttr,
          itemTypeId: boId,
          itemPropertyName: col.value,
          pathConfig: [
            {
              path: `${keyName}.settings.columns.${idx}.value`,
              value: col.value,
            },
          ],
        });
      });

      result.push(
        ...getObjectsFromFilters({
          filters,
          filtersLabel: `${keyName}.settings.filters`,
          itemTypeId: boId,
        }),
      );

      break;
    }
    case 'coupon_wheel':
    case 'surprise-treasure-hunt':
    case 'shake-and-win': {
      const { sections } = block.settings;

      sections.forEach((section, idx) => {
        const { pool, couponCode } = section;

        if (pool && couponCode) {
          result.push({
            type: OBJECT_TYPE.promotionPool,
            poolCode: section.couponCode,
            pathConfig: [
              {
                path: `${keyName}.settings.sections.${idx}.couponCode`,
                value: 'poolCode',
              },
            ],
          });
        }
      });
      break;
    }
    default:
      break;
  }

  return result;
};

export const getObjectsFromDisplayCondition = args => {
  const {
    displayCondition,
    keyName = 'displayCondition',
    journeySettings,
  } = args;

  if (!displayCondition) return [];

  const result = [];

  const { event_metadata } = displayCondition;

  result.push(
    ...getObjectsFromDynField({
      field: displayCondition,
      keyName,
      journeySettings,
    }),
  );

  result.push(
    ...getObjectsFromEventMetadata({
      event_metadata,
      keyName: `${keyName}.event_metadata`,
    }),
  );

  return result;
};

export const getObjectsFromTemplateSettings = args => {
  const {
    keyName = 'template_setting',
    templateSettings,
    journeySettings,
  } = args;

  const result = [];

  result.push(
    ...getObjectsFromContentSources({
      contentSources: templateSettings.contentSources,
    }),

    ...getObjectsFromElActions({
      actions: templateSettings.actions,
      journeySettings,
    }),

    ...getObjectsFromBOTable({
      boTableSettings: templateSettings.boTableSettings,
    }),
  );

  return uniqWith(concatPathToListObj(result, keyName), isEqual);
};

export const getObjectsFromBOTable = ({
  boTableSettings,
  keyName = 'boTableSettings',
}) => {
  if (!boTableSettings) return [];

  const result = [];

  Object.entries(boTableSettings).forEach(([tableKey, tableSetting]) => {
    const path = `${keyName}.${tableKey}`;

    const { filters, columns, itemTypeId } = tableSetting;

    result.push({
      type: OBJECT_TYPE.dataObject,
      itemTypeId,
      pathConfig: [
        {
          path: `${path}.itemTypeId`,
          value: 'itemTypeId',
        },
      ],
    });

    const objectsFromFitlers = getObjectsFromFilters({
      filters,
      itemTypeId,
    });

    result.push(...concatPathToListObj(objectsFromFitlers, path));

    if (Array.isArray(columns)) {
      columns.forEach((col, idx) => {
        const { value: itemPropertyName } = col;

        if (!itemPropertyName) return;

        result.push({
          type: OBJECT_TYPE.dataObjectAttr,
          itemTypeId,
          itemPropertyName,
          pathConfig: [
            {
              path: `${path}.columns.${idx}.value`,
              value: 'itemPropertyName',
            },
          ],
        });
      });
    }
  });

  return result;
};

export const getObjectsFromDynField = args => {
  const { keyName = 'field', field, journeySettings = {} } = args;

  const { triggerType } = journeySettings;

  const result = [];

  const { type, mappingFields } = field;

  if (typeof type !== 'string') return [];

  switch (true) {
    case type.startsWith('visitor-attribute') ||
      type.startsWith('customer_attribute'): {
      const itemPropertyName = get(field, 'attribute.value');
      const itemTypeId = type.startsWith('visitor-attribute') ? -1007 : -1003;
      const pathPrefix = type.startsWith('visitor-attribute')
        ? 'visitor'
        : 'customer';

      const pathConfig = [
        {
          path: `${keyName}.attribute.value`,
          value: 'itemPropertyName',
        },
      ];

      if (mappingFields) {
        pathConfig.push({
          path: `${keyName}.mappingFields`,
          pattern: `${pathPrefix}.${itemPropertyName}`,
          value: `${pathPrefix}.{itemPropertyName}`,
        });
      }

      result.push(
        {
          type: OBJECT_TYPE.dataObject,
          itemTypeId,
          pathConfig: [],
        },
        {
          type: OBJECT_TYPE.dataObjectAttr,
          itemTypeId,
          itemPropertyName,
          pathConfig,
        },
      );
      break;
    }
    case type.startsWith('content-source'): {
      const itemPropertyName = get(field, 'attribute.value');
      const chunks = type.split('::');

      const itemTypeId = +chunks[2];
      const sourceKey = chunks[1];

      if (itemTypeId && itemPropertyName) {
        const pathConfig = [
          {
            path: `${keyName}.attribute.value`,
            value: 'itemPropertyName',
          },
          {
            path: `${keyName}.attribute.itemTypeName`,
            value: 'itemTypeName',
          },
          {
            path: `${keyName}.type`,
            pattern: `content-source::${sourceKey}::${itemTypeId}`,
            value: `content-source::${sourceKey}::{itemTypeId}`,
          },
        ];

        if (mappingFields) {
          pathConfig.push({
            path: `${keyName}.mappingFields`,
            pattern: `groups.${sourceKey}[${itemTypeId}].${itemPropertyName}`,
            value: `groups.${sourceKey}[{itemTypeId}].{itemPropertyName}`,
          });
        }

        result.push(
          {
            type: OBJECT_TYPE.dataObject,
            itemTypeId,
            pathConfig: [],
          },
          {
            type: OBJECT_TYPE.dataObjectAttr,
            itemTypeId,
            itemPropertyName,
            pathConfig,
          },
        );
      }
      break;
    }
    case type.startsWith('event-attribute') && triggerType === 'event_based': {
      const event = String(field.event);

      const eventActionId = +event.split(':')[0];
      const eventCategoryId = +event.split(':')[1];

      if (!eventActionId || !eventCategoryId) return [];

      const eventProps = { eventActionId, eventCategoryId };

      result.push({
        type: OBJECT_TYPE.event,
        ...eventProps,
        pathConfig: [
          {
            path: `${keyName}.event`,
            pattern: `${eventActionId}:${eventCategoryId}`,
            value: '{eventActionId}:{eventCategoryId}',
          },
          {
            path: `${keyName}.source`,
            value: 'insightPropertyIds',
          },
        ],
      });

      const itemTypeId = +field.attribute?.itemTypeId;

      if (itemTypeId) {
        const [itemTypeName, itemPropertyName] = String(
          field.attribute?.value,
        ).split('.');

        if (!itemPropertyName || !itemTypeName) break;

        const pathConfig = [
          {
            path: `${keyName}.attribute.value`,
            value: 'itemPropertyName',
          },
          {
            path: `${keyName}.attribute.propertyName`,
            value: 'itemPropertyName',
          },
          {
            path: `${keyName}.attribute.itemTypeId`,
            value: 'itemTypeId',
          },
          {
            path: `${keyName}.attribute.itemTypeName`,
            value: 'itemTypeName',
          },
          {
            path: `${keyName}.attribute.eventPropertySyntax`,
            value: 'eventPropertySyntax',
          },
        ];

        if (mappingFields) {
          pathConfig.push({
            path: `${keyName}.mappingFields`,
            pattern: `event.${itemTypeName}.${itemPropertyName}`,
            value: 'event.{itemTypeName}.{itemPropertyName}',
          });
        }

        result.push(
          {
            type: OBJECT_TYPE.dataObject,
            itemTypeId,
            pathConfig: [],
          },
          {
            type: OBJECT_TYPE.dataObjectAttr,
            itemTypeId,
            itemPropertyName,
            pathConfig: [],
          },
          {
            ...eventProps,
            type: OBJECT_TYPE.eventAttribute,
            itemPropertyName,
            itemTypeId,
            pathConfig,
          },
        );

        break;
      }

      const eventPropertyName = field.attribute?.value;

      if (!eventPropertyName) break;

      const pathConfig = [
        {
          path: `${keyName}.attribute.value`,
          value: 'eventPropertyName',
        },
        {
          path: `${keyName}.attribute.propertyName`,
          value: 'eventPropertyName',
        },
      ];

      if (mappingFields) {
        pathConfig.push({
          path: `${keyName}.mappingFields`,
          pattern: `event.${eventPropertyName}`,
          value: 'event.{eventPropertyName}',
        });
      }

      result.push({
        ...eventProps,
        type: OBJECT_TYPE.eventAttribute,
        eventPropertyName,
        pathConfig,
      });
      break;
    }
    case type.startsWith('promotion-code'): {
      const poolCode = field.pool;

      if (!poolCode) break;

      const pathConfig = [
        {
          path: `${keyName}.pool`,
          value: 'poolCode',
        },
      ];

      if (mappingFields) {
        pathConfig.push({
          path: `${keyName}.mappingFields`,
          pattern: `promotion_code.${poolCode}.pool_id`,
          value: 'promotion_code.{poolCode}.pool_id',
        });
      }

      result.push({
        type: OBJECT_TYPE.promotionPool,
        poolCode,
        pathConfig,
      });
      break;
    }
    default:
      break;
  }

  return result;
};

export const getObjectsFromElActions = args => {
  const result = [];

  const {
    actions,
    journeySettings,
    keyName: actionsKeyName = 'actions',
  } = args;

  actions.forEach((action, actionIdx) => {
    const { element_type } = action;

    switch (element_type) {
      case 'text': {
        const { dynamic } = action;

        if (!dynamic) break;

        const { textData, linkData } = dynamic;

        const handleAddObjects = ({ dynamicSettings, keyName }) => {
          if (!dynamicSettings) return;

          Object.entries(dynamicSettings).forEach(([key, value]) => {
            const path = `${actionsKeyName}.${actionIdx}.${keyName}.${key}`;

            //   console.log(JSON.stringify(actionIdx));
            //   console.log(JSON.stringify(action));
            //   console.log(JSON.stringify(journeySettings));

            result.push(
              ...getObjectsFromDynField({
                field: value,
                keyName: `${path}`,
                journeySettings,
              }),
            );
          });
        };

        handleAddObjects({
          dynamicSettings: textData,
          keyName: 'dynamic.textData',
        });

        handleAddObjects({
          dynamicSettings: linkData,
          keyName: 'dynamic.linkData',
        });
        break;
      }
      default:
        break;
    }
  });

  return result;
};

export const getObjectsFromContentSources = args => {
  const result = [];

  const { contentSources, keyName = 'contentSources' } = args;

  contentSources.forEach((contentSource, contentSourceIdx) => {
    const path = `${keyName}.${contentSourceIdx}`;

    const { itemTypeId, filters, itemTypeName } = contentSource;

    const doObject = {
      type: OBJECT_TYPE.dataObject,
      itemTypeId,
      pathConfig: [
        {
          path: `${path}.itemTypeId`,
          value: 'itemTypeId',
        },
        {
          path: `${path}.itemTypeName`,
          value: 'itemTypeName',
        },
      ],
    };

    const isBOProduct = SYSTEM_BO.Product.itemTypeId === itemTypeId;
    const isBOArticle = itemTypeName === 'article';

    if (isBOProduct || isBOArticle) {
      const settingFields = isBOProduct ? ['item_type_id'] : ['item_type_name'];

      doObject.pathConfig.push(
        {
          path: `${path}.fallback`,
          value: 'fallback',
          dataReplace: {
            fallback: 'hidden',
          },
          replaceCondition: [
            {
              key: 'Only-replace_onChange',
              args: { settingFields },
            },
          ],
        },
        {
          path: `${path}.filters`,
          value: 'filters',
          dataReplace: {
            filters: { OR: [] },
          },
          replaceCondition: [
            {
              key: 'Only-replace_onChange',
              args: { settingFields },
            },
          ],
        },
        {
          path: `${path}.ranking.algorithms.value`,
          value: 'value',
          dataReplace: {
            value: [
              {
                value: 'get_top',
                sort_by: 'last_updated',
                quantity: 5,
                sort_type: 'desc',
              },
            ],
          },
          replaceCondition: [
            {
              key: 'Only-replace_onChange',
              args: { settingFields },
            },
          ],
        },
      );
    }

    result.push(doObject);

    result.push(
      ...getObjectsFromFilters({
        itemTypeId,
        filters,
        filtersLabel: `${path}.filters`,
      }),
    );
  });

  return result;
};

export const getObjectsFromEventMetadata = args => {
  const { keyName = 'event_metadata', event_metadata } = args;

  const result = [];

  if (!event_metadata || isEmpty(event_metadata)) return [];

  // Cases: Display condition with content source, ...
  if (event_metadata.useBo && args.itemTypeId) {
    const { itemTypeId } = args;

    result.push(
      {
        type: OBJECT_TYPE.dataObject,
        itemTypeId,
        pathConfig: [],
      },
      {
        type: OBJECT_TYPE.dataObjectAttr,
        itemTypeId,
        itemPropertyName: event_metadata.field_code_bo,
        pathConfig: [
          {
            path: `${keyName}.field_code_bo`,
            value: 'itemPropertyName',
          },
        ],
      },
    );
  }

  if (!event_metadata.useBo) {
    const { event_action_id, event_category_id } = event_metadata;

    if (!event_action_id || !event_action_id) return [];

    const eventInfo = {
      eventActionId: event_action_id,
      eventCategoryId: event_category_id,
    };

    result.push({
      ...eventInfo,
      type: OBJECT_TYPE.event,
      pathConfig: [
        {
          path: `${keyName}.event_action_id`,
          value: 'eventActionId',
        },
        {
          path: `${keyName}.event_category_id`,
          value: 'eventCategoryId',
        },
        {
          path: `${keyName}.event_tracking_name`,
          value: 'eventTrackingName',
        },
        {
          path: `${keyName}.event_property_syntax`,
          value: 'eventPropertySyntax',
        },
        {
          path: `${keyName}.insight_property_ids`,
          value: 'insightPropertyIds',
        },
      ],
    });

    const { item_type_id } = event_metadata;

    if (item_type_id) {
      const { item_property_name } = event_metadata;

      result.push(
        {
          type: OBJECT_TYPE.dataObject,
          itemTypeId: item_type_id,
          pathConfig: [
            {
              path: `${keyName}.item_type_id`,
              value: 'itemTypeId',
            },
            {
              path: `${keyName}.item_type_name`,
              value: 'itemTypeName',
            },
          ],
        },
        {
          type: OBJECT_TYPE.dataObjectAttr,
          itemTypeId: item_type_id,
          itemPropertyName: item_property_name,
          pathConfig: [],
        },
        {
          ...eventInfo,
          type: OBJECT_TYPE.eventAttribute,
          itemTypeId: item_type_id,
          itemPropertyName: item_property_name,
          pathConfig: [
            {
              path: `${keyName}.item_property_name`,
              value: 'itemPropertyName',
            },
          ],
        },
      );
    } else {
      const { item_property_name } = event_metadata;

      result.push({
        ...eventInfo,
        type: OBJECT_TYPE.eventAttribute,
        eventPropertyName: item_property_name,
        pathConfig: [
          {
            path: `${keyName}.item_property_name`,
            value: 'eventPropertyName',
          },
        ],
      });
    }
  }

  return result;
};

export const getObjectsFromFilters = args => {
  const { itemTypeId, filters, filtersLabel = 'filters' } = args;

  if (!filters) return [];

  const result = [];

  loopFilters(filters, {
    eachCondition: (condition, conditionPath, _info) => {
      let path = `${filtersLabel}.${conditionPath}`;

      const {
        column,
        event_metadata,
        visitor_metadata,
        customer_metadata,
      } = condition;

      result.push({
        type: OBJECT_TYPE.dataObjectAttr,
        itemTypeId,
        itemPropertyName: column,
        pathConfig: [
          {
            path: `${path}.column`,
            value: 'itemPropertyName',
          },
        ],
      });

      if (event_metadata && !isEmpty(event_metadata)) {
        result.push(
          ...concatPathToListObj(
            getObjectsFromEventMetadata({
              event_metadata,
              keyName: `${path}.event_metadata`,
            }),
          ),
        );
      }

      const handleBOAttrMetadata = (metadata, keyName) => {
        const { item_type_id, item_property_name } = metadata;

        if (!item_type_id || !item_property_name) return;

        path = `${path}.${keyName}`;

        result.push(
          {
            type: OBJECT_TYPE.dataObject,
            itemTypeId: item_type_id,
            pathConfig: [],
          },
          {
            type: OBJECT_TYPE.dataObjectAttr,
            itemTypeId: item_type_id,
            itemPropertyName: item_property_name,
            pathConfig: [
              {
                path: `${path}.item_property_name`,
                value: 'itemPropertyName',
              },
              {
                path: `${path}.item_type_id`,
                value: 'itemTypeId',
              },
            ],
          },
        );
      };

      if (visitor_metadata && !isEmpty(visitor_metadata)) {
        handleBOAttrMetadata(visitor_metadata, 'visitor_metadata');
      }

      if (customer_metadata && !isEmpty(customer_metadata)) {
        handleBOAttrMetadata(customer_metadata, 'customer_metadata');
      }
    },
  });

  return result;
};

export const getObjectsFromRelationships = ({
  objectRelationships,
  keyName = 'objectRelationships',
}) => {
  const result = [];

  if (!Array.isArray(objectRelationships)) return [];

  const MAPPING = {
    // OBJECT_TYPE defined in media_template project
    //
    // OBJECT_TYPE = {
    //   ITEM_PROPERTY: 1,
    //   SEGMENT: 2,
    //   JOURNEY: 3,
    //   AM: 4,
    //   DATAFLOW: 5,
    //   DATASOURCE: 6,
    //   MEDIA_TEMPLATE: 7,
    // }

    1: OBJECT_TYPE.dataObjectAttr,
    2: OBJECT_TYPE.segment,
  };

  objectRelationships.forEach((object, idx) => {
    const path = `${keyName}.${idx}`;

    const type = MAPPING[object.object_type];

    switch (type) {
      case OBJECT_TYPE.segment: {
        result.push({
          type: OBJECT_TYPE.segment,
          segmentId: object.object_id,
          pathConfig: [
            {
              path: `${path}.object_id`,
              value: 'segmentId',
            },
          ],
        });
        break;
      }
      case OBJECT_TYPE.dataObjectAttr: {
        if (!object.object_id) return;

        result.push(
          {
            type: OBJECT_TYPE.dataObject,
            itemTypeId: object.object_id,
            pathConfig: [],
          },
          {
            type: OBJECT_TYPE.dataObjectAttr,
            itemTypeId: object.object_id,
            itemPropertyName: object.object_property_name,
            pathConfig: [
              {
                path: `${path}.object_id`,
                value: 'itemTypeId',
              },
              {
                path: `${path}.object_property_name`,
                value: 'itemPropertyName',
              },
            ],
          },
        );
        break;
      }
      default:
        break;
    }
  });

  return result;
};

export const getObjectsFromHTMLContent = args => {
  const result = [];

  const {
    html,
    journeySettings = {},
    keyName = 'html',
    templateSettings = {},
  } = args;

  const { triggerType, triggerEvent } = journeySettings;

  if (isEmpty(journeySettings)) return [];

  const loopDynTag = (content = html, callback) => {
    const REGEX_DETECT_TOKEN = /#\{([^}]+)\}/g;
    const REGEX_DETECT_TOKEN_SUPPORT = /\${([^}]+)\}/g;

    const dynRegex = new RegExp(
      `${REGEX_DETECT_TOKEN.source}|${REGEX_DETECT_TOKEN_SUPPORT.source}`,
      'g',
    );

    let match;

    while ((match = dynRegex.exec(content)) !== null) {
      const tagContent = match[1];

      callback({ tag: tagContent });
    }
  };

  const cleanUpTag = tag => {
    const REGEX_FORMAT_TOKEN = /format\("[^}]+"\)/g;
    const REGEX_FORMAT_TOKEN_SUPPORT = /format\(\\"[^}]+"\)/g;

    const displayFormatPattern = new RegExp(
      `.${REGEX_FORMAT_TOKEN.source}|.${REGEX_FORMAT_TOKEN_SUPPORT.source}`,
    );

    return tag.replace(displayFormatPattern, '');
  };

  loopDynTag(html, ({ tag = '' }) => {
    const chunks = (tag.split('||').at(0) || '').split('.');

    const type = chunks[0];

    switch (true) {
      case type.startsWith('customer') || type.startsWith('visitor'): {
        const itemTypeId = type.startsWith('customer') ? -1003 : -1007;
        const itemPropertyName = chunks[1];

        if (itemTypeId && itemPropertyName) {
          result.push(
            {
              type: OBJECT_TYPE.dataObject,
              itemTypeId,
              pathConfig: [],
            },
            {
              type: OBJECT_TYPE.dataObjectAttr,
              itemTypeId,
              itemPropertyName,
              pathConfig: [
                {
                  path: keyName,
                  value: tag.replace(itemPropertyName, '{itemPropertyName}'),
                  pattern: tag,
                },
              ],
            },
          );
        }
        break;
      }

      case type.startsWith('event') && triggerType === 'event_based': {
        const eventProps = {
          eventActionId: triggerEvent.eventActionId,
          eventCategoryId: triggerEvent.eventCategoryId,
        };

        result.push({
          type: OBJECT_TYPE.event,
          ...eventProps,
          pathConfig: [],
        });

        if (chunks.length < 2) break;

        const cleanedTag = cleanUpTag(tag);

        const itemPropertyName = cleanedTag.split('.').at(2);

        if (itemPropertyName) {
          const itemTypeName = cleanedTag.split('.').at(1);

          result.push(
            {
              type: OBJECT_TYPE.dataObject,
              itemTypeName,
              pathConfig: [],
            },
            {
              type: OBJECT_TYPE.dataObjectAttr,
              itemTypeName,
              itemPropertyName,
              pathConfig: [],
            },
            {
              ...eventProps,
              type: OBJECT_TYPE.eventAttribute,
              itemTypeName,
              itemPropertyName,
              pathConfig: [
                {
                  path: keyName,
                  value: tag.replace(
                    `${itemTypeName}.${itemPropertyName}`,
                    '{itemTypeName}.{itemPropertyName}',
                  ),
                  pattern: tag,
                },
              ],
            },
          );
          break;
        }

        const eventPropertyName = cleanedTag.split('.').at(1);

        result.push({
          ...eventProps,
          type: OBJECT_TYPE.eventAttribute,
          eventPropertyName,
          pathConfig: [
            {
              path: keyName,
              value: tag.replace(`${eventPropertyName}`, '{eventPropertyName}'),
              pattern: tag,
            },
          ],
        });
        break;
      }

      case type.startsWith('promotion_code'): {
        const poolCode = chunks[1];

        if (!poolCode) break;

        result.push({
          type: OBJECT_TYPE.promotionPool,
          poolCode,
          pathConfig: [
            {
              path: keyName,
              value: tag.replace(poolCode, '{poolCode}'),
              pattern: tag,
            },
          ],
        });
        break;
      }

      case tag.startsWith('groups'): {
        const { contentSources = [] } = templateSettings;

        const regex = /groups\.(\w+)\[(\d+)\]\.(\w+)/;

        const match = tag.match(regex);

        if (match) {
          const groupId = match.at(1);
          const itemPropertyName = match.at(3);

          const contentSource = contentSources.find(s => s.groupId === groupId);

          if (contentSource?.itemTypeId && itemPropertyName) {
            const { itemTypeId } = contentSource;

            result.push(
              {
                type: OBJECT_TYPE.dataObject,
                itemTypeId,
                pathConfig: [],
              },
              {
                type: OBJECT_TYPE.dataObjectAttr,
                itemTypeId,
                itemPropertyName,
                pathConfig: [
                  {
                    path: keyName,
                    value: tag.replace(itemPropertyName, '{itemPropertyName}'),
                    pattern: tag,
                  },
                ],
              },
            );
          }
        }
        break;
      }
      default:
        break;
    }
  });

  (function getObjectsFromTableContent() {
    const boTableSettings = get(templateSettings, 'boTableSettings');

    if (isEmpty(boTableSettings)) return;

    const regex = /#BEGIN_FOR_TABLE_(.*?)#(.*?)#END_FOR_TABLE_\1#/gs;

    const matches = [...html.matchAll(regex)];

    matches.forEach(match => {
      const tableKey = match.at(1);
      const content = match.at(2);

      if (!tableKey || !content) return;

      if (!boTableSettings[tableKey]?.itemTypeId) return;

      const tableSetting = boTableSettings[tableKey];

      const { itemTypeId } = tableSetting;

      result.push({
        type: OBJECT_TYPE.dataObject,
        itemTypeId,
        pathConfig: [],
      });

      loopDynTag(content, ({ tag }) => {
        let itemPropertyName = tag.split('.').at(1) || '';

        itemPropertyName = itemPropertyName.split('||').at(0) || '';
        itemPropertyName = itemPropertyName.trim();

        if (
          !itemPropertyName ||
          every(tableSetting.columns, col => col.value !== itemPropertyName)
        )
          return;

        const tableStart = `#BEGIN_FOR_TABLE_${tableKey}#`;
        const tableEnd = `#END_FOR_TABLE_${tableKey}#`;

        const replaceRegex = new RegExp(
          `${tableStart}([\\s\\S]*?)#{item\\.${itemPropertyName}([\\s\\S]*?)${tableEnd}`,
        );

        result.push({
          type: OBJECT_TYPE.dataObjectAttr,
          itemTypeId,
          itemPropertyName,
          pathConfig: [
            {
              path: keyName,
              value: `${tableStart}$1#{item.{itemPropertyName}}$2${tableEnd}`,
              regex: {
                source: replaceRegex.source,
                flags: replaceRegex.flags,
              },
            },
          ],
        });
      });
    });
  })();

  (function getObjectsFromDataWheel() {
    const regex = /data-wheel="([^"]*)"/g;
    const matches = [...html.matchAll(regex)];

    matches.forEach(match => {
      const dataWheelString = match[1].replace(/&quot;/g, '"');
      const dataWheelArray = JSON.parse(dataWheelString);

      if (!Array.isArray(dataWheelArray)) return;

      dataWheelArray.forEach(rewardItem => {
        if (!rewardItem?.poolId) return;

        const { poolId, code } = rewardItem;

        const genReg = (key, value, dataReplaceKey) => {
          const pattern = `data-wheel="([^"]*)${key}([^:]*):([^:]*)${value}([^"]*)"`;

          const reg = new RegExp(pattern);

          return {
            value: `data-wheel="$1${key}$2:$3{${dataReplaceKey}}$4"`,
            regex: { source: reg.source, flags: reg.flags },
          };
        };

        const pathConfig = [
          {
            path: keyName,
            ...genReg('poolId', poolId, 'poolId'),
          },
          {
            path: keyName,
            ...genReg('code', code, 'poolCode'),
          },
        ];

        result.push({
          type: OBJECT_TYPE.promotionPool,
          poolId,
          pathConfig,
        });
      });
    });
  })();

  return uniqWith(result, isEqual);
};

export function loopFilters(filters, opts) {
  const { eachCondition, filterCondition = () => true } = opts;

  if (!filters) return;

  const orObj = get(filters, 'OR') || [];

  orObj.forEach((orItem, orIndex) => {
    const andConditions = get(orItem, 'AND', []);

    if (!Array.isArray(andConditions)) return;

    andConditions.forEach((condition, andIndex) => {
      if (!filterCondition(condition)) return;

      const path = `OR.${orIndex}.AND.${andIndex}`;

      if (eachCondition) {
        eachCondition(condition, path, {
          and: andIndex,
          or: orIndex,
        });
      }
    });
  });
}

import { get, pick } from 'lodash';
import { FIELD_WITH_OBJ, OBJECT_TYPE } from '../constant';
import {
  eachJourneyNodeTags,
  eachJourneyTags,
  eachVariantsTags,
} from './helper';
import { loopFilters } from './mediatemplate';

export const getEventFromNodeTrigger = branch => {
  if (branch.actionType !== FIELD_WITH_OBJ.EVENT_BASED) return null;

  const event = get(branch, 'metadata.event');

  return pick(event, ['eventActionId', 'eventCategoryId']);
};

export const getEventFromPeformEvent = ({
  perfEvent,
  pathPrefix = 'perfEvent',
}) => {
  const pathConfig = [
    {
      path: `${pathPrefix}.eventActionId`,
      value: 'eventActionId',
    },
    {
      path: `${pathPrefix}.eventCategoryId`,
      value: 'eventCategoryId',
    },
    {
      path: `${pathPrefix}.eventTrackingName`,
      value: 'eventTrackingName',
    },
    {
      path: `${pathPrefix}.insightPropertyIds`,
      value: 'insightPropertyIds',
    },
  ];

  return {
    ...pick(perfEvent, ['eventActionId', 'eventCategoryId']),
    pathConfig,
  };
};

export const getEventFromFilterCondition = ({
  condition,
  pathPrefix,
  triggerEvent,
}) => {
  const { eventInfo } = condition;

  let event = triggerEvent;

  if (eventInfo && Object.keys(eventInfo).length > 0) {
    event = {
      ...pick(eventInfo, ['eventActionId', 'eventCategoryId']),

      pathConfig: [
        {
          path: `${pathPrefix}.eventActionId`,
          value: 'eventActionId',
        },
        {
          path: `${pathPrefix}.eventCategoryId`,
          value: 'eventCategoryId',
        },
      ],
    };
  }

  return event;
};

export const getEventsFromFilters = ({
  filters,
  pathPrefix = 'filters',
  triggerEvent,
}) => {
  const events = [];

  loopFilters(filters, {
    eachCondition: (condition, conditionPath) => {
      const event = getEventFromFilterCondition({
        condition,
        pathPrefix: `${pathPrefix}.${conditionPath}`,
        triggerEvent,
      });

      if (event) {
        events.push(event);
      }
    },
  });

  return events;
};

export const getEventsFromTag = ({ tag = {}, pathPrefix }) => {
  const { type: tagType } = tag;

  const events = [];

  if (tagType === 'allocated_code') {
    const { conditions = [] } = tag;

    conditions.forEach((condition, idx) => {
      const { type, value } = condition;

      const { eventActionId, eventCategoryId } = value;

      const path = `${pathPrefix}.conditions.${idx}.value`;

      if (type === 'event') {
        events.push({
          eventActionId,
          eventCategoryId,
          pathConfig: [
            {
              path: `${path}.eventActionId`,
              value: 'eventActionId',
            },
            {
              path: `${path}.eventCategoryId`,
              value: 'eventCategoryId',
            },
            {
              path: `${path}.eventTrackingName`,
              value: 'eventTrackingName',
            },
            {
              path: `${path}.value`,
              pattern: `${eventCategoryId}-${eventActionId}`,
              value: '{eventCategoryId}-{eventActionId}',
            },
          ],
        });
      }
    });
  }

  return events;
};

export const getEventsFromJourneyProperties = ({
  properties,
  pathPrefix = 'properties',
}) => {
  const { nodes } = properties;

  const events = [];

  eachJourneyNodeTags({
    nodes,
    pathPrefix: `${pathPrefix}.nodes`,
    eachTag: ({ tag, path: tagPath }) => {
      events.push(...getEventsFromTag({ tag, pathPrefix: tagPath }));
    },
  });

  return events;
};

export const getEventsFromHavingAttributes = ({
  havingAttrsSettings,
  pathPrefix = 'havingAttributes',
}) => {
  const { triggerEvent, waitEvent } = havingAttrsSettings;

  const events = [];

  [
    {
      ...triggerEvent.attribute,
      path: `${pathPrefix}.triggerEvent.attribute`,
    },
    {
      ...waitEvent.attribute,
      path: `${pathPrefix}.waitEvent.attribute`,
    },
  ].forEach(attribute => {
    const { eventActionId, eventCategoryId, path } = attribute;

    events.push({
      eventActionId,
      eventCategoryId,
      pathConfig: [
        {
          path: `${path}.eventActionId`,
          value: 'eventActionId',
        },
        {
          path: `${path}.eventCategoryId`,
          value: 'eventCategoryId',
        },
      ],
    });
  });

  return events.map(e => ({
    ...e,
    type: OBJECT_TYPE.event,
  }));
};

export const getEventsFromTags = ({ tags, pathPrefix = 'tags' }) => {
  const events = [];

  eachJourneyTags({
    tags,
    pathPrefix,
    eachTag: ({ tag, path }) => {
      events.push(...getEventsFromTag({ tag, pathPrefix: path }));
    },
  });

  return events;
};

export const getEventsFromVariantTags = ({
  metadata,
  pathPrefix = 'metadata',
}) => {
  const events = [];

  const { variants = [] } = metadata;

  eachVariantsTags({
    variants,
    pathPrefix: `${pathPrefix}.variants`,
    eachTags: ({ tags, path }) => {
      events.push(...getEventsFromTags({ tags, pathPrefix: path }));
    },
  });

  return events;
};

/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-param-reassign */
/* eslint-disable camelcase */
import produce from 'immer';
import {
  cloneDeep,
  flatten,
  forEach,
  get,
  isEmpty,
  isFunction,
  isNumber,
  isObjectLike,
  keyBy,
  map,
  set,
  uniqWith,
  find,
} from 'lodash';
import isEqual from 'react-fast-compare';
import SelectorServices from 'services/Selector';
import TRANSLATE_KEY from '../../../messages/constant';
import { NODE_TYPE } from '../../../modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/constant';
import { SYSTEM_BO } from '../../../utils/constants';
import { addRegexFlags } from '../../../utils/regex';
import { isJsonString, parseJSON } from '../../../utils/web/json';
import { getUntitledName } from '../../../utils/web/properties';
import { addMessageToQueue } from '../../../utils/web/queue';
import { parseObjectNumeric, safeParseArray } from '../../../utils/web/utils';
import { getTranslateMessage } from '../../Translate/util';
import {
  FIELD_WITH_OBJ,
  JOURNEY_OBJECT_TYPE,
  MODE,
  OBJECT_TYPE,
} from '../constant';
import {
  getDOAttrsFromCampaign,
  getDOAttrsFromCustomInput,
  getDOsFromCampaign,
  getEventAttrsFromCampaign,
  getEventsFromCampaign,
  getObjectsFromVariantTemplates,
} from './campaign';
import { ALLOW_CREATE_OBJECT, ERROR_CODE } from './config';
import {
  bindBOPropertiesToObjects,
  getDOsFromFilters,
  getDOsFromJourneyProperties,
  getDOsFromPerfEvent,
} from './dataObject';
import {
  getActDOAttrAdditionalInfo,
  getDOAttrsFromFilters,
  getDOAttrsFromJourneyProperties,
  getDOAttrsFromPerfEvent,
} from './dataObjectAttr';
import {
  getEventsFromFilters,
  getEventFromNodeTrigger,
  getEventFromPeformEvent,
  getEventsFromJourneyProperties,
} from './event';
import {
  getEventAttrsFromFilters,
  getEventAttrsFromJourneyProperties,
  getEventAttrsFromPerfEvent,
} from './eventAttr';
import {
  composeObjectErrors,
  concatPathToListObj,
  findObject,
  getActEventAttrAdditionalInfo,
  getObjectName,
  getObjectSettings,
  groupObjectsWithType,
  isDisabledObject,
  loopBranchs,
} from './helper';
import { getJourneyGoalFromEventBase } from './journeyGoal';
import {
  bindPoolIdToPoolObjects,
  getPoolObjsFromCampaign,
  getPoolsFromJourneyProperties,
} from './promotionPool';
import { getSegmentFromFilters } from './segment';
import {
  JourneyObject,
  getCampaignSettings,
  getVariantSettings,
} from './service';

const PATH =
  'MarketingHub/Journey/Create/_UI/JourneyTemplate/utils/objectHandler';

export const JOURNEY_OBJECT_META = (() => {
  const objectMeta = {
    [JOURNEY_OBJECT_TYPE.journeyGoal]: {
      label: 'Goal',
      in: [FIELD_WITH_OBJ.EVENT_BASED, FIELD_WITH_OBJ.SCHEDULED],
      get: {
        [FIELD_WITH_OBJ.EVENT_BASED]: ({ metadata }) => {
          return getJourneyGoalFromEventBase({ metadata });
        },
        [FIELD_WITH_OBJ.SCHEDULED]: ({ metadata }) => {
          return getJourneyGoalFromEventBase({ metadata });
        },
      },
    },
    [JOURNEY_OBJECT_TYPE.segment]: {
      label: 'Segment',
      in: [
        FIELD_WITH_OBJ.SCHEDULED,
        FIELD_WITH_OBJ.CONDITION_YES,
        FIELD_WITH_OBJ.FILTER,
        FIELD_WITH_OBJ.UPDATE_SEGMENT,
      ],
      get: {
        [FIELD_WITH_OBJ.SCHEDULED]: ({ metadata }) => {
          const audiences = [
            {
              key: 'excludedAudiences',
              value: metadata.audiences.excludedAudiences,
            },
            {
              key: 'includedAudiences',
              value: metadata.audiences.includedAudiences,
            },
          ];

          const { itemTypeId } = metadata.audiences;

          return audiences.reduce((acc, { key, value: { filters } }) => {
            let segments = getSegmentFromFilters(filters, { itemTypeId });

            segments = concatPathToListObj(
              segments,
              `metadata.audiences.${key}`,
            );

            acc.push(...segments);

            return acc;
          }, []);
        },
        [FIELD_WITH_OBJ.FILTER]: ({ metadata }) => {
          const { filterType, excludedFilters, filters, itemTypeId } = metadata;

          if (filterType !== 'item_segment') return [];

          const audiences = [
            {
              key: 'excludedFilters',
              value: excludedFilters,
            },
            {
              key: 'filters',
              value: filters,
            },
          ];

          return audiences.reduce((acc, { key, value: filtersObj }) => {
            let segments = getSegmentFromFilters(filtersObj, {
              customPathConfig: ({ defaultPathConfig, conditionPath }) => {
                const temp = [...defaultPathConfig];

                set(temp, '1.path', `${conditionPath}.value`);

                return temp;
              },
              filtersPropName: key,
              itemTypeId,
            });

            segments = concatPathToListObj(segments, `metadata`);

            acc.push(...segments);

            return acc;
          }, []);
        },
        [FIELD_WITH_OBJ.CONDITION_YES]: {
          fn: FIELD_WITH_OBJ.FILTER,
        },
        [FIELD_WITH_OBJ.UPDATE_SEGMENT]: ({ metadata }) => {
          const { itemTypeId, segmentIds } = metadata;

          return segmentIds.map((segmentId, idx) => ({
            segmentId,
            itemTypeId,
            pathConfig: [
              {
                path: `metadata.segmentIds.${idx}`,
                value: 'segmentId',
              },
            ],
          }));
        },
      },
    },
    [JOURNEY_OBJECT_TYPE.dataObject]: {
      label: 'Data Object',
      in: [
        FIELD_WITH_OBJ.CONDITION_YES,
        FIELD_WITH_OBJ.FILTER,
        FIELD_WITH_OBJ.UPDATE_INFO,
        FIELD_WITH_OBJ.DESTINATION,
        FIELD_WITH_OBJ.EVENT_BASED,
        FIELD_WITH_OBJ.WAIT_EVENT,
        FIELD_WITH_OBJ.PROPERTIES,
      ],
      get: {
        [FIELD_WITH_OBJ.EVENT_BASED]: ({ metadata }) => {
          const { event } = metadata;

          const dataObjs = [];

          dataObjs.push(
            ...getDOsFromPerfEvent({
              perfEvent: event,
              pathPrefix: 'metadata.event',
            }),
          );

          if (!isEmpty(metadata?.custom_inputs)) {
            dataObjs.push({
              itemTypeId: SYSTEM_BO.Journey.itemTypeId,
              pathConfig: [],
            });
          }

          return dataObjs;
        },

        [FIELD_WITH_OBJ.FILTER]: ({ metadata }) => {
          const { filters } = metadata;

          const dataObjs = getDOsFromFilters({
            filters,
            pathPrefix: 'metadata.filters',
          });

          return dataObjs;
        },

        [FIELD_WITH_OBJ.CONDITION_YES]: {
          fn: FIELD_WITH_OBJ.FILTER,
        },

        [FIELD_WITH_OBJ.UPDATE_INFO]: ({ metadata }) => {
          const { itemTypeId } = metadata;

          return [
            {
              itemTypeId,
              pathConfig: [
                {
                  path: 'metadata.itemTypeId',
                  value: 'itemTypeId',
                },
                {
                  path: 'metadata.itemTypeName',
                  value: 'itemTypeName',
                },
              ],
            },
          ];
        },

        [FIELD_WITH_OBJ.DESTINATION]: ({ metadata }) => {
          return getDOsFromCampaign({
            metadata,
            pathPrefix: 'metadata',
          });
        },

        [FIELD_WITH_OBJ.WAIT_EVENT]: ({ metadata }) => {
          const { waitingEvents = [] } = metadata;

          return waitingEvents.flatMap((perfEvent, idx) => {
            const dataObjs = getDOsFromPerfEvent({
              perfEvent,
              pathPrefix: `metadata.waitingEvents.${idx}`,
            });

            return dataObjs;
          });
        },

        [FIELD_WITH_OBJ.PROPERTIES]: ({ properties }) =>
          getDOsFromJourneyProperties({ properties }),
      },
    },
    [JOURNEY_OBJECT_TYPE.dataObjectAttr]: {
      label: 'DO attribute',
      in: [
        FIELD_WITH_OBJ.CONDITION_YES,
        FIELD_WITH_OBJ.FILTER,
        FIELD_WITH_OBJ.UPDATE_INFO,
        FIELD_WITH_OBJ.DESTINATION,
        FIELD_WITH_OBJ.WAIT_EVENT,
        FIELD_WITH_OBJ.EVENT_BASED,
        FIELD_WITH_OBJ.PROPERTIES,
      ],
      get: {
        [FIELD_WITH_OBJ.FILTER]: ({ metadata }) => {
          const { filters } = metadata;

          const doAttrs = getDOAttrsFromFilters({
            filters,
          });

          return doAttrs;
        },
        [FIELD_WITH_OBJ.CONDITION_YES]: {
          fn: FIELD_WITH_OBJ.FILTER,
        },
        [FIELD_WITH_OBJ.DESTINATION]: ({ metadata }) => {
          const doAttrs = getDOAttrsFromCampaign({
            metadata,
            pathPrefix: 'metadata',
          });

          return doAttrs;
        },
        [FIELD_WITH_OBJ.EVENT_BASED]: ({ metadata }) => {
          const { event } = metadata;

          const doAttrs = [];

          doAttrs.push(
            ...getDOAttrsFromCustomInput({
              customInputs: metadata.custom_inputs,
              itemTypeId: SYSTEM_BO.Journey.itemTypeId,
            }),

            ...getDOAttrsFromPerfEvent({
              perfEvent: event,
              pathPrefix: 'metadata.event',
            }),
          );

          return doAttrs;
        },
        [FIELD_WITH_OBJ.WAIT_EVENT]: ({ metadata }) => {
          const { waitingEvents = [] } = metadata;

          return waitingEvents.flatMap((perfEvent, idx) => {
            const doAttrs = getDOAttrsFromPerfEvent({
              perfEvent,
              pathPrefix: `metadata.waitingEvents.${idx}`,
            });

            return doAttrs;
          });
        },
        [FIELD_WITH_OBJ.UPDATE_INFO]: ({ metadata }) => {
          const { params, itemTypeId } = metadata;

          return params.map((attrUpdated, idx) => {
            const { propertyName: itemPropertyName } = attrUpdated;

            return {
              itemPropertyName,
              itemTypeId,
              pathConfig: [
                {
                  path: `metadata.params.${idx}.propertyName`,
                  value: 'itemPropertyName',
                },
              ],
            };
          });
        },
        [FIELD_WITH_OBJ.PROPERTIES]: ({ properties }) =>
          getDOAttrsFromJourneyProperties({ properties }),
      },
    },
    [JOURNEY_OBJECT_TYPE.event]: {
      label: 'Event',
      in: [
        FIELD_WITH_OBJ.EVENT_BASED,
        FIELD_WITH_OBJ.WAIT_EVENT,
        FIELD_WITH_OBJ.DESTINATION,
        FIELD_WITH_OBJ.PROPERTIES,
        FIELD_WITH_OBJ.FILTER,
        FIELD_WITH_OBJ.CONDITION_YES,
      ],
      get: {
        [FIELD_WITH_OBJ.EVENT_BASED]: ({ metadata }) => {
          const event = getEventFromPeformEvent({
            perfEvent: metadata.event,
            pathPrefix: 'metadata.event',
          });

          return [event];
        },

        [FIELD_WITH_OBJ.WAIT_EVENT]: ({ metadata }) => {
          const { waitingEvents = [] } = metadata;

          return waitingEvents.map((perfEvent, idx) => {
            const event = getEventFromPeformEvent({
              perfEvent,
              pathPrefix: `metadata.waitingEvents.${idx}`,
            });

            return event;
          });
        },

        [FIELD_WITH_OBJ.DESTINATION]: ({ metadata }) =>
          getEventsFromCampaign({
            metadata,
            pathPrefix: 'metadata',
          }),

        [FIELD_WITH_OBJ.PROPERTIES]: ({ properties }) =>
          getEventsFromJourneyProperties({ properties }),

        [FIELD_WITH_OBJ.FILTER]: ({ metadata }, workflowSettings) => {
          const { filterType, filters } = metadata;

          if (filterType !== 'event_attribute') return [];

          const nodeTriggerEvent = getEventFromNodeTrigger(workflowSettings);

          return getEventsFromFilters({
            filters,
            pathPrefix: 'metadata',
            triggerEvent: nodeTriggerEvent,
          });
        },

        [FIELD_WITH_OBJ.CONDITION_YES]: {
          fn: FIELD_WITH_OBJ.FILTER,
        },
      },
    },
    [JOURNEY_OBJECT_TYPE.eventAttribute]: {
      label: 'Event attribute',
      in: [
        FIELD_WITH_OBJ.EVENT_BASED,
        FIELD_WITH_OBJ.FILTER,
        FIELD_WITH_OBJ.CONDITION_YES,
        FIELD_WITH_OBJ.WAIT_EVENT,
        FIELD_WITH_OBJ.DESTINATION,
        FIELD_WITH_OBJ.PROPERTIES,
      ],
      get: {
        [FIELD_WITH_OBJ.EVENT_BASED]: ({ metadata }) => {
          const eventAttrs = getEventAttrsFromPerfEvent({
            perfEvent: get(metadata, 'event'),
            pathPrefix: 'metadata.event',
          });

          return eventAttrs;
        },
        [FIELD_WITH_OBJ.FILTER]: ({ metadata }, workflowSettings) => {
          const { filterType, filters } = metadata;

          if (filterType !== 'event_attribute') return [];

          const nodeTriggerEvent = getEventFromNodeTrigger(workflowSettings);

          const eventAttrs = getEventAttrsFromFilters({
            filters,
            triggerEvent: nodeTriggerEvent,
          });

          return [...concatPathToListObj(eventAttrs, 'metadata')];
        },
        [FIELD_WITH_OBJ.CONDITION_YES]: {
          fn: FIELD_WITH_OBJ.FILTER,
        },
        [FIELD_WITH_OBJ.WAIT_EVENT]: ({ metadata }) => {
          const { waitingEvents } = metadata;

          return waitingEvents.flatMap((perfEvent, idx) => {
            const eventAttrs = getEventAttrsFromPerfEvent({
              perfEvent,
              pathPrefix: `metadata.waitingEvents.${idx}`,
            });

            return eventAttrs;
          });
        },
        [FIELD_WITH_OBJ.DESTINATION]: ({ metadata }, workflowSettings) => {
          const eventAttrs = getEventAttrsFromCampaign({
            metadata,
            nodeTrigger: workflowSettings,
            pathPrefix: 'metadata',
          });

          return eventAttrs;
        },
        [FIELD_WITH_OBJ.PROPERTIES]: ({ properties, triggerEvent }) =>
          getEventAttrsFromJourneyProperties({ properties, triggerEvent }),
      },
    },
    [JOURNEY_OBJECT_TYPE.campaign]: {
      label: 'Campaign',
      in: [FIELD_WITH_OBJ.DESTINATION],
      get: {
        [FIELD_WITH_OBJ.DESTINATION]: ({ metadata }) => {
          const { destinationId, campaignId, catalogId } = metadata;

          const objects = [
            {
              destinationId,
              campaignId,
              catalogId,
              pathConfig: [
                {
                  path: 'metadata.destinationId',
                  value: 'destinationId',
                },
                {
                  path: 'metadata.catalogId',
                  value: 'catalogId',
                },
                {
                  path: 'metadata.channelId',
                  value: 'channelId',
                },
              ],
            },
          ];

          return objects;
        },
      },
    },
    [JOURNEY_OBJECT_TYPE.promotionPool]: {
      label: 'Promotion pool',
      in: [FIELD_WITH_OBJ.DESTINATION, FIELD_WITH_OBJ.PROPERTIES],
      get: {
        [FIELD_WITH_OBJ.DESTINATION]: ({ metadata }) => {
          return getPoolObjsFromCampaign({
            metadata,
            pathPrefix: 'metadata',
          });
        },
        [FIELD_WITH_OBJ.PROPERTIES]: ({ properties }) =>
          getPoolsFromJourneyProperties({ properties }),
      },
    },
  };

  Object.entries(objectMeta).forEach(([objectType, meta]) => {
    Object.entries(meta.get).forEach(([nodeType, getMethod]) => {
      if (!isFunction(getMethod) && getMethod.fn) {
        const replacedMethod = objectMeta[objectType].get[getMethod.fn];

        objectMeta[objectType].get[nodeType] = replacedMethod;
      }
    });
  });

  return Object.freeze(objectMeta);
})();

export const getObjectsFromBranch = (branch, workflowSettings) => {
  let result = [];

  const { actionType, metadata } = branch;

  try {
    Object.entries(JOURNEY_OBJECT_META).forEach(([objectType, meta]) => {
      const isIn = meta.in.includes(actionType);
      const getMethod = meta.get[actionType];

      if (isIn && isFunction(getMethod)) {
        const objects = safeParseArray(getMethod(branch, workflowSettings));

        result.push(...objects.map(obj => ({ ...obj, type: objectType })));
      }
    });

    // Retrieve objects from data in other projects (media template, email template, etc...)
    if (actionType === NODE_TYPE.DESTINATION) {
      const { variants = [] } = metadata;

      result.push(
        ...getObjectsFromVariantTemplates({
          variants,
          pathPrefix: `metadata.variants`,
        }),
      );
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);

    addMessageToQueue({
      path: PATH,
      func: 'getObjectFromBranch',
      data: error.stack,
      branch,
    });
  }

  result = result.map(obj => ({
    pathConfig: [],
    actionId: branch.actionId,
    actionType: branch.actionType,
    ...obj,
  }));

  // console.log(`object by node ${actionType}`, {
  //   branch,
  //   objects: result,
  // });

  return uniqWith(result, isEqual);
};

export const serializeObjects = async (...serializeHanlderResults) => {
  const objects = Object.fromEntries(
    Object.values(OBJECT_TYPE).map(objectType => [objectType, []]),
  );

  await Promise.all(
    serializeHanlderResults.map(async serializeHandlerResult => {
      const { objects: objsByType } = await serializeHandlerResult;

      Object.entries(objsByType).forEach(([key, value]) => {
        objects[key].push(...value);
      });
    }),
  );

  const allObjects = flatten(Object.values(objects));

  const eachGroupObject = Object.entries(objects).map(
    async ([objectType, objsByType]) => {
      let temp = [...objsByType];

      switch (objectType) {
        case JOURNEY_OBJECT_TYPE.dataObject:
        case JOURNEY_OBJECT_TYPE.dataObjectAttr:
        case JOURNEY_OBJECT_TYPE.eventAttribute: {
          temp = await bindBOPropertiesToObjects(objsByType);
          break;
        }
        case JOURNEY_OBJECT_TYPE.promotionPool: {
          temp = await bindPoolIdToPoolObjects(objsByType);
          break;
        }
        default:
          break;
      }

      temp = groupObjectsWithType({
        objects: temp,
        allObjects,
        objectType,
        customPathConfigProcesser: ({ processedPathConfig, mappedObject }) => {
          const { actionId, actionType } = mappedObject;

          return processedPathConfig.map(pathInfo => ({
            ...pathInfo,
            actionId,
            actionType,
          }));
        },
      });

      return temp;
    },
  );

  const objectGroups = await Promise.all(eachGroupObject);

  return { objects: flatten(objectGroups) };
};

export const serializeJourneyTemplate = async data => {
  const {
    workflow_setting: workflowSetting,
    properties,
    onError,
    onSucecss,
  } = data;

  let serializedData = {
    objects: [],
    objectSettings: [],
    workflowSetting,
    properties: '',
  };

  try {
    await bindCampaignAndVariantsToWorkflowSetting({
      workflowSetting,
    });

    const nodeTriggerEvent = getEventFromNodeTrigger(workflowSetting);

    const { objects } = await serializeObjects(
      getObjectsFromWorkflowSetting({
        workflowSetting,
      }),

      getObjectsFromProperties({
        triggerEvent: nodeTriggerEvent,
        properties: parseJSON(properties),
      }),
    );

    const mainObjects = objects.filter(obj =>
      Object.values(JOURNEY_OBJECT_TYPE).some(type => type === obj.type),
    );

    const objectsInfo = await JourneyObject.getJourneyObjectsDetail({
      objects: mainObjects,
    });

    if (isFunction(onSucecss)) {
      onSucecss();
    }

    serializedData = {
      ...serializedData,

      objects: objectsInfo.objects,
      objectSettings: objectsInfo.objectSettings,
    };
  } catch (error) {
    if (isFunction(onError)) {
      onError(error);
    }

    // eslint-disable-next-line no-console
    console.log(error);

    addMessageToQueue({
      path: PATH,
      func: 'serializeJourneyTemplate',
      data: error.stack,
    });

    return false;
  }

  return serializedData;
};

export const bindCampaignAndVariantsToWorkflowSetting = async ({
  workflowSetting,
}) => {
  const lackingVariantInfos = [];
  const lackingCampaignInfos = [];

  loopBranchs(workflowSetting, branch => {
    const { actionType, actionId } = branch;

    if (actionType === FIELD_WITH_OBJ.DESTINATION) {
      const {
        variants,
        variantIds = [],
        campaignId = null,
        campaign,
      } = branch.metadata;

      if (isEmpty(variants)) {
        variantIds.forEach(variantId => {
          lackingVariantInfos.push({ actionId, variantId });
        });
      }

      if (isEmpty(campaign) && campaignId) {
        lackingCampaignInfos.push({ actionId, campaignId });
      }
    }
  });

  const campaignIds = map(lackingCampaignInfos, ({ campaignId }) => campaignId);
  const variantIds = map(lackingVariantInfos, ({ variantId }) => variantId);

  const [campaigns, variants] = await Promise.all([
    getCampaignSettings({ campaignIds }),
    getVariantSettings({ variantIds }),
  ]);

  loopBranchs(workflowSetting, branch => {
    const { metadata } = branch;

    if (branch.actionType !== FIELD_WITH_OBJ.DESTINATION) return;

    lackingVariantInfos.forEach(({ actionId, variantId }) => {
      if (actionId !== branch.actionId) return;

      const variant = find(variants, { variant_id: variantId });

      if (!variant) return;

      if (isEmpty(metadata.variants)) {
        metadata.variants = [];
      }

      metadata.variants.push(
        parseObjectNumeric(variant, {
          keyFormat: 'camel',
          ignoreKeys: ['custom_inputs'],
        }),
      );
    });

    lackingCampaignInfos.forEach(({ actionId, campaignId }) => {
      if (actionId !== branch.actionId) return;

      const campaign = find(campaigns, { campaign_id: campaignId });

      if (!campaign) return;

      metadata.campaign = parseObjectNumeric(campaign, {
        keyFormat: 'camel',
        ignoreKeys: ['custom_inputs'],
      });
    });
  });

  // console.log(variants, campaigns);
  // console.log(variantInfos, campaignInfos);
  // console.log(workflowSetting);
};

export const getObjectsFromProperties = ({ properties, triggerEvent }) => {
  const result = {
    objects: Object.fromEntries(
      Object.values(OBJECT_TYPE).map(objectType => [objectType, []]),
    ),
  };

  if (isEmpty(properties)) {
    return result;
  }

  Object.entries(JOURNEY_OBJECT_META).forEach(([objectType, meta]) => {
    const isIn = meta.in.includes(FIELD_WITH_OBJ.PROPERTIES);
    const getObjsFn = meta.get[FIELD_WITH_OBJ.PROPERTIES];

    if (!isIn || !getObjsFn) return;

    result.objects[objectType].push(
      ...safeParseArray(getObjsFn({ triggerEvent, properties })),
    );
  });

  return result;
};

export const getObjectsFromWorkflowSetting = ({ workflowSetting }) => {
  const objects = Object.fromEntries(
    Object.values(OBJECT_TYPE).map(objectType => [objectType, []]),
  );

  if (isEmpty(workflowSetting)) {
    return [];
  }

  loopBranchs(workflowSetting, branch => {
    const { actionType } = branch;

    if (actionType !== NODE_TYPE.END) {
      const branchObjects = getObjectsFromBranch(branch, workflowSetting);

      branchObjects.forEach(object => {
        objects[object.type].push(object);
      });
    }
  });

  return { objects };
};

export const getObjectWarningMsg = ({ object, templateObjSettings, mode }) => {
  let objectLabel = JOURNEY_OBJECT_META[object.type].label;

  let warningMsg = null;

  if (object.type === OBJECT_TYPE.eventAttribute && object.itemTypeId) {
    objectLabel = JOURNEY_OBJECT_META.bo_attribute.label;
  }

  if (mode === MODE.Save) {
    const { errorMessage } = findObject(object, templateObjSettings) || {};

    if (errorMessage === ERROR_CODE.NOT_EXIST) {
      warningMsg = `${objectLabel} deleted`;
    }

    if (errorMessage === ERROR_CODE.NONE_PERMISSION) {
      warningMsg = `${objectLabel} access restricted`;
    }
  }

  return { warningMsg };
};

export const getObjectNameDisplay = (args = {}) => {
  const {
    object = {},
    objects = [],
    templateObjSettings = [],
    defaultName = null,
    mode,
  } = args;

  const { type = '' } = object;

  let { warningMsg } = getObjectWarningMsg({
    object,
    templateObjSettings,
    mode,
  });

  try {
    const settings = getObjectSettings({
      object,
      objects,
      templateObjSettings,
      mode,
    });

    let name = getObjectName({
      object,
      settings,
      defaultName,
    });

    if (type === OBJECT_TYPE.eventAttribute && object.itemTypeId) {
      const { warningMsg: boWarningMsg } = getObjectWarningMsg({
        object: {
          type: OBJECT_TYPE.dataObject,
          itemTypeId: object.itemTypeId,
        },
        templateObjSettings,
        mode,
      });

      if (boWarningMsg) {
        warningMsg = `Reference DO: ${boWarningMsg}`;
      } else {
        const boSettings = getObjectSettings({
          object: {
            type: OBJECT_TYPE.dataObject,
            itemTypeId: object.itemTypeId,
          },
          objects,
          templateObjSettings,
          mode,
        });

        const boName = getObjectName({
          object,
          settings: boSettings,
          defaultName,
        });

        name = `Reference DO: ${boName} \u00BB ${name}`;

        if (warningMsg) {
          warningMsg = `Reference DO: ${boName} \u00BB ${warningMsg}`;
        }
      }
    }

    return { name: name || defaultName, warningMsg };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);

    addMessageToQueue({
      func: 'getObjectName',
      error,
    });

    return defaultName;
  }
};

export const composeObjectInfo = args => {
  const { objects, templateObjSettings, mode = MODE.Use } = args;

  const object = findObject(args.object, objects);

  const result = { info: null, saveInfo: undefined };

  if (!object || isEmpty(templateObjSettings)) {
    return result;
  }

  const disabled = isDisabledObject({
    mode,
    object,
    listObject: objects,
    templateObjSettings,
  });

  const { name, warningMsg } = getObjectNameDisplay({
    object,
    objects,
    templateObjSettings,
    defaultName: 'Unknown',
    mode,
  });

  result.info = {
    type: object.type,
    name,
    warningMsg,
    disabled,
  };

  if (mode === MODE.Save) {
    const templateObj = findObject(object, templateObjSettings);

    result.info = {
      ...result.info,
      ...templateObj,

      errors: composeObjectErrors({ object: templateObj, mode }),
    };
  }

  if (mode === MODE.Use && !isEmpty(object.verify)) {
    const objInfo = findObject(object, templateObjSettings);

    result.saveInfo = { ...objInfo };

    const { verify, type, isCreating, isLoadExisting } = object;

    const getAdditionalInfo = () => ({
      ...getActDOAttrAdditionalInfo(object, objects),
      ...getActEventAttrAdditionalInfo(object, objects),
    });

    const composedErrors = composeObjectErrors({ object, mode });

    const allowCreate =
      !get(verify, 'isExist') &&
      isEmpty(composedErrors) &&
      ALLOW_CREATE_OBJECT.includes(type);

    const allowChange =
      get(verify, 'errorMessage') === ERROR_CODE.CATALOG_NOT_VALID
        ? false
        : object.allowChange;

    result.info = {
      ...result.info,
      isCreating,
      allowCreate,
      allowChange,
      isLoadExisting,
      isExist: verify.isExist,
      settings: verify.settings,
      additionalInfo: verify.dataReplace,
      errors: composedErrors,
      ...getAdditionalInfo(),
    };
  }

  return result;
};

const pipe = (...functions) => x => {
  let result = x;

  for (const fn of functions) {
    const { value, stop } = fn(result);

    if (stop) {
      return value; // Dừng pipe và trả về giá trị
    }

    result = value; // Tiếp tục với giá trị mới
  }
  return result;
};

/**
 * Handle replacement of data in journey based on configuration
 * @param {Object} params - Parameters for the replacement
 * @param {Object} params.data - The data to be modified
 * @param {Object} params.config - Configuration for the replacement
 * @param {Object} params.dataReplace - Data to replace with
 * @param {Function} params.mutateEachNode - Function to mutate each node
 * @param {Object} [params.object] - Object data for journey-specific logic
 * @param {Object} [params.objectTemplate] - Object template for journey-specific logic
 * @returns {Object} - The modified data
 */
export const handleReplacement = ({
  data,
  config,
  dataReplace,
  mutateEachNode,
}) => {
  let updatedData = cloneDeep(data);

  // Helper function to replace tags in strings with data values
  function replaceTagWithDataLocal(value, dataObj) {
    if (typeof value !== 'string') return value;

    const regex = /\{([^}]+)\}/g;
    let result = value;
    let match = null;

    // eslint-disable-next-line no-cond-assign
    while ((match = regex.exec(value)) !== null) {
      const [fullMatch, key] = match;
      const replaceValue = get(dataObj, key);

      if (replaceValue !== undefined) {
        result = result.replace(fullMatch, replaceValue);
      }
    }

    return result;
  }

  function checkingReplaceCondition(inputData) {
    // Use custom condition checking if provided
    if (config.originalCheckingCondition) {
      return config.originalCheckingCondition(inputData);
    }

    const { replaceCondition } = config;

    if (!replaceCondition) {
      return { value: inputData };
    }

    const { path, value } = replaceCondition;

    const currentValue = get(inputData, path);

    if (currentValue === value) {
      return { value: inputData };
    }

    return { value: inputData, stop: true };
  }

  function handlerReplaceWithValue(inputData) {
    // Use custom value replacement if provided
    if (config.originalReplaceWithValue) {
      return config.originalReplaceWithValue(inputData);
    }

    const { path, value: valueReplace } = config;

    if (!valueReplace) {
      return { value: inputData };
    }

    const parsedValue = replaceTagWithDataLocal(valueReplace, dataReplace);

    switch (true) {
      case Array.isArray(parsedValue): {
        const currentValue = get(inputData, path, []);

        if (isEqual(currentValue, parsedValue)) {
          return { value: inputData };
        }

        set(inputData, path, parsedValue);
        break;
      }
      default:
        break;
    }

    set(inputData, path, parsedValue);

    return { value: inputData };
  }

  function handlerReplaceWithPattern(inputData) {
    const { path, regex } = config;

    let { pattern, value: valueReplace } = config;

    if (regex) {
      pattern = new RegExp(regex.source, regex.flags || '');
    }

    if (!pattern) {
      return { value: inputData };
    }

    pattern = isNumber(pattern) ? `${pattern}` : pattern;

    valueReplace = replaceTagWithDataLocal(valueReplace, dataReplace);

    const currentValue = get(inputData, path);

    if (typeof currentValue !== 'string') {
      const errorMsg = `The value in path "${path}" will be replaced with a match from a regular expression pattern, not a literal string`;

      throw Error(errorMsg);
    }

    if (valueReplace === undefined || isEqual(currentValue, valueReplace)) {
      return { value: inputData };
    }

    if (typeof pattern === 'string' && pattern !== `${valueReplace}`) {
      const updatedValue = currentValue.replaceAll(pattern, valueReplace);

      set(inputData, path, updatedValue);
    }

    if (pattern instanceof RegExp) {
      const updatedValue = currentValue.replaceAll(
        addRegexFlags(pattern, 'g'),
        valueReplace,
      );

      set(inputData, path, updatedValue);
    }

    return { value: inputData };
  }

  function handlerRenameKey(inputData) {
    const { renameKeys } = config;

    if (isEmpty(renameKeys)) {
      return { value: inputData };
    }

    const { path = '' } = config;

    renameKeys.forEach(renameInfo => {
      const oldKey = replaceTagWithDataLocal(renameInfo.oldKey, dataReplace);
      const newKey = replaceTagWithDataLocal(renameInfo.newKey, dataReplace);

      const currentValue = get(inputData, path);

      if (oldKey === newKey || !isObjectLike(currentValue)) {
        return;
      }

      currentValue[newKey] = currentValue[oldKey];

      delete currentValue[oldKey];

      set(inputData, path, currentValue);
    });

    return { value: inputData, stop: true };
  }

  try {
    const { actionId } = config;

    const handlerReplacePipe = pipe(
      checkingReplaceCondition,
      handlerReplaceWithValue,
      handlerReplaceWithPattern,
      handlerRenameKey,
    );

    switch (true) {
      case !!actionId: {
        updatedData.workflow_setting = mutateEachNode(
          updatedData.workflow_setting,
          node => {
            if (actionId !== node.actionId) return node;

            const temp = handlerReplacePipe(node);

            return temp;
          },
        );
        break;
      }
      default:
        updatedData = handlerReplacePipe(updatedData);
    }
  } catch (error) {
    addMessageToQueue({
      message: 'replace failed',
      args: { config, dataReplace },
      error,
    });

    // eslint-disable-next-line no-console
    console.log(error);
  }

  return updatedData;
};

export const safeParseJourney = ({ journey }) => {
  const result = cloneDeep(journey);

  if (!isJsonString(journey.properties)) {
    result.properties = '{}';
  }

  return result;
};

export const toStorySettings = async ({
  journeyTemplate,
  templateObjSettings,
  objects,
}) => {
  let result = null;

  const { data: zones = [] } = await SelectorServices.zones.getList();

  const byIdZones = keyBy(zones, 'zoneId');

  const mutateEachNode = (node, cb) => {
    const tempNode = cb(node);

    if (Array.isArray(node.branchs)) {
      tempNode.branchs = node.branchs.map(childNode =>
        mutateEachNode(childNode, cb),
      );
    }

    return tempNode;
  };

  const updateWithPathConfig = ({
    journey,
    pathConfig,
    dataReplace: dataReplaceArg,
    object,
    objectTemplate = {},
  }) => {
    let updatedJourney = cloneDeep(journey);

    forEach(pathConfig, config => {
      const dataReplace = {
        ...(config.dataReplace || {}),
        ...(dataReplaceArg || {}),
      };

      // Create a custom config with journey-specific logic
      const customConfig = { ...config };

      const { replaceCondition = [] } = customConfig;

      // Add journey-specific condition checking
      if (Array.isArray(replaceCondition) && replaceCondition.length > 0) {
        customConfig.originalCheckingCondition = inputData => {
          let continuteReplacement = true;

          replaceCondition.forEach(condition => {
            const { key, args } = condition || {};

            if (!continuteReplacement) {
              return;
            }

            switch (key) {
              case 'Only-replace_onChange': {
                const { settingFields = [] } = args || {};

                continuteReplacement = settingFields.some(
                  fieldComparePath =>
                    !isEqual(
                      get(object.verify.settings, fieldComparePath),
                      get(objectTemplate.settings, fieldComparePath),
                    ),
                );
                break;
              }
              default:
                break;
            }
          });

          if (!continuteReplacement) {
            return { value: inputData, stop: true };
          }

          return { value: inputData };
        };
      }

      // Add journey-specific value replacement logic
      if (config.value && typeof config.value === 'string') {
        customConfig.originalReplaceWithValue = inputData => {
          const { path, value: valueKey } = config;

          const currentValue = get(inputData, path);

          const valueReplace = get(dataReplace, valueKey);

          if (
            valueReplace === undefined ||
            isEqual(valueReplace, currentValue)
          ) {
            return { value: inputData };
          }

          set(inputData, path, valueReplace);

          return { value: inputData };
        };
      }

      // Use the handleReplacement function with the custom config
      updatedJourney = handleReplacement({
        data: updatedJourney,
        config: customConfig,
        dataReplace,
        mutateEachNode,
      });
    });

    return updatedJourney;
  };

  const updateDestinations = ({ workflowSetting }) => {
    return mutateEachNode(workflowSetting, node => {
      const { metadata, actionType } = cloneDeep(node);

      if (actionType !== FIELD_WITH_OBJ.DESTINATION) {
        return node;
      }

      metadata.campaignInfo = null;
      metadata.variantInfo = [];

      if (Object.keys(byIdZones).length > 0 && !byIdZones[metadata.zoneId]) {
        metadata.zoneId = zones.at(0).zoneId;
      }

      if (metadata.campaign) {
        metadata.campaignInfo = {
          ...metadata.campaign,
          campaign_id: null,
        };

        delete metadata.campaign;
      }

      if (safeParseArray(metadata.variants).length) {
        metadata.variantInfo = [...metadata.variants].map(variant => {
          const { contentSetting = {} } = variant;
          const { variantExtraData = {} } = contentSetting;

          return {
            ...variant,
            variant_id: null,
            contentSetting: {
              ...contentSetting,
              variantExtraData: produce(variantExtraData || {}, draft => {
                Object.keys(draft).forEach(key => {
                  if (!draft[key]?.fe_config_id) return;

                  draft[key].fe_config_id = null;
                });
              }),
            },
          };
        });

        delete metadata.variants;
      }

      metadata.campaignId = null;
      metadata.variantIds = [];

      metadata.campaignInfo = parseObjectNumeric(metadata.campaignInfo, {
        keyFormat: 'snake',
      });

      metadata.variantInfo = metadata.variantInfo.map(variant =>
        parseObjectNumeric(variant, {
          keyFormat: 'snake',
        }),
      );

      return { ...node, metadata };
    });
  };

  const handleUpdateJourneySettings = args => {
    let { journey } = args;

    journey.properties = parseJSON(journey.properties);

    objects.forEach(object => {
      const { pathConfig, verify } = object;

      const { dataReplace } = verify;

      const objectTemplate = findObject(object, templateObjSettings);

      journey = updateWithPathConfig({
        journey,
        dataReplace,
        pathConfig,
        object,
        objectTemplate,
      });
    });

    journey.workflow_setting = updateDestinations({
      workflowSetting: journey.workflow_setting,
      templateObjSettings,
    });

    journey.workflow_setting.story_name = getUntitledName(
      getTranslateMessage(TRANSLATE_KEY._UNTITLED_STORY, 'Untitled Story'),
    );

    journey.properties = JSON.stringify(journey.properties);

    return journey;
  };

  try {
    const { config } = cloneDeep(journeyTemplate);

    const journey = handleUpdateJourneySettings({
      journey: config.journey,
    });

    journey.tactic = {
      templateId: journeyTemplate.template_id,
      templateName: journeyTemplate.template_name,
      networkId: journeyTemplate.network_id,
    };

    result = journey;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);

    addMessageToQueue({
      message: error.message || 'Some thing wrong',
      func: 'toStorySettings',
      path: PATH,
      args: {
        journeyTemplate,
        objects,
      },
    });

    return { result: null, error };
  }

  return { result, error: null };
};

/* eslint-disable react/default-props-match-prop-types */
/* eslint-disable react/prop-types */
/* eslint-disable no-else-return */
import React, { useState } from 'react';
import PropTypes from 'prop-types';
// import classnames from 'classnames';

// import { FormConditionValue } from '../../_UI/styled';

// import InputCalendar from '../../../../components/common/Input/CalendarV3';
import {
  UIInputCalendar,
  UIWrapperDisable as WrapperDisable,
  UIInput,
  UIInputNumberFormat,
} from '@xlab-team/ui-components';
import { isValid } from 'date-fns';
import {
  isCheckConditionBoolean,
  isCheckHiddenValue,
  isCheckConditionAutoSuggestion,
  isCheckPropertyHasAutoSuggestion,
  isOperatorObjectIdHasSuggestion,
  isOperatorDateTimeAgo,
  isCheckConditionBetween,
  isCheckConditionMultiCheckbox,
  isPropertyDislayFormatNumberwithProperty,
} from '../../../utils';
import ConditionValueBoolean from './ConditionValueBoolean';
import ConditionValueObjectId from './ConditionValueObjectId';
import ConditionValueAutoSuggestion from './ConditionValueAutoSuggestion';
// import InputNumberFormat from '../../../../../components/form/InputNumberFormat';
import ConditionValueDateTimeAgo from './ConditionValueDateTimeAgo';
import ConditionValueNumberBetween from './ConditionValueNumberBetween';
import ConditionValueCalendarBetween from './ConditionValueCalendarBetween';
import TRANSLATE_KEY from '../../../../../messages/constant';
import ConditionError from '../ConditionError';
import { safeParse } from '../../../../../utils/common';
import { AutoSuggestionWrapper, FormConditionValue } from '../styles';
import ConditionValueDropdownSelect from './ConditionValueDropdownSelect';
import { getTranslateMessage } from '../../../../Translate/util';
import ConditionValueArray from './ConditionValueArray';
import { PortalDate } from '../../../../../utils/date';
import { AdvancedPicker } from '@antscorp/antsomi-ui/es/components/molecules/DatePicker/components/AdvancedPicker';
import { AdvancedRangePicker } from '@antscorp/antsomi-ui/es/components/molecules/DatePicker/components/AdvancedRangePicker';
import { OPERATORS_CODE } from '../../../_UI/operators';
import { parseDataRangePickUI } from '../../../../Segment/Content/Condition/utils';
import { getPortalTimeZone } from '../../../../../utils/web/portalSetting';
import { displayFormatDefault } from '../utils';
import ConditionValueStringBetween from './ConditionValueStringBetween';
import { MAP_ATTR_TYPE } from '../../../../modals/ModalComputedType/utils';
import ConditionValueSelectField from './ConditionValueSelectField';

const calculateDiffTimeZone = () => {
  const tzLocal = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const tz = APP_CACHE_PARAMS.p_timezone;

  const date = new PortalDate();
  const dateLocal = date
    .toLocaleString('en-US', {
      timeZone: tzLocal,
      timeZoneName: 'short',
    })
    .split(/GMT/g)[1];

  const dateTz = date
    .toLocaleString('en-US', {
      timeZone: tz,
      timeZoneName: 'short',
    })
    .split(/GMT/g)[1];

  return dateLocal - dateTz;
};

function changeTimeZone(date, add = true, setHour = true) {
  const diffTz = calculateDiffTimeZone();

  if (setHour) {
    date.setHours(0);
  }
  date.setMinutes(0);
  date.setSeconds(0);
  date.setMilliseconds(0);
  if (add) return new Date(date.getTime() + diffTz * 3600 * 1000);

  return new Date(date.getTime() - diffTz * 3600 * 1000);
}

function ConditionValue(props) {
  //
  const {
    item,
    dataType,
    disabled,
    changeValue,
    changeOtherValue,
    use,
    error,
    eventValue,
    isFilter = false,
  } = props;
  const [mustGreaterThanZero, setMustGreaterThanZero] = useState(false);
  const currentOperator = safeParse(item.get('operator'), {
    label: '',
    value: '',
  });
  const eventAttribute = safeParse(item.get('eventAttribute'), null);
  const operatorValue = safeParse(currentOperator.value, '');
  const value = item.get('value');
  const valueEnd = item.get('valueEnd');
  const semantic = item.get('semantic');
  const computeType = safeParse(item.get('computeType'), {});
  const valueDate = isValid(value)
    ? changeTimeZone(new Date(value), false, false).getTime()
    : '';
  const changeValueDate = val => {
    if (semantic && val && val !== null) {
      changeValue(val);
    } else if (val !== null) {
      const date = changeTimeZone(new Date(val), true);
      changeValue(date.getTime());
    } else {
      changeValue(null);
    }
  };
  const changeValueBoolean = val => {
    changeValue(val.value);
  };
  // / min max text field number
  // useEffect(() => {
  //   const valueChange = 1;
  //   if (
  //     computeType.value === MAP_ATTR_TYPE.event_counter.value ||
  //     computeType.value === MAP_ATTR_TYPE.unique_list_count.value
  //   ) {
  //     if (Number(value) <= 0 && !valueBase && valueBase != 0) {
  //       changeValue(valueChange);
  //     }
  //     if (Number(valueEnd) <= 0 && !valueEndBase && valueEndBase != 0) {
  //       changeOtherValue('valueEnd', valueChange);
  //     }
  //   }
  // }, [value, valueEnd]);
  // let checkLabelID = null;
  // if ((item.get('property') || {}).bracketed_code === 'customer_id') {
  //   item.get('property').options.forEach(each => {
  //     if (each.bracketed_code === 'customers.customer_id') {
  //       checkLabelID = each;
  //     }
  //   });
  // } else if ((item.get('property') || {}).bracketed_code === 'user_id') {
  //   item.get('property').options.forEach(each => {
  //     if (each.bracketed_code === 'users.user_id') {
  //       checkLabelID = each;
  //     }
  //   });
  // } else {
  //   checkLabelID = item.get('property');
  // }

  const handleValidateMustGreatThanZero = (valueIn = null) => {
    const isLTZero = valueIn && typeof +valueIn === 'number' && +valueIn <= 0;
    setMustGreaterThanZero(isLTZero);
  };

  const showUIValue = () => {
    const property =
      eventAttribute && Object.keys(eventAttribute).length > 0
        ? eventAttribute
        : safeParse(item.get('property'), null);

    if (property && props.viewData) {
      const { viewId } = props.viewData || {};

      if (viewId) {
        property.configSuggestion.viewId = viewId;
      }
    }
    // single input field
    const noFilterValue = error === 3;
    const noValueBase = error === 6;
    // ConditionValueArray
    const noFilterArray = error === 4 || error === 3;
    const noFilterAutoSuggestion = noFilterArray;

    // two input field, ex: ConditionValueCalendarBetween
    const noFilterValueStart = error === 3;
    const noFilterValueEnd = error === 4;
    const invalidSegment = error === 5;
    const noFilterValueBaseStart = error === 6;
    const noFilterValueBaseEnd = error === 7;

    const requiredFilterMessage = getTranslateMessage(
      TRANSLATE_KEY._NOTI_FIELD_REQUIRED,
      'This field is required',
    );
    const requiredGreterThan0 = getTranslateMessage(
      TRANSLATE_KEY._NOTI_EVENT_COUNTER__ERROR,
      'Value must be greater than 0',
    );
    const invalidSegmentMessage = getTranslateMessage(
      TRANSLATE_KEY._ERROR_INVALID_SEGMENT,
      'Invalid segment',
    );

    const hasValidateGreaterThanZero =
      computeType.value === MAP_ATTR_TYPE.unique_list_count.value ||
      computeType.value === MAP_ATTR_TYPE.event_counter.value;

    // console.log(property);
    // console.log(error);

    const componentKey = `${property.value}__${operatorValue}`;
    if (isCheckConditionBoolean(operatorValue, dataType, use)) {
      return (
        <ConditionValueBoolean
          item={item}
          onChange={changeValueBoolean}
          // isViewMode={props.isViewMode}
        />
      );
    } else if (isCheckConditionBetween(operatorValue)) {
      if (
        dataType === 'number' ||
        (dataType === 'string' &&
          computeType.value === MAP_ATTR_TYPE.unique_list_count.value) ||
        (dataType === 'datetime' &&
          computeType.value === MAP_ATTR_TYPE.unique_list_count.value)
      ) {
        return (
          <ConditionValueNumberBetween
            componentKey={componentKey}
            computeType={computeType}
            value={value}
            changeValue={changeValue}
            changeOtherValue={changeOtherValue}
            valueEnd={valueEnd}
            translateCode={TRANSLATE_KEY._USER_GUIDE_INPUT_VALUE}
            displayFormat={
              computeType.value === MAP_ATTR_TYPE.unique_list_count.value ||
              computeType.value === MAP_ATTR_TYPE.event_counter.value
                ? displayFormatDefault
                : property.displayFormat
            }
            isUseLabel={props.isUseLabel}
            errors={{
              startPoint: {
                hasError: noFilterValueStart || noFilterValueBaseStart,
                helperText: noFilterValueStart
                  ? requiredFilterMessage
                  : noFilterValueBaseStart && requiredGreterThan0,
              },
              endPoint: {
                hasError: noFilterValueEnd || noFilterValueBaseEnd,
                helperText: noFilterValueEnd
                  ? requiredFilterMessage
                  : noFilterValueBaseEnd && requiredGreterThan0,
              },
            }}
            isViewMode={props.isViewMode}
          />
        );
      } else if (dataType === 'datetime') {
        if (semantic) {
          return (
            <div style={{ minWidth: 320 }}>
              <AdvancedRangePicker
                timeRange={parseDataRangePickUI(value)}
                onChange={changeValueDate}
                timezone={getPortalTimeZone()}
                valueType={semantic.value}
                inputStyle={{ marginRight: 10 }}
                useFormatMapping
                separator={
                  <span
                    style={{
                      width: '20px',
                      display: 'block',
                      marginBottom: '5px',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    and
                  </span>
                }
                showLabel={false}
              />
            </div>
          );
        }
        return (
          <ConditionValueCalendarBetween
            componentKey={componentKey}
            value={value}
            changeValue={changeValue}
            changeOtherValue={changeOtherValue}
            valueEnd={valueEnd}
            isShowLabel={props.isShowLabel}
            errors={{
              startDate: {
                hasError: noFilterValueStart,
                helperText: noFilterValueStart && requiredFilterMessage,
              },
              endDate: {
                hasError: noFilterValueEnd,
                helperText: noFilterValueEnd && requiredFilterMessage,
              },
            }}
          />
        );
      }
    } else if (
      dataType === 'datetime' &&
      computeType.value !== MAP_ATTR_TYPE.unique_list_count.value
    ) {
      if (isOperatorDateTimeAgo(operatorValue)) {
        return (
          <ConditionValueDateTimeAgo
            componentKey={componentKey}
            operatorValue={operatorValue}
            value={value}
            valueEnd={valueEnd}
            timeUnit={item.get('timeUnit')}
            onChange={changeValue}
            onChangeOtherValue={changeOtherValue}
            errors={{
              startTime: {
                hasError: noFilterValueStart,
                helperText: noFilterValueStart && requiredFilterMessage,
              },
              endTime: {
                hasError: noFilterValueEnd,
                helperText: noFilterValueEnd && requiredFilterMessage,
              },
            }}
          />
        );
      }
      if (semantic) {
        if (
          operatorValue === OPERATORS_CODE.MATCHES ||
          operatorValue === OPERATORS_CODE.NOT_MATCHES
        ) {
          return (
            <AutoSuggestionWrapper>
              <ConditionValueAutoSuggestion
                componentKey={componentKey}
                isOpenLookup={props.isOpenLookup}
                encryptConfig={props.encryptConfig}
                semantic={semantic}
                property={property}
                eventValue={eventValue}
                use={props.use}
                operatorValue={operatorValue}
                item={item}
                activeRow={props.activeRow}
                onChange={changeValue}
                callback={props.callback}
                fullWidthPopover={props.fullWidthPopover}
                error={noFilterAutoSuggestion || invalidSegment}
                helperText={
                  (noFilterAutoSuggestion && requiredFilterMessage) ||
                  (invalidSegment && invalidSegmentMessage)
                }
                moduleConfig={props.moduleConfig}
              />
            </AutoSuggestionWrapper>
          );
        }
        if (value) {
          // console.log({
          //   onUpdatedNewDate: changeValueDate,
          //   formatInputDisplay: '',
          //   valueType: semantic.value,
          //   timezone: getPortalTimeZone(),
          //   onApply: { changeValueDate },
          //   date: value.date,
          //   format: 'YYYY-MM-DD HH:mm:ss',
          //   option: {
          //     value: value.value,
          //     dateType: value.dateType || 'today',
          //     calculationDate: value.calculationDate || 'days',
          //     calculationType: value.calculationType || 'minus',
          //   },
          // });
          return (
            <AdvancedPicker
              inputStyle={{ width: props.maxWidth }}
              onUpdatedNewDate={changeValueDate}
              formatInputDisplay=""
              valueType={semantic.value}
              timezone={getPortalTimeZone()}
              onApply={changeValueDate}
              date={value[0].date}
              format="YYYY-MM-DD HH:mm:ss"
              option={{
                value: value[0].value,
                dateType: value[0].dateType || 'today',
                calculationDate: value[0].calculationDate || 'days',
                calculationType: value[0].calculationType || 'minus',
              }}
            />
          );
        }
      }

      return (
        <UIInputCalendar
          className="calendar"
          componentKey={componentKey}
          error={noFilterValue}
          helperText={noFilterValue && requiredFilterMessage}
          value={valueDate}
          onChange={changeValueDate}
          isShowLabel={props.isShowLabel}
          placeholder="MM/DD/YYYY"
        />
      );
    } else if (
      dataType === 'object_id' &&
      isCheckPropertyHasAutoSuggestion(property) &&
      isOperatorObjectIdHasSuggestion(operatorValue, item)
    ) {
      // const noFilterValue = error === 3;
      return (
        <ConditionValueObjectId
          componentKey={componentKey}
          encryptConfig={props.encryptConfig}
          operatorValue={operatorValue}
          use={props.use}
          item={item}
          onChange={changeValue}
          callback={props.callback}
          moduleConfig={props.moduleConfig}
        />
      );
    } else if (
      dataType === 'string' &&
      props.isUsingCustomerJourney &&
      operatorValue === OPERATORS_CODE.EQUALS
    ) {
      return (
        <ConditionValueSelectField
          value={item?.get('value', '')}
          error={noFilterAutoSuggestion || invalidSegment}
          helperText={
            (noFilterAutoSuggestion && requiredFilterMessage) ||
            (invalidSegment && invalidSegmentMessage)
          }
          onChange={changeValue}
          sourceTags={props.sourceTags}
        />
      );
    } else if (
      isCheckConditionAutoSuggestion(
        operatorValue,
        property,
        item.get('operators'),
        dataType,
      )
    ) {
      return (
        <AutoSuggestionWrapper>
          <ConditionValueAutoSuggestion
            componentKey={componentKey}
            isOpenLookup={props.isOpenLookup}
            encryptConfig={props.encryptConfig}
            eventValue={eventValue}
            use={props.use}
            operatorValue={operatorValue}
            item={item}
            property={property}
            activeRow={props.activeRow}
            onChange={changeValue}
            callback={props.callback}
            fullWidthPopover={props.fullWidthPopover}
            error={noFilterAutoSuggestion || invalidSegment}
            helperText={
              (noFilterAutoSuggestion && requiredFilterMessage) ||
              (invalidSegment && invalidSegmentMessage)
            }
            moduleConfig={props.moduleConfig}
            isFilter={isFilter}
          />
        </AutoSuggestionWrapper>
      );
    } else if (isCheckConditionMultiCheckbox(dataType)) {
      const options = safeParse(property.propertyValues, []);
      const uiType = safeParse(property.uiType, '');

      if (options.length === 0 && uiType !== 'matches-any-v1') {
        return (
          <ConditionValueArray
            componentKey={componentKey}
            dataType={dataType}
            item={item}
            value={value}
            onChange={changeValue}
            error={noFilterArray}
            helperText={requiredFilterMessage}
          />
        );
      }

      return (
        <ConditionValueDropdownSelect
          componentKey={componentKey}
          isMulti
          item={item}
          value={value}
          onChange={changeValue}
          errors={[...(noFilterArray ? [requiredFilterMessage] : [])]}
        />
      );
    }
    if (isPropertyDislayFormatNumberwithProperty(property, computeType)) {
      if (props.isViewMode) {
        return <span>{value}</span>;
      }

      return computeType && computeType.value === 'event_counter' ? (
        <>
          <UIInputNumberFormat
            componentKey={componentKey}
            hasNote={false}
            returnName={false}
            onChange={(...restOut) => {
              if (hasValidateGreaterThanZero) {
                handleValidateMustGreatThanZero(...restOut);
              }
              changeValue(...restOut);
            }}
            value={value}
            name="Input number"
            translateCode={TRANSLATE_KEY._USER_GUIDE_INPUT_VALUE}
            displayFormat={
              computeType.value === MAP_ATTR_TYPE.unique_list_count.value ||
              computeType.value === MAP_ATTR_TYPE.event_counter.value
                ? displayFormatDefault
                : property.displayFormat
            }
            isUseLabel={props.isUseLabel}
            error={noFilterValue}
            helperText={noFilterValue && requiredFilterMessage}
          />
          <ConditionError
            keyError={9}
            error={9}
            isError={noValueBase || mustGreaterThanZero}
          />
        </>
      ) : (
        <>
          <UIInputNumberFormat
            componentKey={componentKey}
            hasNote={false}
            returnName={false}
            onChange={(...restOut) => {
              if (hasValidateGreaterThanZero) {
                handleValidateMustGreatThanZero(...restOut);
              }

              changeValue(...restOut);
            }}
            value={value}
            name="Input number"
            translateCode={TRANSLATE_KEY._USER_GUIDE_INPUT_VALUE}
            displayFormat={
              computeType.value === MAP_ATTR_TYPE.unique_list_count.value ||
              computeType.value === MAP_ATTR_TYPE.event_counter.value
                ? displayFormatDefault
                : property.displayFormat
            }
            isUseLabel={props.isUseLabel}
            error={noFilterValue || noValueBase || mustGreaterThanZero}
            helperText={
              noFilterValue
                ? requiredFilterMessage
                : (noValueBase || mustGreaterThanZero) && requiredGreterThan0
            }
          />
        </>
      );
    }
    return (
      <UIInput
        componentKey={componentKey}
        type={item.get('dataType')}
        className="ConditionValueText"
        value={value}
        onChange={changeValue}
        placeholder={getTranslateMessage(
          TRANSLATE_KEY._USER_GUIDE_INPUT_VALUE,
          'Input your value',
        )}
        fullWidth
        showCancel
        error={noFilterValue}
        helperText={noFilterValue && requiredFilterMessage}
      />
    );
  };

  if (isCheckHiddenValue(operatorValue)) {
    return null;
  }

  return (
    <FormConditionValue
      className={props.className}
      width={props.width}
      maxWidth={props.maxWidth}
    >
      <WrapperDisable disabled={disabled}>{showUIValue()}</WrapperDisable>
      {/* <ConditionError keyError={3} error={props.error} /> */}
      {/* <ConditionError keyError={4} error={props.error} /> */}
    </FormConditionValue>
  );
}

ConditionValue.defaultProps = {
  // eslint-disable-next-line react/default-props-match-prop-types
  actionType: 'condition', // condition | filter:  phan biet man hinh filter hay condition de callback data encrypt
  disabled: false,
  maxWidth: 'calc(50% - 20px)',
  width: '50%',
  className: '',
  error: 0,
  isUseLabel: true,
  isShowLabel: true,
  onChangeEmptyString: false,
  callback: () => {},
  fullWidthPopover: true,
};

ConditionValue.propTypes = {
  error: PropTypes.any,
  // actionType: PropTypes.string,
  maxWidth: PropTypes.string,
  className: PropTypes.string,
  width: PropTypes.string,
  item: PropTypes.object.isRequired,
  dataType: PropTypes.string.isRequired,
  disabled: PropTypes.bool.isRequired,
  // onlyParent: PropTypes.bool.isRequired,
  changeValue: PropTypes.func.isRequired,
  changeOtherValue: PropTypes.func.isRequired,
  // isUseLabel: PropTypes.bool,
  // isShowLabel: PropTypes.bool,
  // fullWidthPopover: PropTypes.bool,
  // onChangeEmptyString: PropTypes.bool,
};

export default React.memo(ConditionValue);
// export default React.memo(ConditionValue);

/* eslint-disable no-param-reassign */
/* eslint-disable camelcase */
import { isMap as isImmutableMap, isOrderedMap } from 'immutable';
import get from 'lodash/get';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import mapValues from 'lodash/mapValues';
import set from 'lodash/set';
import uniq from 'lodash/uniq';

import { ALLOCATED_CODE, TAG_STATUS, TAG_TYPE } from '@antscorp/antsomi-ui';
import { find, has, isEqualWith, keyBy, map, pick } from 'lodash';
import JourneyService from 'services/Journey';
import { FILTER_TYPE } from '../../../../../components/common/UINodeFilter/utils';
import { toAPIPerformEvent } from '../../../../../components/common/UIPerformEventV2/utils';
import { CONDITION_TYPE } from '../../../../../components/Molecules/ConditionAllocatedCode/constants';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import OperateServices from '../../../../../services/Operate';
import SelectorServices from '../../../../../services/Selector';
import { getObjectPropSafely } from '../../../../../utils/common';
import {
  DATA_ACCESS_OBJECT,
  DEFAULT_STATUS,
} from '../../../../../utils/constants';
import {
  permissionEdit,
  permissionEditOrView,
} from '../../../../../utils/web/permission';
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { validateConditionNo } from './Content/Nodes/ConditionNo/utils';
import { validateNodeYes } from './Content/Nodes/ConditionYes/utils';
import { NODE_TYPE } from './Content/Nodes/constant';
import { validateNodeDelay } from './Content/Nodes/Delay/utils';
import { validateNodeDestination } from './Content/Nodes/Destination/utils';
import { createFeVarinatId } from './Content/Nodes/Destination/utils.state';
import { validateNodeFilter } from './Content/Nodes/Filter/utils';
import { validateNodeTriggerEventBased } from './Content/Nodes/TriggerEventBased/utils';
import { validateNodeTriggerScheduled } from './Content/Nodes/TriggerScheduled/utils';
import { validateNodeUpdateInfo } from './Content/Nodes/UpdateInfo/utils';
import { validateNodeUpdateSegment } from './Content/Nodes/UpdateSegment/utils';
import { validateNodeWaitEvent } from './Content/Nodes/WaitEvent/utils';
import { validateNodeWFRNode } from './Content/Nodes/WFRNode/utils';
import { buildEventTrackingKey, buildEventValue } from './utils.flow';
import { loopNodes } from './utils.map';

const PATH = 'modules/Dashboard/MarketingHub/Journey/Create/utils.validate.js';

export const validateNodeActive = (
  activeNode,
  infoNode,
  isValidateFormatDateTime,
) => {
  try {
    const { type, nodeId } = activeNode;

    if (type === NODE_TYPE.EVENT_BASED) {
      const res = validateNodeTriggerEventBased(
        infoNode,
        isValidateFormatDateTime,
      );
      // console.log('object validateNodeTriggerEventBased', res, infoNode.toJS());
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.DESTINATION) {
      const res = validateNodeDestination(infoNode);
      // console.log('NODE_TYPE.DESTINATION', res);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.SCHEDULED) {
      const res = validateNodeTriggerScheduled(
        infoNode,
        isValidateFormatDateTime,
      );
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.FILTER) {
      const res = validateNodeFilter(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.DELAY) {
      const res = validateNodeDelay(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.WFR_NODE || type === NODE_TYPE.WFR_NODE_NO) {
      const res = validateNodeWFRNode(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.CONDITION_YES) {
      const res = validateNodeYes(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.CONDITION_NO) {
      const res = validateConditionNo(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.WAIT_EVENT) {
      const res = validateNodeWaitEvent(infoNode);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.UPDATE_INFO) {
      const res = validateNodeUpdateInfo(infoNode);
      // console.log('res', res);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    } else if (type === NODE_TYPE.UPDATE_SEGMENT) {
      const res = validateNodeUpdateSegment(infoNode);
      // console.log('res', res);
      if (res.status === false) {
        return { [nodeId]: true };
      }
    }

    return { [nodeId]: false };
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/utils.validate.js',
      func: 'validateNodeActive',
      data: err.stack,
    });

    // eslint-disable-next-line no-console
    console.log(err);
  }

  // console.log('dataNodeActive===>', infoNode);
  return {};
};
export const validateMessage = (
  activeNode,
  infoNode,
  isValidateFormatDateTime,
) => {
  try {
    const { nodeId, type } = activeNode;

    if (type === NODE_TYPE.SCHEDULED) {
      const res = validateNodeTriggerScheduled(
        infoNode,
        isValidateFormatDateTime,
      );
      if (res.status === false) {
        return { [nodeId]: res.errors };
      }
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/utils.validate.js',
      func: 'validateNodeActive',
      data: err.stack,
    });

    // eslint-disable-next-line no-console
    console.log(err);
  }

  // console.log('dataNodeActive===>', infoNode);
  return {};
};
export const validateNodesDuplicateCampaignId = nodes => {
  const uniqueElements = [];

  if (
    !nodes ||
    !nodes.workflow_setting ||
    !nodes.workflow_setting.branchs ||
    nodes.workflow_setting.branchs.length === 0 ||
    !isArray(nodes.workflow_setting.branchs)
  ) {
    return true;
  }

  const { branchs } = nodes.workflow_setting;

  const validateCampaingId = branchsData => {
    branchsData.forEach(each => {
      if (each.actionType === 'DESTINATION' && each.metadata.campaignId) {
        uniqueElements.push(each.metadata.campaignId);
      }
      validateCampaingId(each.branchs);
    });
  };

  validateCampaingId(branchs);

  const isDuplicateCampaignId = uniqueElements.some(
    (element, index) => uniqueElements.indexOf(element) !== index,
  );
  return isDuplicateCampaignId;
  // isDuplicateCampaignId === true: Có id campaignId trùng || Sai
  // isDuplicateCampaignId === false: Có id campaignId trùng || Đúng
};

export const replaceDuplicateVariantKeys = ({ node, uniqVariantKeys = [] }) => {
  try {
    if (node.actionType === 'END' || Object.keys(node).length === 0) {
      return node;
    }

    if (node.branchs && node.branchs.length) {
      node.branchs.forEach(childNode => {
        if (childNode.actionType === NODE_TYPE.DESTINATION) {
          const variants = get(childNode, 'metadata.variants', []);

          if (variants.length) {
            // for replace random setting
            const mapOldVariantKeys = {};

            variants.forEach(variant => {
              const oldVariantKey = variant.variantKey;

              if (oldVariantKey) {
                const isDuplicate = uniqVariantKeys.includes(oldVariantKey);

                if (isDuplicate) {
                  const newVariantKey = createFeVarinatId();

                  // Reassign values to workflow_setting
                  variant = set(variant, 'variantKey', newVariantKey);

                  mapOldVariantKeys[oldVariantKey] = newVariantKey;
                }

                uniqVariantKeys.push(oldVariantKey);
              }
            });

            const random = get(
              childNode,
              'metadata.campaign.campaignSetting.random',
              {},
            );

            // replace variantKeys in random setting
            const newRandom = Object.entries(random).reduce((acc, cur) => {
              const [variantId, percent] = cur;

              const newVariantKey = mapOldVariantKeys[variantId];

              if (newVariantKey) {
                acc[newVariantKey] = percent;
              } else {
                acc[variantId] = percent;
              }

              return acc;
            }, {});

            // Reassign values to workflow_setting
            childNode = set(
              childNode,
              'metadata.campaign.campaignSetting.random',
              newRandom,
            );
          }
        }

        replaceDuplicateVariantKeys({
          node: childNode,
          uniqVariantKeys,
        });
      });
    }

    return {
      node,
      uniqVariantKeys,
    };
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/utils.validate.js',
      func: 'replaceDuplicateVariantKeys',
      data: error.stack,
    });
  }

  return null;
};

const hasDuplicates = a => {
  return uniq(a).length !== a.length;
};

export const handleReplaceDuplicateVariantKeys = workflowSetting => {
  try {
    // replace and check duplicate of new variantKeys, maximum 3 times
    for (let i = 0; i < 3; i += 1) {
      const {
        node: newWorkflowSetting,
        uniqVariantKeys,
      } = replaceDuplicateVariantKeys({
        node: workflowSetting,
      });

      const isDuplicate = hasDuplicates(uniqVariantKeys);

      if (!isDuplicate) {
        return newWorkflowSetting;
      }
    }

    return null;
  } catch (error) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/utils.validate.js',
      func: 'handleReplaceDuplicateVariantKeys',
      data: error.stack,
    });
  }

  return null;
};

export const validateAllJourney = async nodes => {
  let data = {
    isValidate: true,
    error: { message: '', data: nodes, actionId: null },
  };

  if (isEmpty(nodes)) {
    data.isValidate = false;
    data.error.message = 'nodes is required';
  } else if (isEmpty(nodes.workflow_setting)) {
    data.isValidate = false;
    data.error.message = 'workflow_setting is required exist';
  } else if (!isArray(nodes.workflow_setting.branchs)) {
    data.isValidate = false;
    data.error.message = 'branchs is required typeof an array';
  } else if (nodes.workflow_setting.branchs.length === 0) {
    data.isValidate = false;
    data.error.message = 'branchs is require length > 0';
  } else if (nodes.trigger_type === 'SCHEDULED') {
    /* ----------- Check Case SCHEDULED: segmentIds not exist segment ----------- */
    const { workflow_setting } = nodes;
    const { audiences } = workflow_setting.metadata;
    const { excludedAudiences, includedAudiences } = audiences;

    if (isEmpty(audiences)) {
      data.isValidate = false;
      data.error.message = 'audiences is required';
    } else if (
      excludedAudiences.specificAudienceIds.length === 0 &&
      excludedAudiences.filters.OR.length === 0 &&
      includedAudiences.specificAudienceIds.length === 0 &&
      includedAudiences.filters.OR.length === 0
    ) {
      data.isValidate = false;
      data.error.message = 'SCHEDULED: SegmentIds Nothing';
    }
  }

  if (data.isValidate) {
    const nodeDestinations = [];

    const nodeYes = [];

    loopNodes(nodes.workflow_setting, node => {
      if (node.actionType === NODE_TYPE.DESTINATION) {
        nodeDestinations.push(node);
      }

      if (node.actionType === NODE_TYPE.CONDITION_YES) {
        nodeYes.push(node);
      }
    });

    const validateDestinationResult = await valiateDestinations({
      destinations: nodeDestinations,
    });

    const validateYesResult = await validateUIPerformEvent({
      nodes: nodeYes,
    });

    if (!validateDestinationResult.isValidate) {
      data = validateDestinationResult;
    }
  }

  return data;

  // isValidate => True => pass cho action tiep
};

export const validateUIPerformEvent = async ({ nodes }) => {
  console.log('validateUIPerformEvent', nodes);
};

export const valiateDestinations = async ({ destinations }) => {
  const result = {
    isValidate: true,
    error: { message: '', data: destinations, actionId: null },
  };

  destinations.forEach(dest => {
    if (!result.isValidate) return;

    const { metadata = {}, actionId } = dest;

    const noCampaign = !metadata.campaignId && !metadata.campaign;

    const noVariants =
      isEmpty(metadata.variantIds) && isEmpty(metadata.variants);

    if (noCampaign || noVariants) {
      result.isValidate = false;
      result.error.actionId = actionId;
    }
  });

  if (result.isValidate) {
    const campaignNamesValidateResult = await validateCampaignNames({
      nodeDestinations: destinations,
    });

    if (!campaignNamesValidateResult.isValidate) {
      const { info } = campaignNamesValidateResult;

      result.isValidate = false;
      result.error.message = `Campaigns with the same name: ${
        info.campaignName
      }`;
      result.error.actionId = info.actionId;
      result.error.detail = {
        actionId: info.actionId,
        actionType: NODE_TYPE.DESTINATION,
        errors: [getTranslateMessage(TRANSLATE_KEY._NOTIFICATION_NAMESAKE)],
        invalidFields: ['campaignName'],
      };
    }
  }

  return result;
};

export const validateCampaignNames = async ({ nodeDestinations }) => {
  const temp = [...nodeDestinations];

  const notChangedCampaigns = [];
  const newCampaigns = [];

  temp.forEach(node => {
    const { actionId, metadata } = node;

    const { campaignId, campaign } = metadata;

    if (!campaign && campaignId) {
      notChangedCampaigns.push({ actionId, campaignId });
    }

    if (campaign?.campaignName) {
      newCampaigns.push({
        actionId,
        campaignName: campaign.campaignName,
      });
    }
  });

  const { data: campaigns } = await JourneyService.campaigns.getByIds({
    data: {
      campaign_ids: map(notChangedCampaigns, c => c.campaignId),
    },
  });

  const campaignInfos = [...newCampaigns];

  notChangedCampaigns.forEach(info => {
    const { campaignId } = info;

    const campaign = find(campaigns, c => c.campaign_id === campaignId);

    if (!campaign) return;

    campaignInfos.push({
      campaignId,
      campaignName: campaign.campaign_name,
    });
  });

  const result = { isValidate: true, info: null };

  newCampaigns.forEach(info => {
    const { campaignName, actionId } = info;

    if (!result.isValidate) return;

    campaignInfos.forEach(campaign => {
      if (campaign.actionId === actionId) return;

      if (campaign.campaignName === campaignName) {
        result.isValidate = false;
        result.info = {
          actionId: campaign.actionId || actionId,
          campaignName,
        };
      }
    });
  });

  return result;
};

export const validateCampaignEmptySelected = (campaignActive = {}) => {
  const { actionId = '', audiences = {} } = campaignActive;
  let isError = false;
  if (isEmpty(audiences)) {
    return { [actionId]: true };
  }

  const { includedAudiences = {}, excludedAudiences = {} } = audiences;
  const includeAudienceTypes = includedAudiences.audienceTypes;
  const excludeAudienceTypes = excludedAudiences.audienceTypes;
  const includeFilterOR = includedAudiences.filters.OR || [];
  const excludeFilterOR = excludedAudiences.filters.OR || [];

  if (isEmpty(includeAudienceTypes)) {
    return { [actionId]: true };
  }

  if (
    (isEmpty(includedAudiences.specificAudienceIds) &&
      includeFilterOR.some(filterItem => isEmpty(filterItem.AND))) ||
    (isEmpty(includedAudiences.specificAudienceIds) && isEmpty(includeFilterOR))
  ) {
    isError = true;
  }

  if (isError) {
    return { [actionId]: isError };
  }

  if (!isEmpty(excludeAudienceTypes)) {
    if (
      (isEmpty(excludedAudiences.specificAudienceIds) &&
        isEmpty(excludeFilterOR)) ||
      (isEmpty(excludedAudiences.specificAudienceIds) &&
        excludeFilterOR.some(filterItem => isEmpty(filterItem.AND)))
    ) {
      isError = true;
    }
  }

  return { [actionId]: isError };
};

export const validateCampaignSenTo = (
  targetAudience = {},
  nodeId = '',
  design = '',
) => {
  const { currentData = {}, backup = {} } = targetAudience;
  const [errorKey, message, emptyDataAudiences] = [nodeId, '', false];

  if (!isEmpty(backup) && isEmpty(currentData)) {
    if (design === 'update' && isEmpty(backup)) {
      return { errorKey, message, isEmpty: emptyDataAudiences };
    }

    const isNext = Object.keys(backup || {}).some(
      audienceKey => !isEmpty(backup[audienceKey]),
    );

    if (isNext) {
      return {
        errorKey,
        message,
        isEmpty: emptyDataAudiences,
      };
    }

    return {
      errorKey,
      message: `*This field can't be empty`,
      isEmpty: true,
    };
  }

  if (design === 'update' && isEmpty(currentData)) {
    return { errorKey, message, isEmpty: emptyDataAudiences };
  }

  const isNext = Object.keys(currentData || {}).some(
    audienceKey => !isEmpty(currentData[audienceKey]),
  );

  if (isNext) {
    return {
      errorKey,
      message,
      isEmpty: emptyDataAudiences,
    };
  }

  return {
    errorKey,
    message: `*This field can't be empty`,
    isEmpty: true,
  };
};

export const validateSegmentsStatus = async ({ segmentIds }) => {
  const params = {
    data: {
      objectType: 'BO_SEGMENTS',
      isSnakeCase: 1,
      limit: null,
      sort: 'utime',
      sd: 'desc',
      columns: [
        'segment_display',
        'segment_id',
        'item_type_id',
        'segment_code',
      ],
      filters: {
        OR: [
          {
            AND: [
              {
                column: 'segment_id',
                data_type: 'number',
                operator: 'matches',
                value: segmentIds.map(Number),
              },
              {
                column: 'status',
                data_type: 'number',
                operator: 'matches',
                value: [1, 2, 3, 4],
              },
            ],
          },
        ],
      },
    },
  };

  const { data } = await SelectorServices.segments.getListByObjectType(params);

  const filterByStatus = status =>
    data.filter(i => status === +i.status).map(i => i.segment_id);

  return {
    archived: filterByStatus(DEFAULT_STATUS.ARCHIVED),
    removed: filterByStatus(DEFAULT_STATUS.REMOVED),
  };
};

export const validateSegmentsInNodes = async ({
  segmentIds,
  segmentsByNodes,
  flattenNodes,
}) => {
  let results = {};

  try {
    results = mapValues(segmentsByNodes, () => ({
      nonPermissions: [],
      archived: [],
      removed: [],
    }));

    const promies = [
      OperateServices.permission.getInfo({
        body: {
          objects: segmentIds.map(id => ({
            objectId: id,
            objectType: DATA_ACCESS_OBJECT.SEGMENT,
          })),
        },
      }),
      validateSegmentsStatus({ segmentIds }),
    ];

    const [
      { data: accessInfos },
      { archived: archivedSegments, removed: removedSegments },
    ] = await Promise.all(promies);

    const validateFnMap = {
      [NODE_TYPE.SCHEDULED]: permissionEditOrView,
      [NODE_TYPE.FILTER]: permissionEditOrView,
      [NODE_TYPE.CONDITION_YES]: permissionEditOrView,
      [NODE_TYPE.UPDATE_SEGMENT]: permissionEdit,
    };

    Object.entries(segmentsByNodes).forEach(([nodeId, segemntIdsByNode]) => {
      segemntIdsByNode.forEach(segmentId => {
        const accessInfo = accessInfos.find(i => i.objectId === segmentId);
        const nodeInfo = flattenNodes.find(i => i.nodeId === nodeId);
        const fnCheck = () => validateFnMap[nodeInfo.type]({ accessInfo });

        if (getObjectPropSafely(fnCheck, null) === false) {
          results[nodeId].nonPermissions.push(segmentId);
        }

        if (archivedSegments.includes(segmentId)) {
          results[nodeId].archived.push(segmentId);
        }

        if (removedSegments.includes(segmentId)) {
          results[nodeId].removed.push(segmentId);
        }
      });
    });
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      function: validateSegmentsInNodes.name,
      data: error.stack,
    });

    // eslint-disable-next-line no-console
    console.log(error);
  }

  return results;
};

export const mergeIsErrorNodes = (...errorObjs) => {
  const result = {};

  errorObjs.forEach(errorObj => {
    Object.entries(errorObj).forEach(([nodeId, isError]) => {
      if (result[nodeId]) return;

      if (result[nodeId] === undefined && isError) {
        result[nodeId] = true;
      }
    });
  });

  return result;
};

const validateEvent = (triggerEvent, attrTriggerEvt) =>
  isEqualWith(
    triggerEvent,
    attrTriggerEvt,
    (a, b) =>
      a?.eventActionId === b?.eventActionId &&
      a?.eventCategoryId === b?.eventCategoryId,
  );

export const processPerformEvents = (performEvents, triggerEvent) => {
  try {
    if (Array.isArray(performEvents)) {
      return performEvents.every(event => {
        // Skip if no trigger event initialized
        if (!has(event, 'havingAttributes.triggerEvent')) return true;

        return validateEvent(
          triggerEvent,
          get(event, ['havingAttributes', 'triggerEvent', 'attribute'], {}),
        );
      });
    }

    if (isImmutableMap(performEvents)) {
      return performEvents
        .toList()
        .flatMap(each => (isImmutableMap(each) ? each.toList() : []))
        .every(sub => {
          // Skip if no trigger event initialized
          if (!sub.hasIn(['havingAttributes', 'triggerEvent'])) return true;

          return validateEvent(
            triggerEvent,
            sub.getIn(['havingAttributes', 'triggerEvent', 'attribute']),
          );
        });
    }

    return true;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'processPerformEvents',
      data: { error: error.stack, args: { performEvents, triggerEvent } },
    });
    return false;
  }
};

const validatePromotionCode = (item, properties, addError) => {
  try {
    const operator = item?.getIn?.(['operator', 'value']) || item?.operator;
    const dataType = item?.get?.('dataType') || item?.dataType;
    const metadataCode =
      item?.getIn?.(['value', 'code']) || item?.metadata?.code;

    if (
      operator === 'equals' &&
      dataType === 'string' &&
      metadataCode?.type === TAG_TYPE.PROMOTION_CODE
    ) {
      const { nodeId: refNodeId, value: tagId } = metadataCode;
      const isExist = properties.hasIn(['nodes', refNodeId, 'tags', tagId]);
      const type = properties.getIn(
        ['nodes', refNodeId, 'tags', tagId, 'type'],
        '',
      );
      const isPromotion = type === TAG_TYPE.PROMOTION_CODE;

      if (!isExist || !isPromotion) {
        addError(refNodeId, tagId);
      }
    }
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'validatePromotionCode',
      data: { error: error.stack, args: { item, properties, addError } },
    });
  }
};

const validateRefineWithProperties = (data, properties) => {
  if (!data || !isOrderedMap(data) || !isImmutableMap(properties)) {
    return { isValid: true, errors: [] };
  }

  const errors = [];

  data.forEach((item, key) => {
    item.forEach((subItem, subKey) => {
      const refineWithProperties = subItem.get('refineWithProperties');
      if (!isOrderedMap(refineWithProperties)) return;

      refineWithProperties.forEach((propValue, refineKey) => {
        validatePromotionCode(propValue, properties, (refNodeId, tagId) =>
          errors.push({
            key,
            subKey,
            refineKey,
            refNodeId,
            tagId,
            message: "The promotion code doesn't exist",
          }),
        );
      });
    });
  });

  return { isValid: errors.length === 0, errors };
};

const validateMetadataCodes = (data, properties) => {
  const errors = [];

  if (!data?.OR || !Array.isArray(data.OR) || !isImmutableMap(properties))
    return { isValid: true, errors };

  data.OR.forEach((andGroup, orIndex) => {
    if (!andGroup?.AND || !Array.isArray(andGroup.AND)) return;

    andGroup.AND.forEach((condition, andIndex) => {
      validatePromotionCode(condition, properties, (refNodeId, tagId) =>
        errors.push({
          orIndex,
          andIndex,
          refNodeId,
          tagId,
          groupId: condition.groupId,
          column: condition.column,
          message: "The promotion code doesn't exist",
        }),
      );
    });
  });

  return { isValid: errors.length === 0, errors };
};

export const processFilterPerformEvents = ({ nodes, nodeId, properties }) => {
  if (!isImmutableMap(nodes) || !isImmutableMap(properties) || !nodeId)
    return false;

  try {
    const filterPath = [nodeId, 'filter', 'event_attribute', 'rules'];
    const filterRulePath = [...filterPath, 'data-init', 'backup'];
    const filterType = nodes.getIn([nodeId, 'filter', 'filterType', 'value']);

    if (filterType !== FILTER_TYPE.PERFORMED_EVENT) return true;

    const isInit = nodes.getIn([...filterPath, 'data-init', 'isInit']);

    const { isValid } = isInit
      ? validateMetadataCodes(nodes.getIn(filterRulePath, []), properties)
      : validateRefineWithProperties(nodes.getIn(filterPath), properties);

    return isValid;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'processFilterPerformEvents',
      data: {
        error: error.stack,
        args: { nodes, nodeId, properties },
      },
    });
    return false;
  }
};

export const processFilterPerformEventsWithProperty = ({
  nodes,
  nodeId,
  listEvents,
}) => {
  if (!isImmutableMap(nodes) || !nodeId) return false;

  try {
    const filterPath = [nodeId, 'filter', 'event_attribute', 'rules'];
    const filterType = nodes.getIn([nodeId, 'filter', 'filterType', 'value']);

    if (filterType !== FILTER_TYPE.PERFORMED_EVENT) return true;

    const isInit = nodes.getIn([...filterPath, 'data-init', 'isInit']);

    if (isArray(listEvents) && listEvents.length > 0) {
      const { isValid } = isInit
        ? validatePropertyWithMetadata(
            nodes.getIn([...filterPath, 'data-init', 'filters']),
            listEvents,
          )
        : validateProperty(nodes.getIn(filterPath), listEvents);

      return isValid;
    }

    return true;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'processFilterPerformEventsWithProperty',
      data: {
        error: error.stack,
        args: { nodes, nodeId },
      },
    });
    return false;
  }
};

export const validateProperty = (data, listEvent) => {
  if (!data || !isOrderedMap(data)) {
    return { isValid: true, errors: [] };
  }

  const mapEventsByValue = keyBy(listEvent, 'value');

  const errors = [];

  data.forEach((item, key) => {
    item.forEach((subItem, subKey) => {
      const property = subItem?.get('property');

      if (
        property === null ||
        property === undefined ||
        !mapEventsByValue[(property?.value)]
      ) {
        errors.push({
          key,
          subKey,
          message: 'Property is required',
        });
      }
    });
  });

  return { isValid: errors.length === 0, errors };
};

export const validatePropertyWithMetadata = (data, listEvent) => {
  if (!data) {
    return { isValid: true, errors: [] };
  }

  const mapEventsByValue = keyBy(listEvent, 'value');

  const errors = [];

  data.OR.forEach(andGroup => {
    if (!andGroup?.AND || !Array.isArray(andGroup.AND)) return;

    andGroup.AND.forEach(condition => {
      const { eventInfo } = condition;

      // Case eventInfo is empty
      if (!eventInfo) return;

      const keyEvent = `${eventInfo?.eventCategoryId}-${
        eventInfo?.eventActionId
      }-${eventInfo?.nodeId}-${eventInfo?.eventKey}`;

      if (!mapEventsByValue[keyEvent]) {
        errors.push({
          key: andGroup.key,
          subKey: condition.key,
          message: 'Property is required',
        });
      }
    });
  });

  return { isValid: errors.length === 0, errors };
};

const isExistPromoTagInAncestors = ({ node, flattenNodes, properties }) => {
  try {
    const isExistPromoTag = tags =>
      tags.some(tag => tag.type === TAG_TYPE.PROMOTION_CODE);

    let lookupNode = flattenNodes.find(
      flattenNode => flattenNode.nodeId === node.parentId,
    );

    while (lookupNode?.parentId && lookupNode?.parentId !== null) {
      const { nodeId, type: nodeType, parentId } = lookupNode;
      const isDestination = NODE_TYPE.DESTINATION === nodeType;

      if (isDestination && properties.hasIn(['nodes', nodeId, 'tags'])) {
        const listTags = Object.values(
          properties.getIn(['nodes', nodeId, 'tags'], {}),
        );

        if (isExistPromoTag(listTags)) {
          return true;
        }
      }

      // lookup next node
      lookupNode = flattenNodes.find(flNode => flNode.nodeId === parentId);
    }

    return false;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'isExistPromoTagInAncestors',
      data: { error: error.stack, args: { node, flattenNodes, properties } },
    });
    return false;
  }
};

export const validateNodeTags = ({
  nodeId,
  parentId,
  nodes,
  flattenNodes,
  tags = {},
  properties = {},
}) => {
  try {
    if (!nodeId || !isImmutableMap(properties)) {
      throw new Error('Invalid input');
    }

    const errors = [];
    let existPromoTag = false;
    const allocatedTagIdsEmptyPromoCondition = new Set();

    const addError = (tagId, refTagId = null) => {
      errors.push({
        tagId,
        refTagId,
        status: TAG_STATUS.WARNING,
        statusMsg: getTranslateMessage(
          TRANSLATE_KEY._PERSONALIZE_PROCESS_CODE_ERR_TOOLTIP,
          'Invalid settings',
        ),
      });
    };

    const validateDynamicCode = (tagId, condValue) => {
      const { nodeId: refNodeId, tagId: refTagId } = condValue;
      const refTag = properties.getIn(
        ['nodes', refNodeId, 'tags', refTagId],
        {},
      );

      if (isEmpty(refTag) || refTag.type !== TAG_TYPE.PROMOTION_CODE) {
        addError(tagId, refTagId);
        return true; // Stop iteration
      }
      return false;
    };

    const validateEventCode = (tagId, condValue) => {
      if (!isImmutableMap(nodes)) return false;

      const { nodeId: refNodeId, value: eventValue } = condValue;
      const isInit = nodes.getIn([
        refNodeId,
        'peformEvent',
        'data-init',
        'isInit',
      ]);
      let isValidEvent = false;

      const validateEvents = events => {
        if (!isArray(events)) return false;

        return events?.some(({ eventActionId, eventCategoryId, eventKey }) => {
          const trackingKey = buildEventTrackingKey({
            eventActionId,
            eventCategoryId,
          });

          return (
            buildEventValue(trackingKey, refNodeId, eventKey) === eventValue
          );
        });
      };

      if (isInit) {
        const backupEvents = nodes.getIn(
          [refNodeId, 'peformEvent', 'data-init', 'backup'],
          [],
        );
        if (Array.isArray(backupEvents)) {
          isValidEvent = validateEvents(backupEvents);
        } else {
          const { eventActionId, eventCategoryId, eventKey } = pick(
            backupEvents,
            ['eventActionId', 'eventCategoryId', 'eventKey'],
          );
          const trackingKey = buildEventTrackingKey({
            eventActionId,
            eventCategoryId,
          });
          isValidEvent =
            buildEventValue(trackingKey, refNodeId, eventKey) === eventValue;
        }
      } else {
        const performEvent = nodes.getIn([refNodeId, 'peformEvent'], {});
        isValidEvent = validateEvents(toAPIPerformEvent(performEvent));
      }

      if (!isValidEvent) {
        addError(tagId);
        return true; // Stop iteration
      }

      return false;
    };

    Object.entries(tags).forEach(([tagId, { type, conditions = [] }]) => {
      if (type === TAG_TYPE.PROMOTION_CODE) {
        existPromoTag = true;
      }

      // Make sure that the tag type is allocated
      if (type !== ALLOCATED_CODE || !isArray(conditions)) return;

      if (conditions.length === 0) {
        allocatedTagIdsEmptyPromoCondition.add(tagId);
      } else {
        // Check if all conditions are event
        let everyEvtCondition = true;

        // Validate conditions of allocated tag
        conditions.some(({ type: condType, value: condValue = {} }) => {
          if (condType === CONDITION_TYPE.DYNAMIC_CODE) {
            everyEvtCondition = false;
            return validateDynamicCode(tagId, condValue);
          }
          if (condType === CONDITION_TYPE.EVENT) {
            return validateEventCode(tagId, condValue);
          }

          return false;
        });

        // Add tagId if all conditions are event
        if (everyEvtCondition) {
          allocatedTagIdsEmptyPromoCondition.add(tagId);
        }
      }
    });

    if (existPromoTag) allocatedTagIdsEmptyPromoCondition.clear();

    if (allocatedTagIdsEmptyPromoCondition.size) {
      const isValidTagEmptyConditions = isExistPromoTagInAncestors({
        node: { nodeId, parentId },
        flattenNodes,
        properties,
      });

      if (!isValidTagEmptyConditions) {
        allocatedTagIdsEmptyPromoCondition.forEach(tagId => {
          addError(tagId);
        });
      }
    }

    return { nodeId, errors, isValidate: errors.length === 0 };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'validateNodeTags',
      data: {
        error: error.stack,
        args: { nodeId, parentId, tags, flattenNodes, properties },
      },
    });
    return { nodeId, isValidate: false, errors: [] };
  }
};

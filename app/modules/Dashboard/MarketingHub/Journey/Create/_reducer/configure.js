/* eslint-disable no-multi-assign */
/* eslint-disable indent */
/* eslint-disable dot-notation */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
import produce from 'immer';
import { Map, isMap as isImmutableMap } from 'immutable';
import _, { isEmpty, isString, omit, omitBy } from 'lodash';
import isEqual from 'lodash/isEqual';
import { combineReducers } from 'redux';
import { getTranslateMessage } from '../../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../../messages/constant';
import ReduxTypes from '../../../../../../redux/constants';
import { safeParse } from '../../../../../../utils/common';
import { getUntitledName } from '../../../../../../utils/web/properties';
import { NODE_TYPE } from '../Content/Nodes/constant';
import { buildFlattenNodeWithNumberBranch } from '../utils.flow';
import { getNodeRules } from '../utils.node.rules';
import { serializeListNode } from '../utils.serialize';
import configureFlowReducerFor, { actionTypes } from './configure.flow';
import {
  adjustTagIfNeeded,
  checkStatusNode,
  deleteExistNodeIdInFlow,
  getHasOpenModalConfirm,
  getTitleBranch,
  mapDataCopyToNodes,
  // mapInputToAPI,
  mapNodeToFlattenNodes,
  reInitNodeMenus,
} from './utils';

export const initFlattenNodes = [
  {
    nodeId: 'ux3eu',
    parentId: null,
    active: false,
    key: 'EVENT_BASED',
    type: 'EVENT_BASED',
    icon: 'icon-antsomi-action-trigger',
    iconUrl: '',
    value: 'EVENT_BASED',
    label: 'EVENT_BASED',
  },
  {
    label: 'Yes',
    icon: '',
    nodeId: 'pbrsn',
    parentId: 'aqtbzl',
    active: true,
    type: 'CONDITION_YES',
  },
  {
    label: 'Delay',
    icon: '',
    nodeId: 'pbrsnxxx',
    parentId: 'pbrsn',
    active: false,
    type: NODE_TYPE.DELAY,
  },
  // {
  //   label: 'Yes',
  //   icon: '',
  //   nodeId: 'pbrso',
  //   parentId: 'aqtbzl',
  //   active: true,
  //   type: 'CONDITION_YES',
  // },
  // {
  //   label: 'Yes',
  //   icon: '',
  //   nodeId: 'pbrsp',
  //   parentId: 'aqtbzl',
  //   active: true,
  //   type: 'CONDITION_YES',
  // },
  {
    label: 'No',
    icon: '',
    nodeId: 'j5zqa',
    parentId: 'aqtbzl',
    active: false,
    type: 'CONDITION_NO',
  },
  {
    label: 'If/then branch',
    icon: 'icon-xlab-if_then_branches',
    nodeId: 'aqtbzl',
    parentId: 'ux3eu',
    active: false,
    type: 'CLASSIC_LIST_BRANCH',
  },
  // {
  //   label: 'Destination Demo Yes',
  //   icon: '',
  //   nodeId: 'pbrsn111',
  //   parentId: 'pbrsn',
  //   active: false,
  //   type: 'DESTINATION',
  // },
  // {
  //   label: 'Destination Demo No',
  //   icon: '',
  //   nodeId: 'pbrsn112',
  //   parentId: 'j5zqa',
  //   active: false,
  //   type: 'DESTINATION',
  // },
];

// const initFlattenNodes1 = [
//   // {
//   //   nodeId: 'ux3eu',
//   //   parentId: null,
//   //   active: false,
//   //   key: 'SCHEDULED',
//   //   type: 'SCHEDULED',
//   //   icon: 'icon-antsomi-action-trigger',
//   //   iconUrl: '',
//   //   value: 'SCHEDULED',
//   //   label: 'SCHEDULED',
//   // },
// ];

export const initProperties = initData => {
  if (initData) return Map(initData);

  return Map({ nodes: {} });
};

export const initSyncExtraDataProperties = ({
  isSync = false,
  variantIds = new Set(),
} = {}) => ({
  isSync,
  variantIds,
});

export const initialStateConfigure = () => ({
  isLoading: true,
  name: getUntitledName(
    getTranslateMessage(TRANSLATE_KEY._UNTITLED_STORY, 'Untitled Story'),
  ),
  mainNodesBlast: Map({}),
  mainNodesBlastInit: Map({}),
  nodes: Map({}), // = value controlled data của content node
  cacheNodes: Map({}), // = initValue chứa data của từng node sẽ làm data init khi open content của 1 node
  activeNode: {},
  properties: initProperties(),
  syncExtraDataProperties: initSyncExtraDataProperties(),
  fullEventTracking: { list: [], map: {} },
  triggerNode: {},
  destinationNode: { isOpenModalTesting: false, isEnableTesting: false },
  amountDestinations: 0,
  disabledIncreAmount: false,
  triggerEvent: {},
  triggerEventBased: {},
  itemTypeId: undefined,
  node: {
    menus: [],
    map: {},
    list: [],
    tableNodes: { header: [], body: [] },
  },
  rootNode: {
    menus: [],
    map: {},
    list: [],
    tableNodes: { header: [], body: [] },
  },
  // sendToError: Map({}),
  audienceTypeBlastCampaign: null,
  flattenNodes: [],
  initFlattenNodes: [],
  treeNodes: [],
  renderKey: 1,
  validateKey: 1,
  errorKey: 1,
  updateFreshNodesKey: 0,
  errors: {},
  errorsSelectedAudiences: {},
  mainErrors: {},
  disabledRemoveNodes: {},
  previousNodes: {},
  isOpenModal: false,
  hasOpenModalConfirm: false,
  rules: getNodeRules(),
  channelActive: {},
  customInputs: {},
  isValidateFormatDateTime: true,
  errorsSchedule: [],
  nodeInit: Map({}),
  actionNode: 'update',
  startTime: '',
  nodeActiveBehind: [],
  activeNodeMulti: [],
  nodeFlow: [],
  activeNodeResumeSpecific: {},
  updateRefreshBlastCampaign: 0,
  triggerType: null,
  thumbnails: null,
  isCapturing: null,
  pageTitle: '',
  errorsNotification: '',
});

const resetValidate = () => ({
  audienceTypeBlastCampaign: null,
  errors: {},
  errorsSelectedAudiences: {},
  mainErrors: {},
  // sendToError: Map({}),
  errorsSchedule: [],
  errorsNotification: '',
});

const configureReducerFor = moduleConfig => {
  const PREFIX = moduleConfig.key;
  const configureReducer = (state = initialStateConfigure(), action) =>
    produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.RESET}`: {
          const resetDataValidate = resetValidate();
          Object.keys(resetDataValidate).forEach(key => {
            draft[key] = resetDataValidate[key];
          });
          draft.amountDestinations = 0;
          draft.disabledIncreAmount = false;
          draft.properties = initProperties();
          return;
        }

        case `${PREFIX}@@CREATE_JOURNEY${ReduxTypes.RESET}`: {
          return initialStateConfigure();
        }

        case `${PREFIX}${ReduxTypes.INIT}`: {
          const {
            activeRow,
            design,
            channelActive = {},
            blastCampaign,
            isBlastCampaign = false,
          } = action.payload;

          draft.activeNode = {};
          draft.activeNodeMulti = [];
          draft.activeNodeResumeSpecific = {};
          draft.nodeFlow = [];
          if (design === 'create') {
            draft.name = getUntitledName(
              getTranslateMessage(
                TRANSLATE_KEY._UNTITLED_STORY,
                'Untitled Story',
              ),
            );
            draft.pageTitle = getUntitledName(
              getTranslateMessage(
                TRANSLATE_KEY._UNTITLED_STORY,
                'Untitled Story',
              ),
            );
          } else {
            draft.name = activeRow.story_name;
            draft.pageTitle = activeRow.story_name;

            if (activeRow.properties && _.isString(activeRow.properties)) {
              const properties = JSON.parse(activeRow.properties);

              if (properties !== null && _.isObject(properties)) {
                draft.properties = initProperties(properties);
              } else {
                draft.properties = initProperties();
              }
            } else {
              draft.properties = initProperties();
            }
          }
          if (isBlastCampaign) {
            draft.amountDestinations = 1;
          }
          draft.errors = {};
          draft.disabledRemoveNodes = {};
          draft.errorsSchedule = [];
          draft.previousNodes = {};

          if (blastCampaign && design === 'update') {
            draft.nodes = state.nodeInit;
            draft.cacheNodes = state.nodeInit;
            draft.updateRefreshBlastCampaign += 1;
            draft.mainNodesBlast = state.mainNodesBlastInit;
          } else {
            draft.nodes = Map({});
            draft.cacheNodes = Map({});
            draft.nodeInit = Map({});
            draft.mainNodesBlastInit = Map({});
          }

          draft.flattenNodes = [];
          draft.initFlattenNodes = [];
          draft.isOpenModal = false;
          draft.mainErrors = {};
          draft.rules = getNodeRules(channelActive.value);
          draft.channelActive = channelActive;
          return;
        }

        case `${PREFIX}@@STORY_PROPERTIES@@${ReduxTypes.UPDATE_VALUE}`: {
          const newProperties = action.payload;

          if (!isString(newProperties)) {
            draft.properties = initProperties();
            return;
          }

          const properties = JSON.parse(newProperties || '');

          if (properties !== null && _.isObject(properties)) {
            draft.properties = initProperties(properties);
          }

          return;
        }

        case `${PREFIX}@@REMOVE_TAG_NODES${ReduxTypes.DELETE}`: {
          const { removedNodeIds, isRemoveAll = false } = action.payload;

          if (!isImmutableMap(draft.properties)) return;

          if (isRemoveAll) {
            draft.properties = initProperties();
          } else if (_.isArray(removedNodeIds)) {
            draft.properties = draft.properties.withMutations(mProperties => {
              removedNodeIds.forEach(nodeId => {
                mProperties.deleteIn(['nodes', nodeId]);
              });
            });
          }

          return;
        }
        case `${PREFIX}@@DUPLICATE_NODE_TAG_PROPERTY${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const duplicatedNodes = action.payload;

          if (!isImmutableMap(draft.properties) || !_.isArray(duplicatedNodes))
            return;

          draft.properties = draft.properties.withMutations(mProperties => {
            duplicatedNodes.forEach(([fromNodeId, toNodeId]) => {
              const nodeTagDuplicated = adjustTagIfNeeded({
                duplicatedNodes,
                duplicatedTags: mProperties.getIn(
                  ['nodes', fromNodeId, 'tags'],
                  {},
                ),
              });

              mProperties.setIn(['nodes', toNodeId, 'tags'], nodeTagDuplicated);
            });
          });

          return;
        }
        case `${PREFIX}@@BULK_REMOVE_TAG_PROPERTIES@@${ReduxTypes.DELETE}`: {
          const { removedTagIds, nodeId } = action.payload;

          if (
            !nodeId ||
            !isImmutableMap(draft.properties) ||
            !_.isArray(removedTagIds)
          )
            return;

          draft.properties = draft.properties.withMutations(mProperties => {
            removedTagIds.forEach(tagId => {
              if (mProperties.hasIn(['nodes', nodeId, 'tags', tagId])) {
                mProperties.deleteIn(['nodes', nodeId, 'tags', tagId]);
              }
            });
          });

          return;
        }
        case `${PREFIX}@@REMOVE_TAG_PROPERTY${ReduxTypes.DELETE}`: {
          const { nodeId, tagId } = action.payload;

          const deletePath = ['nodes', nodeId, 'tags', tagId];

          if (!isImmutableMap(draft.properties)) return;

          if (draft.properties.hasIn(deletePath)) {
            draft.properties = draft.properties.deleteIn(deletePath);
          }
          return;
        }
        case `${PREFIX}@@REMOVE_REDUNDANT_METADATA_TAGS@@${
          ReduxTypes.DELETE
        }`: {
          const { nodeId, removedTags = {} } = action.payload;
          const { tags = {}, tagIds = [] } = removedTags;

          if (!nodeId || !Array.isArray(tagIds)) return;

          const variantIds = new Set();
          draft.nodes = draft.nodes.withMutations(mNodes => {
            if (mNodes.has(nodeId)) {
              const activeIdPath = [
                nodeId,
                'destination',
                'data',
                'variants',
                'activeId',
              ];

              tagIds.forEach(tagId => {
                const { belongTo = {} } = tags[tagId] || {};

                if (belongTo?.variantId) {
                  const tagIdPath = [
                    'variants',
                    'cacheInfo',
                    belongTo.variantId,
                    'variantExtraData',
                    'properties',
                    'tags',
                    tagId,
                  ];

                  mNodes.deleteIn([nodeId, 'destination', ...tagIdPath]);
                  mNodes.deleteIn([
                    nodeId,
                    'destination',
                    'data',
                    ...tagIdPath,
                  ]);

                  const variantActiveId = mNodes.getIn(activeIdPath);
                  if (variantActiveId === belongTo.variantId) {
                    mNodes.deleteIn([
                      nodeId,
                      'destination',
                      'data',
                      'variantExtraData',
                      'properties',
                      'tags',
                      tagId,
                    ]);
                  }

                  variantIds.add(belongTo.variantId);
                }
              });
            }
          });

          if (variantIds.size > 0) {
            draft.syncExtraDataProperties = initSyncExtraDataProperties({
              isSync: true,
              variantIds,
            });
          }

          return;
        }
        case `${PREFIX}@@ERROR_TAG_PROPERTY@@${ReduxTypes.UPDATE_VALUE}`: {
          const tagErrorInfo = action.payload;

          if (!isImmutableMap(draft.properties) || !_.isArray(tagErrorInfo))
            return;

          draft.properties = draft.properties.withMutations(mProperties => {
            tagErrorInfo.forEach(({ nodeId, tagErrors = [] }) => {
              tagErrors.forEach(({ tagId, status, statusMsg }) => {
                if (mProperties.hasIn(['nodes', nodeId, 'tags', tagId])) {
                  mProperties.mergeIn(['nodes', nodeId, 'tags', tagId], {
                    status,
                    statusMsg,
                  });
                }
              });
            });
          });

          return;
        }
        case `${PREFIX}@@UPDATE_NODE_TAG_PROPERTIES@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { nodeId, tagId, tag } = action.payload || {};

          if (!nodeId || !tagId || !tag || !isImmutableMap(draft.properties))
            return;

          if (draft.properties.hasIn(['nodes', nodeId, 'tags'])) {
            draft.properties = draft.properties.setIn(
              ['nodes', nodeId, 'tags', tagId],
              tag,
            );
          } else {
            const currentNodeProperties = draft.properties.get('nodes', {});

            draft.properties = draft.properties.set('nodes', {
              ...currentNodeProperties,
              [nodeId]: { tags: { [tagId]: tag } },
            });
          }

          return;
        }
        case `${PREFIX}@@MERGE_LIST_TAG_PROPERTIES_NODE@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { nodeId, tags = {} } = action.payload || {};

          if (!nodeId || isEmpty(tags) || !isImmutableMap(draft.properties))
            return;

          if (draft.properties.hasIn(['nodes', nodeId, 'tags'])) {
            draft.properties = draft.properties.mergeIn(
              ['nodes', nodeId, 'tags'],
              tags,
            );
          } else {
            const currentNodeProperties = draft.properties.get('nodes', {});

            draft.properties = draft.properties.set('nodes', {
              ...currentNodeProperties,
              [nodeId]: { tags },
            });
          }

          return;
        }
        case `${PREFIX}@@UPDATE_SYNC_EXTRA_DATA_PROPERTIES@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const newSyncExtraDataProperties = action.payload;

          draft.syncExtraDataProperties = newSyncExtraDataProperties;
          return;
        }
        case `${PREFIX}${ReduxTypes.UPDATE_DONE}`: {
          draft.nodeInit = state.cacheNodes;
          draft.mainNodesBlastInit = state.mainNodesBlast;
          return;
        }
        case `${PREFIX}@@ACTIVE_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { nodeId, isViewMode } = action.payload;
          draft.actionNode = checkStatusNode(state.nodeInit, nodeId);
          if (!state.errors[state.activeNode.nodeId] || isViewMode) {
            // console.log('state.nodes', state.nodes);
            draft.activeNode = action.payload;
            draft.cacheNodes = state.nodes;
            draft.updateFreshNodesKey = 0;
          }
          return;
        }
        case `${PREFIX}@@FLOW@@CONFIRM_REMOVE_NODE_TRIGGER${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.activeNode = {};

          // trả lại rootNode sau khi xóa triggerNode
          draft.node = reInitNodeMenus({
            rootNode: state.rootNode,
            channelId: state.channelActive.value,
            triggerNode: null,
          });

          // Reset itemTypeId when remove trigger node
          draft.itemTypeId = undefined;
          return;
        }
        case `${PREFIX}@@NODE@@${ReduxTypes.GET_LIST_DONE}`: {
          const node = serializeListNode(action.payload);
          draft.node = node;

          // lưu giá trị gốc của node để có thể reset node sau khi đã thay đổi node
          // không được thay đổi rootNode
          draft.rootNode = node;

          return;
        }

        case `${PREFIX}@@STORY_NAME@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.name = action.payload;
          draft.mainErrors = {};
          return;
        }
        case `${PREFIX}@@PAGE_TITLE@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.pageTitle = action.payload;
          return;
        }

        case `${PREFIX}@@UPDATE_CAMPAIGNS_CREATE_COPY@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { campaigns = [] } = action.payload;
          draft.amountDestinations = campaigns.length;
          return;
        }

        case `${PREFIX}@@UPDATE_FULL_EVENT_TRACKING${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const newFullEventTracking = action.payload;

          if (newFullEventTracking) {
            draft.fullEventTracking = newFullEventTracking;
          }
          return;
        }

        case `${PREFIX}@@FLOW@@${actionTypes.CREATE_NODE}${
          ReduxTypes.UPDATE_VALUE
        }`:
        case `${PREFIX}@@UPDATE_AMOUNT_DESTINATIONS@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const {
            amount = 0,
            node = '',
            disabledIncreAmount = false,
          } = action.payload;

          if (node && node.actionType === 'DESTINATION') {
            // For case add node in normal journey and catch from action of configure.flow
            draft.amountDestinations = state.amountDestinations + 1;
          } else if (amount) {
            draft.amountDestinations = +amount;
          }

          if (disabledIncreAmount) {
            draft.disabledIncreAmount = true;
          }
          return;
        }
        // case `${PREFIX}@@NODE_VALIDATION@@${ReduxTypes.UPDATE_VALUE}`: {
        //   draft.activeNode = {};
        //   return;
        // }
        case `${PREFIX}@@DATA_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          const {
            nodeId,
            name,
            data,
            isRealtime = false,
            isMainNodeBlast = false,
          } = action.payload;

          if (name === 'destination') {
            const currentNode = state.nodes.getIn([nodeId, 'destination']);

            if (
              data &&
              currentNode &&
              currentNode.campaignId &&
              data.campaignId &&
              currentNode.campaignId !== data.campaignId
            ) {
              return state;
            }
          } else if (name === 'COMP_PROP_CONDITION_CHANGE_PROPERTY') {
            draft.triggerEvent = {
              ...draft.triggerEvent,
              eventActionId: data.eventActionId,
              eventCategoryId: data.eventCategoryId,
              label: data.eventTrackingName,
            };

            if (data.type === 'eventBased') {
              draft.triggerEventBased = {
                ...draft.triggerEventBased,
                eventActionId: data.eventActionId,
                eventCategoryId: data.eventCategoryId,
                label: data.eventTrackingName,
              };
            }
          } else if (name === 'CHANGE_ITEM_TYPE_ID') {
            draft.itemTypeId = data;
            draft.audienceTypeBlastCampaign = data;
          } else if (name === 'branchName') {
            draft.activeNode.label = data;
          }

          if (isMainNodeBlast) {
            draft.mainNodesBlast = state.mainNodesBlast.set(name, data);
            if (!state.mainNodesBlastInit.has(name)) {
              draft.mainNodesBlastInit = state.mainNodesBlastInit.set(
                name,
                data,
              );
            }
          } else {
            draft.nodes = state.nodes.setIn([nodeId, name], data);

            if (isRealtime) {
              draft.cacheNodes = state.cacheNodes.setIn([nodeId, name], data);
            }

            if (state.nodeInit) {
              const updateNode = state.nodeInit.get(action.payload.nodeId);
              const destination = updateNode
                ? updateNode.get('destination')
                : null;

              if (destination && !destination.isFetchInfoData) {
                draft.nodeInit = state.nodeInit.setIn([nodeId, name], data);
              }
            }
          }

          if (name === 'customInput') {
            draft.customInputs = data;
          }
          return;
        }
        case `${PREFIX}@@REFRESH_NODES@@${ReduxTypes.UPDATE_VALUE}`: {
          // console.log('REFRESH_NODES: ', action);
          const updateNodes = action.payload || [];
          let tempNodes = state.nodes;
          let tempCacheNodes = state.nodes;
          updateNodes.forEach(item => {
            const { nodeId, name, data } = item;
            tempNodes = tempNodes.setIn([nodeId, name], data);
            tempCacheNodes = tempCacheNodes.setIn([nodeId, name], data);
          });

          draft.nodes = tempNodes;
          draft.cacheNodes = tempCacheNodes;
          if (updateNodes.length > 0) {
            draft.updateFreshNodesKey += 1;
          }
          return;
        }
        case `${PREFIX}@@DATA_TRIGGER@@${ReduxTypes.INIT}`: {
          const nodeTrigger = action.payload;
          // console.log('@@DATA_TRIGGER@@', nodeTrigger);
          let triggerEvent = {};
          let triggerEventBased = {};
          draft.node = reInitNodeMenus({
            rootNode: state.rootNode,
            channelId: state.channelActive.value,
            triggerNode: nodeTrigger.type,
          });

          if (Object.keys(nodeTrigger).length > 0) {
            if (nodeTrigger.type === 'EVENT_BASED') {
              draft.itemTypeId = -1007;
              const event = safeParse(nodeTrigger.metadata.event, {});
              if (Object.keys(event).length > 0) {
                triggerEvent = {
                  eventActionId: event.eventActionId,
                  eventCategoryId: event.eventCategoryId,
                  label: event.eventTrackingName,
                  eventKey: event?.eventKey,
                };
                triggerEventBased = {
                  eventActionId: event.eventActionId,
                  eventCategoryId: event.eventCategoryId,
                  label: event.eventTrackingName,
                  eventKey: event?.eventKey,
                };
              }
            } else if (nodeTrigger.type === NODE_TYPE.SCHEDULED) {
              const audiences = safeParse(nodeTrigger.metadata.audiences, {});
              draft.itemTypeId = audiences.itemTypeId;
            }
          }

          draft.triggerEvent = triggerEvent;
          draft.triggerEventBased = triggerEventBased;
          return;
        }

        case `${PREFIX}@@FLATTEN_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          if (
            state.flattenNodes.length - action.payload.length >= 1 &&
            (action.payload.length === 0 ||
              !action.payload.some(
                node =>
                  !state.flattenNodes.some(
                    curNode => curNode.nodeId === node.nodeId,
                  ),
              ))
          ) {
            // case remove
            const node = action.payload.find(
              tmp => tmp.nodeId === state.activeNode.nodeId,
            );
            if (node === undefined) {
              draft.activeNode = {};
              draft.errors = {};
            }
          }

          const hasOpenModalConfirm = getHasOpenModalConfirm(action.payload);

          draft.hasOpenModalConfirm = hasOpenModalConfirm;
          draft.triggerNode =
            action.payload.length > 0 ? action.payload[0] : {};
          draft.flattenNodes = action.payload;
          return;
        }

        case `${PREFIX}@@FLOW_UPDATE@@${ReduxTypes.INIT}`: {
          const {
            flattenNodes,
            cacheNodes,
            mainNodesBlast = null,
          } = action.payload;
          draft.initFlattenNodes = flattenNodes;
          draft.flattenNodes = flattenNodes;
          draft.nodes = cacheNodes;
          draft.cacheNodes = cacheNodes;
          draft.nodeInit = cacheNodes;
          if (mainNodesBlast) {
            draft.mainNodesBlast = mainNodesBlast;
            draft.mainNodesBlastInit = mainNodesBlast;
          }
          return;
        }

        case `${PREFIX}@@FLATTEN_NODES@@${ReduxTypes.UPDATE_VALUE}`: {
          const { flattenNodes } = action.payload;
          draft.initFlattenNodes = flattenNodes;
          draft.flattenNodes = flattenNodes;
          return;
        }

        case `${PREFIX}@@TREE_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          // console.log(action.payload);
          draft.treeNodes = action.payload;
          return;
        }
        case `${PREFIX}@@STORY_START_TIME@@${ReduxTypes.UPDATE_VALUE}`: {
          const { startTime } = action.payload;
          draft.startTime = startTime;
          return;
        }

        case `${PREFIX}@@VALIDATE_FORMAT@@${ReduxTypes.UPDATE_VALUE}`: {
          const { isValidate } = action.payload;
          // console.log(isValidate);
          // if (isValidate) {
          draft.isValidateFormatDateTime = isValidate;
          // }

          return;
        }
        case `${PREFIX}@@GET_DATA_INIT_INPUT@@${ReduxTypes.UPDATE_VALUE}`: {
          // console.log(action.payload);
          const { data, nodeId } = action.payload;
          draft.customInputs = data;
          draft.cacheNodes = state.cacheNodes.setIn(
            [nodeId, 'customInput'],
            data,
          );
          draft.nodes = state.nodes.setIn([nodeId, 'customInput'], data);
          return;
        }

        case `${PREFIX}@@NUMBER_OF_BRANCH@@${ReduxTypes.UPDATE_VALUE}`: {
          const { flattenNodes } = state;

          const {
            newBranchNodes,
            number: numberOfBranch,
            activeNode = state.activeNode,
          } = action.payload;

          const newFlatten = buildFlattenNodeWithNumberBranch({
            flattenNodes,
            activeNode,
            numberOfBranch,
            newBranchNodes,
          });

          if (newFlatten !== null) {
            draft.initFlattenNodes = newFlatten;
            draft.renderKey = state.renderKey + 1;
          }
          return;
        }

        case `${PREFIX}@@SPLIT_NODE_PARENT_FLATTEN@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { flattenNodes } = action.payload;
          // console.log('SPLIT_NODE_PARENT_FLATTEN', flattenNodes);

          if (flattenNodes !== null) {
            draft.initFlattenNodes = flattenNodes;
            draft.renderKey = state.renderKey + 1;
          }
          return;
        }

        case `${PREFIX}@@STORY_NAME_BRANCH@@${ReduxTypes.UPDATE_VALUE}`: {
          const { nodeId, branchName } = action.payload;
          const temp = state.flattenNodes;
          temp.forEach((item, index) => {
            if (item.nodeId === nodeId) {
              temp[index].label = branchName;
            }
          });
          draft.initFlattenNodes = temp;
          draft.renderKey = state.renderKey + 1;
          return;
        }
        case `${PREFIX}@@EMPTY_AUDIENCES_SELECTED@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { errorsInfo = {} } = action.payload;

          // draft.validateKey = state.validateKey + 1;
          draft.errorsSelectedAudiences = _.omitBy(
            {
              ...draft.errorsSelectedAudiences,
              ...errorsInfo,
            },
            value => value === false,
          );

          return;
        }
        case `${PREFIX}@@STORY_ERRORS@@${ReduxTypes.UPDATE_VALUE}`: {
          const { errors, passValidateKey = false } = action.payload;
          const nodeActionId = state.actionNode?.nodeId;

          if (isEmpty(errors) || !Object.keys(errors).length) return;

          if (!isEqual(state.errors, errors) && !errors[nodeActionId]) {
            draft.errorsSchedule = [];
          }
          draft.errors = omitBy(
            {
              ...draft.errors,
              ...errors,
            },
            value => value === false,
          );
          if (passValidateKey) return;
          draft.validateKey = state.validateKey + 1;

          return;
        }

        case `${PREFIX}@@FORCE_STORY_ERRORS@@${ReduxTypes.UPDATE_VALUE}`: {
          const { errors } = action.payload;
          const nodeActionId = state.actionNode?.nodeId;

          // if (!force && (isEmpty(errors) || !Object.keys(errors).length))
          //   return;

          if (!isEqual(state.errors, errors) && !errors?.[nodeActionId]) {
            draft.errorsSchedule = [];
          }

          draft.validateKey = state.validateKey + 1;
          draft.errors = omitBy(
            {
              ...errors,
            },
            value => value === false,
          );
          return;
        }

        // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
        // case `${PREFIX}@@SEND_TO_LIST_ERROR@@${ReduxTypes.UPDATE_VALUE}`: {
        //   const listErrors = action.payload;

        //   let tempError = state.sendToError;
        //   if (Array.isArray(listErrors) && listErrors.length > 0) {
        //     listErrors.forEach(errorItem => {
        //       const { errorKey, message, isEmpty = false } = errorItem;
        //       if (message && isEmpty) {
        //         tempError = tempError.setIn([errorKey, 'message'], message);

        //         draft.sendToError = tempError.setIn(
        //           [errorKey, 'isEmpty'],
        //           isEmpty,
        //         );
        //       } else if (state.sendToError.has(errorKey) && !isEmpty) {
        //         draft.sendToError = state.sendToError.delete(errorKey);
        //       }
        //     });
        //   }
        //   return;
        // }

        // case `${PREFIX}@@SEND_TO_ERROR@@${ReduxTypes.UPDATE_VALUE}`: {
        //   const { errorKey, message, isEmpty = false } = action.payload;

        //   if (message && isEmpty) {
        //     const tempError = state.sendToError.setIn(
        //       [errorKey, 'message'],
        //       message,
        //     );

        //     draft.sendToError = tempError.setIn([errorKey, 'isEmpty'], isEmpty);
        //   } else if (state.sendToError.has(errorKey) && !isEmpty) {
        //     draft.sendToError = state.sendToError.delete(errorKey);
        //   }
        //   return;
        // }

        case `${PREFIX}@@STORY_DETAILS_BLAST_ERRORS@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { errorsList = [] } = action.payload;

          // Example Form errors
          // errors = {
          //   actionId: "h7r6f"
          //   actionType: "DESTINATION"
          //   errors: "Campaign has name '123' is already exists"
          //   invalidFields: ["campaignName"]
          // }
          let nodesTemps = state.nodes;
          errorsList.forEach(errorItem => {
            const actionTypeToLowerCase = errorItem.actionType.toLowerCase();
            if (errorItem.actionType === 'DESTINATION') {
              const dataNode = state.nodes
                .get(errorItem.actionId)
                .get(actionTypeToLowerCase);
              const data = {
                ...dataNode,
                data: {
                  ...dataNode.data,
                  campaignName: {
                    ...((dataNode.data && dataNode.data.campaignName) || {}),
                    errors: [errorItem.errors],
                    isValidate: false,
                  },
                  invalidFields: errorItem.invalidFields,
                },
              };

              nodesTemps = nodesTemps.setIn(
                [errorItem.actionId, actionTypeToLowerCase],
                data,
              );
            }
          });

          draft.nodes = nodesTemps;
          draft.cacheNodes = nodesTemps;
          draft.errorKey = state.errorKey + 1;
          // draft.validateKey = state.validateKey + 1;
          return;
        }

        case `${PREFIX}@@RESET_CAMPAIGN_NAME_STORY_DETAILS_ERRORS@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { actionId } = action.payload;

          const path = [actionId, 'destination', 'data'];

          if (state.nodes.hasIn(path)) {
            const updatedNodes = state.nodes.updateIn(path, updateData => {
              updateData.campaignName.errors = [];
              updateData.campaignName.isValidate = true;
              updateData.invalidFields = [];
              return updateData;
            });

            draft.nodes = updatedNodes;
            draft.cacheNodes = updatedNodes;
            draft.errors = omit(state.errors, [actionId]);
            draft.errorKey = state.errorKey + 1;
          }

          return;
        }

        case `${PREFIX}@@STORY_DETAILS_ERRORS@@${ReduxTypes.UPDATE_VALUE}`: {
          const {
            actionId,
            actionType,
            errors,
            invalidFields,
          } = action.payload;

          // Example Form errors
          // errors = {
          //   actionId: "h7r6f"
          //   actionType: "DESTINATION"
          //   errors: "Campaign has name '123' is already exists"
          //   invalidFields: ["campaignName"]
          // }

          const actionTypeToLowerCase = actionType.toLowerCase();
          const dataNode = state.nodes.get(actionId).get(actionTypeToLowerCase);
          const data = {
            ...dataNode,
            data: {
              ...dataNode.data,
              campaignName: {
                ...dataNode.data.campaignName,
                errors: [errors],
                isValidate: false,
              },
              invalidFields,
              // destinationInput: {
              //   ...dataNode.data.destinationInput,
              //   heading: { isValidate: true },
              //   content: { isValidate: true },
              // },
            },
          };

          // console.log('data', data);
          draft.nodes = state.nodes.setIn(
            [actionId, actionTypeToLowerCase],
            data,
          );
          draft.cacheNodes = state.nodes.setIn(
            [actionId, actionTypeToLowerCase],
            data,
          );
          draft.errorKey = state.errorKey + 1;
          // draft.validateKey = state.validateKey + 1;
          return;
        }

        case `${PREFIX}@@MAIN_ERROR@@${ReduxTypes.UPDATE_VALUE}`: {
          const codeMessage = action.payload;

          if (codeMessage && TRANSLATE_KEY[codeMessage]) {
            draft.mainErrors['story_name'] = [
              getTranslateMessage(
                TRANSLATE_KEY[codeMessage],
                'Error happened, please try again!',
              ),
            ];
          } else {
            draft.mainErrors['story_name'] = [
              getTranslateMessage(
                TRANSLATE_KEY._INVALID_NAME_SYSTEM_ERROR,
                'Error happened, please try again!',
              ),
            ];
          }
          return;
        }

        case `${PREFIX}@@API_ERRORS@@${ReduxTypes.UPDATE_VALUE}`: {
          const errors = action.payload;
          // console.log('errors', errors);
          if (errors.length > 0) {
            const map = {};
            errors.forEach(err => {
              if (err.actionId) {
                map[err.actionId] = true;
              }
            });
            draft.errors = map;
          } else {
            draft.errors = {};
          }
          return;
        }
        case `${PREFIX}@@ERRORS_BRANCH_COPY@@${ReduxTypes.UPDATE_VALUE}`: {
          const errors = action.payload;
          draft.errors = errors;
          return;
        }
        case `${PREFIX}@@TOGGLE_MODAL${ReduxTypes.UPDATE_VALUE}`: {
          draft.isOpenModal = action.payload;
          return;
        }

        case `${PREFIX}@@RESET_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { nodes, errors = {} } = action.payload;
          draft.nodes = nodes;
          draft.cacheNodes = nodes;
          draft.errors = errors;
          return;
        }

        case `${PREFIX}@@STORY_DISABLED_REMOVE_NODES@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          // console.log('STORY_DISABLED_REMOVE_NODES', action.payload);
          draft.disabledRemoveNodes = action.payload;
          draft.previousNodes = action.payload;
          return;
        }

        case `${PREFIX}@@SPLIT_NODE_DATA@@${ReduxTypes.UPDATE_VALUE}`: {
          const { branches = [], activeNode } = action.payload;
          const { flattenNodes } = state;
          let { nodes } = state;
          const listChildren = flattenNodes.filter(
            item => item.parentId === activeNode.nodeId,
          );
          branches.forEach((item, index) => {
            const tmpNode = listChildren[index];
            if (tmpNode) {
              if (nodes.get(tmpNode.nodeId) === undefined) {
                nodes = nodes.set(tmpNode.nodeId, Map({ weight: item.value }));
              } else {
                nodes = nodes.setIn([tmpNode.nodeId, 'weight'], item.value);
              }
              if (
                item.isControlGroup &&
                !nodes.getIn([tmpNode.nodeId, 'controlGroup'])
              ) {
                const currentOrder = [...nodes.values()]
                  .map(node => node.get('controlGroup'))
                  .filter(Boolean).length; // đếm số lượng control group hiện tại đang có
                nodes = nodes.setIn([tmpNode.nodeId, 'controlGroup'], {
                  order: currentOrder + 1,
                });
              }
            }
          });
          draft.nodes = nodes;
          draft.cacheNodes = nodes;
          // console.log('listChildren', listChildren);

          return;
        }

        case `${PREFIX}@@DESTINATION_TOGGLE_MODAL_TESTING@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.destinationNode.isOpenModalTesting = !state.destinationNode
            .isOpenModalTesting;
          return;
        }
        case `${PREFIX}@@UPDATE_ENABLED_BUTTON_TESTING@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.destinationNode.isEnableTesting = action.payload;
          return;
        }
        case `${PREFIX}@@DATA_NODE_COPPY_BRANDS@@${ReduxTypes.UPDATE_VALUE}`: {
          const { nodeId, name, data } = action.payload;
          // draft.nodes = state.nodes.setIn([nodeId, name], data + 1);
          if (state.nodes.get(nodeId)) {
            draft.nodes = state.nodes.setIn([nodeId, name], data + 1);
          } else {
            draft.nodes = state.nodes.set(
              nodeId,
              Map({ numBranchs: data + 1 }),
            );
          }
          return;
        }
        case `${PREFIX}@@ADD_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { key = '', initCampaignNode = null } = action.payload;
          let newNodes;
          if (initCampaignNode) {
            newNodes = state.nodes.set(key, initCampaignNode);
          } else {
            newNodes = state.nodes.setIn([key, 'destination'], {});
          }
          draft.nodes = newNodes;
          draft.cacheNodes = newNodes;

          return;
        }
        case `${PREFIX}@@DUPLICATE_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { newId = '', dataMap = Map({}) } = action.payload;

          draft.nodes = state.nodes.setIn([newId], dataMap);
          draft.cacheNodes = state.nodes.setIn([newId], dataMap);
          return;
        }
        case `${PREFIX}@@REMOVE_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { key = '' } = action.payload;

          if (!state.nodes.has(key)) return;

          const newNodes = state.nodes.delete(key);
          draft.nodes = newNodes;
          draft.cacheNodes = newNodes;
          draft.errors = _.omitBy(
            {
              ...draft.errors,
              ...{ [key]: false },
            },
            value => value === false,
          );
          draft.errorsSelectedAudiences = _.omitBy(
            {
              ...draft.errorsSelectedAudiences,
              ...{ [key]: false },
            },
            value => value === false,
          );
          return;
        }
        case `${PREFIX}@@UPDATE_AUDIENCE_TYPE_BLAST_CAMPAIGN@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { itemTypeId } = action.payload;
          draft.audienceTypeBlastCampaign = itemTypeId;
          return;
        }
        case `${PREFIX}@@FLATTEN_NODE_COPPY_BRANDS@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { newNode, nodeAB } = action.payload;
          const newFlatten = state.flattenNodes;
          const dataTitle = getTitleBranch(newNode);
          const newNodes = mapDataCopyToNodes(newNode, state.nodes, nodeAB);
          let arrayFlatten = mapNodeToFlattenNodes(newNode, newFlatten);

          arrayFlatten = arrayFlatten.map(n => ({
            ...n,
            label: dataTitle[n.nodeId] || n.label,
          }));

          // init data Copy
          draft.nodes = newNodes;
          draft.cacheNodes = newNodes;
          draft.flattenNodes = arrayFlatten;
          draft.initFlattenNodes = arrayFlatten;
          draft.renderKey = state.renderKey + 1;
          return;
        }
        case `${PREFIX}@@BEHIND_NODE_ACTIVE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { arrayNodeId } = action.payload;
          draft.nodeActiveBehind = arrayNodeId;
          // draft.nodes = state.nodes.setIn([nodeId, name], data + 1);
          return;
        }
        case `${PREFIX}@@ACIVE_NODE_MULTI@@${ReduxTypes.UPDATE_VALUE}`: {
          const { node, nodeFlow } = action.payload;
          const nodeTmp = [...state.activeNodeMulti];
          const index = nodeTmp.findIndex(each => each.nodeId === node.nodeId);
          if (index === -1) {
            nodeTmp.push(node);
            draft.nodeFlow = nodeFlow.concat(draft.nodeFlow);
          } else {
            nodeTmp.splice(index, 1);
            draft.nodeFlow = deleteExistNodeIdInFlow(state.nodeFlow, nodeFlow);
          }
          draft.activeNodeMulti = nodeTmp;
          draft.activeNodeResumeSpecific = node;
          // const isExistNode  = findIndexTwoArray(nodeFlow, nodeTmp);
          // if (isExistNode) {
          //   draft.nodeFlow = nodeFlow.concat(draft.nodeFlow);
          // } else {
          //   draft.nodeFlow = deleteExistNodeIdInFlow(state.nodeFlow, nodeFlow);
          // }
          // draft.nodes = state.nodes.setIn([nodeId, name], data + 1);
          return;
        }
        case `${PREFIX}@@UPDATE_VALUE@@${ReduxTypes.UPDATE_VALUE}`: {
          if (Object.entries(action.payload)) {
            Object.entries(action.payload).forEach(([key, value]) => {
              draft[key] = value;
            });
          }
          return;
        }
        case `${PREFIX}@@CAPTURING_THUMBNAIL@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.isCapturing = action.payload;
          return;
        }
        case `${PREFIX}@@STORY_THUMBNAIL@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.thumbnails = action.payload;
          draft.isCapturing = false;
          return;
        }
        case `${PREFIX}@@ERRORS_NOTIFICATION@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.errorsNotification = action.payload;
          return;
        }

        default:
          return state;
      }
    });
  return configureReducer;
};

const configureReducer = moduleConfig =>
  combineReducers({
    main: configureReducerFor(moduleConfig),
    flow: configureFlowReducerFor(moduleConfig),
  });

export default configureReducer;

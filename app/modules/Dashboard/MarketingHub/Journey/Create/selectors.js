import { Map as ImmutableMap, isMap as isImmutableMap } from 'immutable';
import { get, has, isEmpty } from 'lodash';
import { createSelector } from 'reselect';

import { initialStateBlastCampaign } from './BlastCampaign/constants';
import { initialStateConfigure } from './_reducer/configure';
import { initialStateMainCreate } from './_reducer/utils';
import { DEFAULT_EVT_TAG_COLLECTED } from './constants';
import {
  collectAllContentSourceTags,
  collectAllocatedPersonalizeTagAndEvents,
  findNearestParentDestinationNode,
} from './utils.flow';

/**
 * Direct selector to the customer state domain
 */

const defaultValue = {
  configure: {
    main: initialStateConfigure(),
  },
  main: initialStateMainCreate(),
  mainReducer: initialStateBlastCampaign(),
};

const selectDomainMainCreateWorkflow = (state, props) =>
  state.get(props && props.moduleConfig && props.moduleConfig.key) ||
  defaultValue;

const selectDomainMainCreateWorkflowV2 = opts => state =>
  state.get(opts?.moduleConfig?.key) || defaultValue;

/**
 * Other specific selectors
 */

const makeSelectMainReducerCreateWorkflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.mainReducer,
  );

const makeSelectConfigureCreateWorkflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure,
  );

const makeSelectConfigureMainCreateWorkflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main,
  );

const makeSelectMainCreateWorkflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.main,
  );

const makeSelectCreateWorkflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate,
  );

const makeSelectConfigureFlow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.flow,
  );

const makeSelectConfigureRulesFlow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main.rules,
  );

const makeSelectActiveNodeData = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => {
      const { nodeId } = substate.configure.main.activeNode;
      return substate.configure.main.nodes.get(nodeId);
    },
  );

const makeSelectActiveNodeId = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => {
      const { nodeId } = substate.configure.main.activeNode;

      return nodeId;
    },
  );

const makeSelectActiveIdNodeData = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => {
      // const { nodeId } = substate.configure.main.activeNode;
      return substate.configure.main.activeNode || {};
    },
  );

const makeSelectNodeByModuleConfig = moduleConfig => {
  return createSelector(
    state => makeSelectActiveIdNodeData()(state, { moduleConfig }),
    state => selectConfigureMainNodes(state, { moduleConfig }),
    (activeNode, nodes) => {
      return { activeNode, nodes };
    },
  );
};

const makeSelectOpenModalDestinationTesting = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => {
      const { isOpenModalTesting } = substate.configure.main.destinationNode;
      return isOpenModalTesting;
    },
  );

const makeSelectDesign = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.main.design,
  );

const makeSelectConfigureMainCreateWorkflowErrors = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main.errors,
  );

const makeSelectIsExpandNodeData = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.main.isExpand,
  );

const makeSelectIsCloseDataflow = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.main.isCloseDataflow,
  );

const makeSelectIsBlastCampaignFlag = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate?.main?.isBlastCampaign || false,
  );

const makeSelectConfigureMainCreateFlattenNodes = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main.flattenNodes,
  );

const makeSelectConfigureMainTreeNodesByModuleConfig = moduleConfig =>
  createSelector(
    state => makeSelectConfigureMainCreateWorkflow()(state, { moduleConfig }),
    (configureMain = {}) => {
      return configureMain?.treeNodes || [];
    },
  );

const makeSelectConfigureMainCreateFlattenNodesWithModuleConfig = moduleConfig =>
  createSelector(
    state => makeSelectConfigureMainCreateWorkflow()(state, { moduleConfig }),
    (configureMain = {}) => {
      return configureMain?.flattenNodes || [];
    },
  );

const selectConfigureMainProperties = createSelector(
  selectDomainMainCreateWorkflow,
  substate => substate?.configure?.main?.properties || ImmutableMap({}),
);

const selectConfigureMainNodes = createSelector(
  selectDomainMainCreateWorkflow,
  substate => substate?.configure?.main?.nodes,
);

const selectFullEventTracking = createSelector(
  selectDomainMainCreateWorkflow,
  substate => substate?.configure?.main?.fullEventTracking || {},
);

const makeSelectFullEventTracking = moduleConfig =>
  createSelector(
    state => selectFullEventTracking(state, { moduleConfig }),
    fullEventTracking => {
      return fullEventTracking;
    },
  );

const makeSelectNodeIdActive = moduleConfig => {
  return createSelector(
    state => makeSelectConfigureMainCreateWorkflow()(state, { moduleConfig }),
    (configureMain = {}) => {
      const { activeNode = {} } = configureMain;
      return activeNode?.nodeId;
    },
  );
};

const makeSelectNodeTagProperties = moduleConfig => {
  return createSelector(
    state => makeSelectConfigureMainCreateWorkflow()(state, { moduleConfig }),
    (configureMain = {}) => {
      const { properties, activeNode = {} } = configureMain;

      if (isImmutableMap(properties) && activeNode.nodeId) {
        return properties.getIn(['nodes', activeNode.nodeId, 'tags']);
      }

      return undefined;
    },
  );
};

const makeCollectAllocatedPersonalizeTagAndEvents = () =>
  createSelector(
    makeSelectActiveIdNodeData(),
    makeSelectIsBlastCampaignFlag(),
    selectConfigureMainProperties,
    selectFullEventTracking,
    selectConfigureMainNodes,
    makeSelectConfigureMainCreateFlattenNodes(),
    (
      activeNode = {},
      isBlastCampaign,
      properties,
      fullEventTracking,
      nodes,
      flattenNodes,
    ) => {
      // Not support allocated personalize for blast campaign
      if (isBlastCampaign) {
        return DEFAULT_EVT_TAG_COLLECTED;
      }

      return collectAllocatedPersonalizeTagAndEvents({
        activeNode,
        properties,
        nodes,
        flattenNodes,
        fullEventTracking,
      });
    },
  );

const makeCollectAllocatedPersonalizeTagAndEventsModuleConfig = moduleConfig =>
  createSelector(
    state => makeSelectActiveIdNodeData()(state, { moduleConfig }),
    state => makeSelectIsBlastCampaignFlag()(state, { moduleConfig }),
    state => selectConfigureMainProperties(state, { moduleConfig }),
    state => selectFullEventTracking(state, { moduleConfig }),
    state => selectConfigureMainNodes(state, { moduleConfig }),
    state =>
      makeSelectConfigureMainCreateFlattenNodes()(state, { moduleConfig }),
    (
      activeNode = {},
      isBlastCampaign,
      properties,
      fullEventTracking,
      nodes,
      flattenNodes,
    ) => {
      // Not support allocated personalize for blast campaign
      if (isBlastCampaign) {
        return DEFAULT_EVT_TAG_COLLECTED;
      }

      return collectAllocatedPersonalizeTagAndEvents({
        activeNode,
        properties,
        nodes,
        flattenNodes,
        fullEventTracking,
      });
    },
  );

const makeSelectAllContentSourceTags = () =>
  createSelector(
    makeSelectActiveIdNodeData(),
    selectConfigureMainProperties,
    selectConfigureMainNodes,
    makeSelectConfigureMainCreateFlattenNodes(),
    (activeNode = {}, properties, nodes, flattenNodes) => {
      return collectAllContentSourceTags({
        activeNode,
        properties,
        nodes,
        flattenNodes,
      });
    },
  );

const makeSelectUseAllocatedCodeStatus = () => {
  return createSelector(
    makeCollectAllocatedPersonalizeTagAndEvents(),
    makeSelectIsBlastCampaignFlag(),
    (allocatedTagAndEvents, isBlastCampaign) => {
      if (isBlastCampaign) return false; // Not support use allocated code for blast campaign

      return allocatedTagAndEvents?.tags?.list?.length > 0;
    },
  );
};

const makeSelectConfigureMainCreateNodesTree = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main.treeNodes,
  );
const makeSelectConfigureMainCreateInitFlattenNodes = () =>
  createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate.configure.main.flattenNodes,
  );

const makeSelectTriggerEventBaseInfo = moduleConfig =>
  createSelector(
    state => makeSelectConfigureMainCreateWorkflow()(state, { moduleConfig }),
    main => {
      return main?.triggerEventBased || {};
    },
  );

const makeSelectTriggerNode = moduleConfig =>
  createSelector(
    state => makeSelectConfigureMainCreateWorkflow()(state, { moduleConfig }),
    main => {
      let nodeTrigger;

      const triggerInfo = main?.triggerNode;

      if (triggerInfo) {
        nodeTrigger = main?.nodes?.get(get(triggerInfo, 'nodeId'));
      }

      return {
        triggerInfo,
        nodeData: nodeTrigger,
      };
    },
  );

export const selectConfigureMain = opts =>
  createSelector(
    selectDomainMainCreateWorkflowV2(opts),
    substate => substate.configure.main,
  );

export const selectStoryNodeList = opts =>
  createSelector(
    selectConfigureMain(opts),
    main => main.node?.list || [],
  );

export const selectNodes = opts =>
  createSelector(
    selectConfigureMain(opts),
    main => main.nodes,
  );

export const selectNodeActive = opts =>
  createSelector(
    selectNodeActiveId(opts),
    selectFlattenNodes(opts),
    (activeId, flattenNodes) =>
      flattenNodes.find(node => node.nodeId === activeId),
  );

export const selectNodeActiveType = opts =>
  createSelector(
    selectNodeActive(opts),
    nodeActive => nodeActive?.type,
  );

export const selectNodeById = (nodeId, opts) =>
  createSelector(
    selectFlattenNodes(opts),
    flattenNodes => flattenNodes.find(n => n.nodeId === nodeId),
  );

export const selectNodeDataById = (nodeId, opts) =>
  createSelector(
    selectNodes(opts),
    nodes => nodes.get(nodeId),
  );

export const selectNodeActiveData = opts =>
  createSelector(
    selectNodes(opts),
    selectNodeActiveId(opts),
    (nodes, nodeActiveId) => nodes.get(nodeActiveId),
  );

export const selectIsExistNodeData = (nodeId, opts) =>
  createSelector(
    selectNodeDataById(nodeId, opts),
    nodeData => !isEmpty(nodeData),
  );

export const selectIsExistNodeActiveData = opts =>
  createSelector(
    selectNodeActiveData(opts),
    nodeActiveData => !isEmpty(nodeActiveData),
  );

export const selectConfigureNodeErrors = opts =>
  createSelector(
    selectConfigureMain(opts),
    main => main.errors,
  );

export const selectNodeActiveId = opts =>
  createSelector(
    selectConfigureMain(opts),
    main => main.activeNode?.nodeId,
  );

export const selectFlattenNodes = opts =>
  createSelector(
    selectConfigureMain(opts),
    main => main.flattenNodes,
  );

export const selectParentNodeActiveId = opts =>
  createSelector(
    selectFlattenNodes(opts),
    selectNodeActiveId(opts),
    (flattenNodes = [], nodeActiveId) =>
      flattenNodes.find(node => node.nodeId === nodeActiveId),
  );

export const selectNodeActiveParentData = opts =>
  createSelector(
    selectNodes(opts),
    selectParentNodeActiveId(opts),
    (nodes, nodeActiveParentId) => nodes.get(nodeActiveParentId),
  );

export const selectIsNodeExistError = (nodeId, opts) =>
  createSelector(
    selectConfigureNodeErrors(opts),
    errors => has(errors, nodeId),
  );

export const selectNodeActiveExistError = opts =>
  createSelector(
    selectNodeActiveId(opts),
    selectConfigureNodeErrors(opts),
    (nodeActiveId, errors) => has(errors, nodeActiveId),
  );

export const selectNearestParentDestinationNode = (nodeId, opts) =>
  createSelector(
    selectFlattenNodes(opts),
    (flattenNodes = []) =>
      findNearestParentDestinationNode(flattenNodes, nodeId),
  );

export const selectModalSaveAsTemplate = moduleConfig =>
  createSelector(
    state => makeSelectMainCreateWorkflow()(state, { moduleConfig }),
    main => main.modalSaveAsTemplate,
  );

export const makeSelectScheduledThirdParty = () => {
  return createSelector(
    selectDomainMainCreateWorkflow,
    substate => substate?.mainReducer?.trigger?.scheduledThirdParty || {},
  );
};

// const makeSelectNeedConfirmActive = moduleConfig =>
//   createSelector(
//     makeSelectJourneyChannelActive(),
//     makeSelectTriggerNode(moduleConfig),
//     state => makeSelectMainCreateWorkflow()(state, { moduleConfig }),
//     (channelActive, triggerNode, main) => {
//       const nodeTriggerMetadata = get(
//         main,
//         'activeRow.workflow_setting.metadata',
//       );

//       return checkDataForActiveChanged({
//         channel: channelActive,
//         nodeTriggerInfo: triggerNode.triggerInfo,
//         nodeTriggerData: triggerNode.nodeData,
//         nodeTriggerMetadata,
//       });
//     },
//   );

/**
 * Default selector used by Customer
 */

export default makeSelectCreateWorkflow;

export {
  makeCollectAllocatedPersonalizeTagAndEvents,
  makeCollectAllocatedPersonalizeTagAndEventsModuleConfig,
  makeSelectActiveIdNodeData,
  makeSelectActiveNodeData,
  makeSelectActiveNodeId,
  makeSelectAllContentSourceTags,
  makeSelectConfigureCreateWorkflow,
  makeSelectConfigureFlow,
  makeSelectConfigureMainCreateFlattenNodes,
  makeSelectConfigureMainCreateFlattenNodesWithModuleConfig,
  makeSelectConfigureMainCreateInitFlattenNodes,
  makeSelectConfigureMainCreateNodesTree,
  makeSelectConfigureMainCreateWorkflow,
  makeSelectConfigureMainCreateWorkflowErrors,
  makeSelectConfigureMainTreeNodesByModuleConfig,
  makeSelectConfigureRulesFlow,
  makeSelectDesign,
  makeSelectFullEventTracking,
  makeSelectIsCloseDataflow,
  makeSelectIsExpandNodeData,
  makeSelectMainCreateWorkflow,
  makeSelectMainReducerCreateWorkflow,
  makeSelectNodeByModuleConfig,
  makeSelectNodeIdActive,
  makeSelectNodeTagProperties,
  makeSelectOpenModalDestinationTesting,
  makeSelectTriggerEventBaseInfo,
  makeSelectTriggerNode,
  makeSelectUseAllocatedCodeStatus,
  selectDomainMainCreateWorkflow,
};

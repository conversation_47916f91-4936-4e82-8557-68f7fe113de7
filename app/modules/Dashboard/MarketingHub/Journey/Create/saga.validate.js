// Libraries
import { put, select } from 'redux-saga/effects';
import { isArray, isObject, isEmpty } from 'lodash';
import { isMap as isImmutableMap } from 'immutable';

// Selectors
import {
  makeCollectAllocatedPersonalizeTagAndEventsModuleConfig,
  makeSelectFullEventTracking,
  selectDomainMainCreateWorkflow,
} from './selectors';

// Utils
import { addMessageToQueue } from '../../../../../utils/web/queue';
import { updateValue } from '../../../../../redux/actions';
import {
  processFilterPerformEvents,
  processFilterPerformEventsWithProperty,
  processPerformEvents,
  validateNodeTags,
} from './utils.validate';

// Constants
import { NODE_TYPE } from './Content/Nodes/constant';
import { collectAllocatedPersonalizeTagAndEvents } from './utils.flow';

const PATH =
  'app/modules/Dashboard/MarketingHub/Journey/Create/saga.validate.js';

const { DESTINATION, FILTER } = NODE_TYPE;

export function* handleValidateTagRuleBeforeSave(args) {
  try {
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );

    const { configure } = reducer;
    const {
      flattenNodes,
      properties,
      nodes,
      triggerNode,
      triggerEventBased,
    } = configure.main;

    const isActBaseTrigger = triggerNode?.type === NODE_TYPE.EVENT_BASED;

    const errors = {};
    const tagErrorInfo = [];

    if (
      isArray(flattenNodes) &&
      flattenNodes.length >= 2 && // At least 2 nodes must allowed to have tags
      isImmutableMap(properties)
    ) {
      let iteratorIndex = flattenNodes.length - 1;

      while (
        flattenNodes[iteratorIndex] &&
        isObject(flattenNodes[iteratorIndex])
      ) {
        if (iteratorIndex < 1) break; // At least 2 nodes must allowed to have tags

        const { nodeId, type, parentId } = flattenNodes[iteratorIndex];

        const isDestination = type === DESTINATION;
        const isNodeFilter = type === FILTER;
        const isConditionYes = type === NODE_TYPE.CONDITION_YES;
        const isNodeWaitEvent = type === NODE_TYPE.WAIT_EVENT;

        /* Node wait event section */
        if (isNodeWaitEvent && isActBaseTrigger) {
          const isInit = nodes.getIn([
            nodeId,
            'peformEvent',
            'data-init',
            'isInit',
          ]);
          const performEvents = isInit
            ? nodes.getIn([nodeId, 'peformEvent', 'data-init', 'backup'], [])
            : nodes.getIn([nodeId, 'peformEvent']);

          if (!processPerformEvents(performEvents, triggerEventBased)) {
            errors[nodeId] = true;
          }
        }

        /* Node destination section */
        if (isDestination) {
          const currentNodeTags = properties.getIn(
            ['nodes', nodeId, 'tags'],
            {},
          );

          if (!isEmpty(currentNodeTags)) {
            const { isValidate, errors: tagErrors } = validateNodeTags({
              parentId,
              nodeId,
              nodes,
              properties,
              flattenNodes,
              tags: currentNodeTags,
            });

            if (!isValidate) {
              errors[nodeId] = true;

              tagErrorInfo.push({
                nodeId,
                tagErrors,
              });
            }
          }
        }

        /* Node filter or condition yes section */
        if (isNodeFilter || isConditionYes) {
          const fullEventTracking = yield select(makeSelectFullEventTracking());
          const tagAndEvents = collectAllocatedPersonalizeTagAndEvents({
            activeNode: flattenNodes[iteratorIndex],
            properties,
            nodes,
            flattenNodes,
            fullEventTracking,
          });

          const listEvents = tagAndEvents?.events?.list;

          const isValidEvent = processFilterPerformEventsWithProperty({
            nodes,
            nodeId,
            listEvents,
          });

          if (
            !processFilterPerformEvents({
              nodes,
              nodeId,
              properties,
            }) ||
            !isValidEvent
          ) {
            errors[nodeId] = true;
          }
        }

        iteratorIndex -= 1;
      }
    }

    if (tagErrorInfo.length > 0) {
      const prefix = args?.moduleConfig?.key;
      yield put(updateValue(`${prefix}@@ERROR_TAG_PROPERTY@@`, tagErrorInfo));
    }

    return { isValidate: isEmpty(errors), errors };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'handleValidateTagRuleBeforeSave',
      data: {
        error: error.stack,
        args,
      },
    });
    return { isValidate: false, errors: {} };
  }
}

export function* handleValidateEventBeforeSave(args) {
  try {
    const reducer = yield select(state =>
      selectDomainMainCreateWorkflow(state, args),
    );

    const { configure } = reducer;
    const { flattenNodes, properties, nodes } = configure.main;

    const errors = [];

    if (
      isArray(flattenNodes) &&
      flattenNodes.length >= 2 && // At least 2 nodes must allowed to have case remove event
      isImmutableMap(properties)
    ) {
      let iteratorIndex = flattenNodes.length - 1;

      while (
        flattenNodes[iteratorIndex] &&
        isObject(flattenNodes[iteratorIndex])
      ) {
        if (iteratorIndex < 1) break; // At least 2 nodes must allowed to have case remove event

        const { nodeId, type } = flattenNodes[iteratorIndex];

        const isNodeFilter = type === FILTER;
        const isConditionYes = type === NODE_TYPE.CONDITION_YES;

        const tagAndEvents = yield select(
          makeCollectAllocatedPersonalizeTagAndEventsModuleConfig(
            args?.moduleConfig,
          ),
        );

        const listEvents = tagAndEvents?.events?.list;

        /* Node filter or condition yes section */
        if (
          (isNodeFilter || isConditionYes) &&
          !processFilterPerformEventsWithProperty({
            nodes,
            nodeId,
            listEvents,
          })
        ) {
          errors.push({
            nodeId,
            message: 'Please select at least one event',
          });
        }

        iteratorIndex -= 1;
      }
    }

    return { isValidate: isEmpty(errors), errors };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'handleValidateTagRuleBeforeSave',
      data: {
        error: error.stack,
        args,
      },
    });
    return { isValidate: false, errors: {} };
  }
}

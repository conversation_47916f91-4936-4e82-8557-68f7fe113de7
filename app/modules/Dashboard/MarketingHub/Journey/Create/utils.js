/* eslint-disable no-nested-ternary */
/* eslint-disable no-restricted-syntax */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
/* eslint-disable camelcase */
/* eslint-disable prefer-destructuring */
import _, {
  cloneDeep,
  flatMap,
  flattenDeep,
  get,
  isEmpty,
  map,
  pick,
  reduce,
  set,
  isArray,
} from 'lodash';
import moment from 'moment';
import isEqual from 'react-fast-compare';
import { toPng } from 'html-to-image';

import MediaService from 'services/Media';

import { addMessageToQueue } from 'utils/web/queue';
import APP from '../../../../../appConfig';
import { getTranslateMessage } from '../../../../../containers/Translate/util';
import TRANSLATE_KEY from '../../../../../messages/constant';
import { generateKey, safeParse } from '../../../../../utils/common';
import { getBase64FromUrl, random } from '../../../../../utils/web/utils';
import {
  convertBlastToJourney,
  convertBlastToThirdPartyCampaigns,
} from './BlastCampaign/utils';
import {
  mapCampaignSettingToFe,
  toEntryReducer,
} from './Content/Nodes/Destination/utils';
import { initVariants } from './Content/Nodes/Destination/utils.state';
import { NODE_DESTINATION_TYPE, NODE_TYPE } from './Content/Nodes/constant';
import {
  adjustTagIfNeeded,
  getRootCustomInputs,
  mapInputToAPI,
} from './_reducer/utils';
import { BREADCRUMDATA } from './config';
import { serializeWorkflowSettingAPI } from './utils.map';
import { getVersionInfo } from './utils.version';
import {
  getDateRange,
  labelFromFrequencyValue,
} from './_UI/modals/ModalConfirmActive/utils';
import {
  toUIDelayAmountOfTime,
  toUIDelayDateOrTime,
} from '../../../../../components/common/UIDelay/utils';
import {
  makeSelectListActionHistoryDomainMain,
  makeSelectListActionHistoryFilter,
  makeSelectListActionHistoryTable,
} from '../Detail/ScheduleHistory2/Detail/List/selectors';
import {
  makeSelectListActionsHistoryDomainMain,
  makeSelectListActionsHistoryFilter,
  makeSelectListActionsHistoryTable,
} from '../Detail/ActionsHistory/ListProcess/selectors';
import {
  makeSelectListActionsHistoryDomainMainAllNodes,
  makeSelectListActionsHistoryFilterAllNodes,
  makeSelectListActionsHistoryTableAllNodes,
} from '../Detail/ActionsHistory/NodesList/selectors';
import {
  makeSelectListScheduleHistoryDomainMain,
  makeSelectListScheduleHistoryFilter,
  makeSelectListScheduleHistoryTable,
} from '../Detail/ScheduleHistory2/Detail/NodesList/selectors';
import { makeSelectDateRangeSchedule } from '../Detail/ScheduleHistory2/Detail/selectors';
import { makeSelectDateRangeAction } from '../Detail/ActionsHistory/selectors';

const PATH = 'app/modules/Dashboard/MarketingHub/Journey/Create/utils.js';

export function getBreadcrums(channelActive) {
  // const data = BREADCRUMDATA;
  // console.log('object', name);
  const tmp = {
    key: '3',
    display: channelActive.label,
    urlPath: {
      first: `${APP.PREFIX}/`,
      last: `/marketing-hub/journeys/${channelActive.value}`,
    },
  };
  const tmpCreate = {
    key: 4,
    display: getTranslateMessage(
      TRANSLATE_KEY._BREADCRUMB_CREATE_NEW_STORY,
      'Create new story',
    ),
  };
  return [...BREADCRUMDATA, tmp, tmpCreate];
}

export function toAPI({
  main,
  design,
  configure,
  channelActive,
  feKeyVariantId,
  versionMT,
  versionId,
  rootJourneyDetail,
  copyId,
  campaigns,
  trigger,
  isBlastCampaign = false,
  isThirdPartyCampaigns = false,
  withWorkflowInfo = false,
}) {
  const generatedByTactic = !isEmpty(main.tactic);

  let data = {
    channel_id: channelActive.value,
    workflow_setting: [],
    trigger_type: '',
    start_date: null,
    end_date: null,
    description: '',
    properties: {},
    custom_inputs: {},
    variantThumbnails: [],

    ...(generatedByTactic && {
      tactic_id: main.tactic.templateId,
      tactic_name: main.tactic.templateName,
    }),
  };

  if (design === 'create') {
    data.story_name = configure.main.name.trim();
  }

  // console.log('configure.main.customInputs', configure.main.customInputs);
  let dataWorkflowSetting;
  let inputAPI;

  if (isBlastCampaign) {
    if (isThirdPartyCampaigns) {
      const {
        workflow_setting,
        custom_inputs,
      } = convertBlastToThirdPartyCampaigns({
        campaigns,
        trigger,
        design,
        // thumbnails: data.variantThumbnails,
        nodesInfo: configure.main.nodes,
      });

      dataWorkflowSetting = [workflow_setting];
      inputAPI = custom_inputs;
    } else {
      const { workflow_setting, custom_inputs } = convertBlastToJourney({
        campaigns,
        trigger,
        design,
        // thumbnails: data.variantThumbnails,
        nodesInfo: configure.main.nodes,
      });

      dataWorkflowSetting = [workflow_setting];
      inputAPI = custom_inputs;
    }

    data.isBlastCampaign = isBlastCampaign;
  } else {
    dataWorkflowSetting = serializeWorkflowSettingAPI(
      configure.main.treeNodes,
      configure.main.nodes,
      design,
      feKeyVariantId,
      channelActive,
      versionMT,
      copyId,
      // data.variantThumbnails,
    );

    inputAPI = mapInputToAPI(configure.main.customInputs);
  }

  const rootCustomInput = getRootCustomInputs(
    rootJourneyDetail.custom_inputs,
    inputAPI,
  );

  data.workflow_setting = dataWorkflowSetting[0];

  // TODO: set the last selected viewObject for BlastCampaign
  if (campaigns?.[0]?.audiences?.includedAudiences?.viewObject) {
    data.workflow_setting.metadata.audiences.viewObject =
      campaigns[0].audiences.includedAudiences.viewObject;
  }

  const triggerNode = dataWorkflowSetting[0];

  data.trigger_type = triggerNode.actionType;
  data.start_date = triggerNode.metadata.startTime || null;
  data.end_date = triggerNode.metadata.endTime || null;
  data.custom_inputs = inputAPI;

  if (versionId) {
    data.version_id = versionId;
  }

  const workflowSetting = set(
    data.workflow_setting,
    'metadata.custom_inputs',
    data.custom_inputs,
  );
  const originalWorkflowSetting = rootJourneyDetail.workflow_setting
    ? set(
        rootJourneyDetail.workflow_setting,
        'metadata.custom_inputs',
        rootCustomInput,
      )
    : {};

  data.version_info = getVersionInfo({
    workflowSetting,
    originalWorkflowSetting,
    isBlastCampaign,
    design,
  });

  if (withWorkflowInfo) {
    data = { ...data, originalWorkflowSetting, workflowSetting };
  }

  return data;
}

export function getParamsFromURL(url) {
  const data = {
    id: '',
    tab: '',
  };
  // const url = window.location.href;
  const arr = url.split('/update/');
  if (arr.length === 2) {
    const arrTempt = arr[1].split('/');
    if (arr.length === 2) {
      data.id = arrTempt[0];
      data.tab = arrTempt[1];
    }
  }
  return data;
}

export const getStoryId = (dataReducer, design = 'create') => {
  let storyId = 0;

  const { activeRow } = dataReducer;

  if (Object.keys(activeRow).length === 0) {
    return storyId;
  }

  if (design === 'create') {
    storyId = activeRow.activeId;
  } else {
    storyId = activeRow.active_id;
  }

  return storyId;
};

export const renderContentPopupConfirm = (dataReducer, design = 'create') => {
  const { activeRow } = dataReducer;

  let title = getTranslateMessage(
    TRANSLATE_KEY._TITL_CANCEL_CREATE_STORY,
    'Cancel creating of this story',
  );

  let content = getTranslateMessage(
    TRANSLATE_KEY._WARN_CANCEL_CREATE_STORY,
    'Once you confirm cancel, this story will NOT be saved',
  );

  if (Object.keys(activeRow).length === 0) {
    return { title, content };
  }

  const storyId = getStoryId(dataReducer, design);

  if (storyId && parseInt(storyId) > 0) {
    title = getTranslateMessage(
      TRANSLATE_KEY._BOX_TITL_DISCARD_CHANGE,
      'Discard changes?',
    );

    content = getTranslateMessage(
      TRANSLATE_KEY._WARN_DISCARD_CHANGE_INTRO,
      "Change you created won't be saved if you leave this site",
    );
  }

  return { title, content };
};

export const mapWorkFlowSettingWithChannelId = (channelId, node) => {
  const today = new Date();
  const curentMonth =
    today.getMonth() + 1 < 10
      ? `0${today.getMonth() + 1}`
      : today.getMonth() + 1;
  const curentDate =
    today.getDate() < 10 ? `0${today.getDate()}` : today.getDate();
  const curentYear = today.getFullYear();

  const curentHours = today.getHours();
  const curentMinute = today.getMinutes();

  const timeStamp = today.getTime();
  const catalogId = getDatalogIdWebEmbe(node);
  const date = `${curentYear}-${curentMonth}-${curentDate}`;
  const MAP = {
    2: {
      label: 'Action-based Trigger',
      actionId: generateKey(),
      actionType: 'EVENT_BASED',
      metadata: {
        endCondition: 'never',
        startDate: date,
        startTimeOfDay: {
          hour: curentHours,
          minute: curentMinute,
        },
        endDate: null,
        endTimeOfDay: null,
        startTime: timeStamp,
        endTime: null,
        event: {},
        frequencyCapping: null,
        timeOfWeek: [],
        triggerType: 'all',
      },
      // branchs: [
      //   {
      //     label: 'END',
      //     actionId: generateKey(),
      //     actionType: 'END',
      //     metadata: {},
      //     branchs: [],
      //   },
      // ],
      branchs: [
        {
          actionId: generateKey(),
          actionType: 'DESTINATION',
          label: 'Web Personalize',
          catalogCode: NODE_DESTINATION_TYPE.WEB_EMBEDDED,
          branchs: [
            {
              label: 'END',
              actionId: generateKey(),
              actionType: 'END',
              metadata: {},
              branchs: [],
            },
          ],
          metadata: { catalogId: Number(catalogId) },
        },
      ],
    },
    3: {
      label: 'Scheduled Trigger',
      actionId: generateKey(),
      actionType: 'SCHEDULED',
      metadata: {
        triggerType: 'specific_date',
        frequency: 'once',
        startDate: date,
        startTime: timeStamp,
        startTimeOfDay: {
          hour: curentHours,
          minute: curentMinute,
        },
        repeatInterval: 1,
        endCondition: 'never',
        endDate: null,
        endTime: null,
        endTimeOfDay: null,
        audiences: {},
        frequencyCapping: null,
      },
      branchs: [
        {
          label: 'END',
          actionId: generateKey(),
          actionType: 'END',
          metadata: {},
          branchs: [],
        },
      ],
    },
  };
  if (channelId === 2) {
    return MAP[channelId];
  }
  return MAP[3];
};
const getDatalogIdWebEmbe = node => {
  let keys = '';
  Object.keys(node).forEach(key => {
    if (node[key].catalogCode === 'web_embedded' && node[key].channelId === 2) {
      keys = key;
    }
  });
  return keys;
};

export const adjustVariantInfoIfNeeded = ({
  initDataVariants = {},
  duplicatedNodes = [],
}) => {
  try {
    if (!isArray(initDataVariants?.list)) return initDataVariants;

    const { list, cacheInfo } = initDataVariants;

    const newCacheInfo = Object.fromEntries(
      list.map(({ value: variantId }) => {
        const cacheInfoVariant = cloneDeep(cacheInfo?.[variantId] || {});
        const pathTags = ['variantExtraData', 'properties', 'tags'];

        // Adjust tags if needed
        const adjustedTags = adjustTagIfNeeded({
          duplicatedNodes,
          duplicatedTags: get(cacheInfoVariant, pathTags, {}),
        });

        set(cacheInfoVariant, pathTags, adjustedTags);
        return [variantId, cacheInfoVariant];
      }),
    );

    return { ...initDataVariants, cacheInfo: newCacheInfo };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'adjustVariantInfoIfNeeded',
      data: {
        error: error.stack,
        args: { initDataVariants, duplicatedNodes },
      },
    });
    return initDataVariants;
  }
};

export const setCampaignVariantInfo = (
  newNode,
  variantInfo,
  camPaignInfo,
  duplicatedNodeIds,
) => {
  if (newNode.type === 'DESTINATION') {
    const campaignId = newNode.map.get('destination').campaignId;
    const variantIds = newNode.map.get('destination').variantIds;
    const catalogId = newNode.map.get('destination').catalogId;
    const contentPlacement = newNode.map.get('destination').contentPlacement;
    const zoneId = newNode.map.get('destination').zoneId;
    const channelId = newNode.map.get('destination').channelId;
    const destinationId = newNode.map.get('destination').destinationId;
    const priority = newNode.map.get('destination').priority;
    const isFitContent = newNode.map.get('destination').isFitContent;
    const variantSetting = [];
    // set campaign setting
    camPaignInfo.forEach(data => {
      if (data.campaign_id === campaignId) {
        newNode.campaignInfo = data;
      }
    });
    // set variant setting
    variantInfo.forEach(data => {
      if (variantIds.includes(data.variant_id)) {
        variantSetting.push(data);
      }
    });
    const initDataVariants = initVariants('RESET', variantSetting, '---');
    const cacheInfoVariant =
      initDataVariants.cacheInfo[initDataVariants.activeId] || {};
    const destinationInput = safeParse(cacheInfoVariant.destinationInput, {});

    Object.keys(initDataVariants.cacheInfo).forEach(key => {
      // const workspaces = _.cloneDeep(
      //   serializeDataInputAttrs({
      //     customInputAttributes:
      //       resCustomInputVariant.data[0].customInputAttributes,
      //     positions: resCustomInputVariant.data[0].positions,
      //   }),
      // );

      initDataVariants.cacheInfo[key].customInputVia = {
        workspaces: [],
      };
      // templateWorkspaces = _.cloneDeep(workspaces);
    });
    newNode.variantInfo = adjustVariantInfoIfNeeded({
      initDataVariants,
      duplicatedNodes: duplicatedNodeIds,
    });
    newNode.campaignSetting = mapCampaignSettingToFe({
      variant: newNode.variantInfo,
      campaign: { ...newNode.campaignInfo.campaign_setting },
      role: 'RESET',
    });
    const dataIn = {
      role: 'RESET',
      isFetchInfoData: false,
      isCopy: true,
      campaignName: { value: newNode.campaignInfo.campaign_name },
      workflowDestination: {
        value: { value: destinationId },
      },
      timeTarget: newNode.campaignInfo.campaign_setting.deliveryTimeConfig || {
        triggerType: 'all_time',
        range: [],
        mode: 'delay',
      },
      campaignCustomInput: {
        templateCustomInput: {},
        workspaces: [],
        customInputs: {},
      },
      destinationInput,
      catalogId,
      lookup: {
        campaignId,
        variantIds,
      },
      campaignId: null,
      channelId,
      status: 1,
      isValidate: false,
      channelCode: newNode.channelCode,
      catalogCode: newNode.catalogCode,
      testingAudienceIds: [],
      variants: newNode.variantInfo,
      variantIds,
      zoneId: { value: { value: zoneId } },
      contentPlacement: { value: { value: contentPlacement } },
      campaignSetting: newNode.campaignSetting,
      priority: { value: priority },
      isFitContent: { value: isFitContent },
    };
    const dataNode = toEntryReducer(dataIn);
    newNode.map = newNode.map.setIn(['destination'], dataNode);
  }
  if (newNode.branchs && newNode.branchs.length > 0) {
    newNode.branchs.forEach(item => {
      newNode = setCampaignVariantInfo(
        item,
        variantInfo,
        camPaignInfo,
        duplicatedNodeIds,
      );
    });
  }
  return newNode;
};

export const buildDataParam = data => {
  if (data.length) {
    const dataSegments = data.map(node => node.segmentIds);
    const dataList = _.flatten(dataSegments);
    const param = {
      // itemTypeId: -1003,
      audienceTypes: 'segmentAudiences',
      audienceIds: dataList,
      dependencies: [],
    };

    return param;
  }

  return {};
};

export function getInitValidatedFieldFromNodes(nodes, flattenNodes) {
  const dataMapHandle = new Map(nodes);

  const segmentsByNodes = {};
  const attributesByNodes = {};
  const eventAttributesByNodes = {};

  flattenNodes.forEach(node => {
    const { type, nodeId } = node;
    const metaDataNode = dataMapHandle.get(nodeId);

    if (type === NODE_TYPE.SCHEDULED) {
      const data = metaDataNode.get('targetAudience');

      if (data && data.backup) {
        segmentsByNodes[nodeId] = getSegmentIdsFromTargetAudience(data);
      }
    }

    if (type === NODE_TYPE.EVENT_BASED && type === NODE_TYPE.WAIT_EVENT) {
      const performEvent = metaDataNode.get('peformEvent');

      eventAttributesByNodes[nodeId] = getAttributesFromPerformEvent(
        performEvent,
        mapEventAttributes,
      );
    }

    if (type === NODE_TYPE.UPDATE_SEGMENT) {
      const data = metaDataNode.get('updateSegment');

      segmentsByNodes[nodeId] = data.segmentIds;
    }

    if (type === NODE_TYPE.FILTER || type === NODE_TYPE.CONDITION_YES) {
      const data = metaDataNode.get('filter');
      const filterType = data.filterType.value;

      if (!data) return;

      if (filterType === 'item_segment' && data.item_segment.backup) {
        segmentsByNodes[nodeId] = getSegmentIdsFromFilter(data);
      }

      if (filterType === 'user_attributes' && data.user_attributes.rules) {
        const rules = data.user_attributes.rules;

        attributesByNodes[nodeId] = getAttributesFromFilterRules(
          rules,
          mapAttributes,
        );
      }

      if (filterType === 'event_attribute' && data.event_attribute.rules) {
        const rules = data.event_attribute.rules;

        eventAttributesByNodes[nodeId] = getAttributesFromFilterRules(
          rules,
          mapEventAttributes,
        );
      }
    }
  });

  return {
    segmentsByNodes,
    attributesByNodes,
    eventAttributesByNodes,
  };
}

const mapEventAttributes = item => ({
  itemTypeId: item.item_type_id || item.itemTypeId,
  eventPropertyName: item.column,
});

const mapAttributes = item => ({
  itemTypeId: item.itemTypeId,
  itemPropertyName: item.column,
});

const getAttributesFromFilterRules = (rules, mapFn) => {
  const conditions = safeParse(rules.getIn(['data-init', 'backup']), {});
  const rulesOR = safeParse(conditions.OR, []);

  const flattenItem = flattenDeep(rulesOR.map(item => safeParse(item.AND, [])));

  return map(flattenItem, mapFn);
};

const getAttributesFromPerformEvent = (performEvent, mapFn) => {
  let backup = performEvent.getIn(['data-init', 'backup']);

  if (!Array.isArray(backup)) backup = [backup];

  const rulesOR = map(backup, item => item.filters.OR || []);
  const ruleAND = flattenDeep(rulesOR);
  const flattenItem = flattenDeep(ruleAND.map(item => item.AND || []));

  return map(flattenItem, mapFn);
};

const getSegmentIdsFromTargetAudience = data => {
  const excludedAudienceFilters = data.backup.excludedAudiences.filters;
  const includedAudienceFilters = data.backup.includedAudiences.filters;

  return [...excludedAudienceFilters.OR, ...includedAudienceFilters.OR].map(
    item => item.AND[0].value[0],
  );
};

const getSegmentIdsFromFilter = data => {
  const backup = data.item_segment.backup;

  const filters = safeParse(backup.filters, { OR: [] });
  const excludeFilters = safeParse(backup.excludedFilters, { OR: [] });

  return [...filters.OR, ...excludeFilters.OR].map(item => item.AND[0].value);
};

export const MAP_TYPE_RESUME = {
  whole_process: 'TRIGGER_NODE',
  from_node: 'FROM_NODE',
  specific_node: 'AT_NODES',
};

export const getNodeId = data => {
  const dataOut = [];
  data.forEach(each => {
    dataOut.push(each.nodeId);
  });
  return dataOut;
};

export const convertNodesToFlattenNodes = ({
  node,
  flattenNodes = [],
  parentId,
  journeyDetail,
}) => {
  try {
    const { branchs, actionType } = node;
    // change nodeInfo instead of node
    const nodeInfo = cloneDeep(node);

    if (actionType === NODE_TYPE.END) {
      return flattenNodes;
    }

    // add, change, remove keys
    if (parentId) {
      nodeInfo.parentId = parentId;
    }

    // fix error custom_inputs not in triggerNode
    if ([NODE_TYPE.EVENT_BASED, NODE_TYPE.SCHEDULED].includes(actionType)) {
      _.set(nodeInfo, 'custom_inputs', journeyDetail.custom_inputs);
    }

    delete nodeInfo.branchs;

    flattenNodes.push(nodeInfo);
    // end of: add, change, remove keys

    if (branchs && branchs.length) {
      for (const branchNode of branchs) {
        convertNodesToFlattenNodes({
          node: branchNode,
          flattenNodes,
          parentId: nodeInfo.actionId,
          journeyDetail,
        });
      }
    }

    return flattenNodes;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'convertNodesToFlattenNodes',
      data: error.stack,
    });

    return flattenNodes;
  }
};

export const NodeModifiedHelper = (() => {
  let instance;

  function defaultCompareFn(data1, data2, fields = []) {
    const compareAll = fields.length === 0;

    const selectedFields1 = compareAll ? data1 : pick(data1, fields);
    const selectedFields2 = compareAll ? data2 : pick(data2, fields);

    // console.log(selectedFields1);
    // console.log(selectedFields2);

    return isEqual(selectedFields1, selectedFields2);
  }

  const MODIFIED_INFO_TO_HTML_HELPER = {
    [NODE_TYPE.SCHEDULED]: {
      UPDATE: [
        function updateScheduleTime(
          node,
          rootNode,
          compareFn = defaultCompareFn,
        ) {
          const { metadata } = node;
          const { metadata: rootMetadata } = rootNode;

          const isModified = !compareFn(metadata, rootMetadata, [
            'endDate',
            'endTime',
            'frequency',
            'startDate',
            'startTime',
            'triggerType',
            'endCondition',
            'endTimeOfDay',
            'repeatInterval',
            'startTimeOfDay',
          ]);

          if (!isModified) return { html: [] };

          const html = [];

          const { startTimeString, endTimeString } = getDateRange(metadata);

          if (
            metadata.frequency === 'once' &&
            metadata.triggerType === 'specific_date'
          ) {
            html.push(`<b>Once</b> - <b>${startTimeString}</b>`);
            return { html };
          }

          if (
            metadata.frequency === 'once' &&
            metadata.triggerType === 'after_activate'
          ) {
            html.push('<b>Once</b> - <b>Right after the journey activated<b/>');
            return { html };
          }

          let dateRangeString = `From ${startTimeString}`;

          if (endTimeString) {
            dateRangeString = `${dateRangeString} to ${endTimeString}`;
          }

          html.push(
            `<b>By ${labelFromFrequencyValue(metadata.frequency)}</b> - <b>${dateRangeString}</b>`, // prettier-ignore
          );

          return { html };
        },
      ],
    },
    [NODE_TYPE.EVENT_BASED]: {
      UPDATE: [
        function updateScheduleTime(
          node,
          rootNode,
          compareFn = defaultCompareFn,
        ) {
          const { metadata } = node;
          const { metadata: rootMetadata } = rootNode;

          const isModified = !compareFn(metadata, rootMetadata, [
            'endDate',
            'endTime',
            'startDate',
            'startTime',
            'endCondition',
            'endTimeOfDay',
            'startTimeOfDay',
          ]);

          if (!isModified) return { html: [] };

          const { startTimeString, endTimeString } = getDateRange(metadata);

          let dateRangeString = `From ${startTimeString}`;

          if (endTimeString) {
            dateRangeString += ` to ${endTimeString}`;
          }

          return { html: [`<b>${dateRangeString}</b>`] };
        },
      ],
    },
    [NODE_TYPE.WAIT_EVENT]: {
      UPDATE: [
        function updateWaitingTime(
          node,
          rootNode,
          compareFn = defaultCompareFn,
        ) {
          const { metadata } = node;
          const { metadata: rootMetadata } = rootNode;

          const isModified = !compareFn(metadata, rootMetadata, [
            'waitingTimeout',
          ]);

          if (!isModified) return { html: [] };

          const { days, hours, minutes, seconds } = toUIDelayAmountOfTime(
            metadata.waitingTimeout,
          );

          function format(number, label) {
            if (number === 0) return '';

            return `${number} ${label}(s)`;
          }

          // prettier-ignore
          return { html: [`Waiting for <b>${format(days, 'day')} ${format(hours, 'hour')} ${format(minutes, 'minute')} ${format(seconds, 'second')}</b>`] }
        },
      ],
    },
    [NODE_TYPE.DELAY]: {
      UPDATE: [
        function baseUpdateDeley(node, rootNode, compareFn = defaultCompareFn) {
          const { metadata } = node;
          const { metadata: rootMetadata } = rootNode;

          const isModified = !compareFn(metadata, rootMetadata);

          if (!isModified) return { html: [] };

          let content = '';

          function format(number, label) {
            if (number === 0) return '';

            return `${number} ${label}(s)`;
          }

          switch (metadata.delayType) {
            case 'amount_of_time': {
              const { days, hours, minutes, seconds } = toUIDelayAmountOfTime(
                metadata.data,
              );

              return { html: [`Delay for <b>${format(days, 'day')} ${format(hours, 'hour')} ${format(minutes, 'minute')} ${format(seconds, 'second')}</b>`] } // prettier-ignore
            }
            case 'date_or_time': {
              const { timeUnit, ...rest } = toUIDelayDateOrTime(metadata.data);

              if (timeUnit === 'DAYS_OF_WEEK_CUSTOM') {
                content = 'days of week';
              }

              if (timeUnit === 'DATE') {
                content = `<b>${moment(rest.date).format("LL")} ${moment(rest.timeOfDay).format('LT')}</b>` // prettier-ignore
              }

              if (timeUnit === 'SAME_DAY') {
                content = `<b>${moment(rest.timeOfDay).format('LT')} on same day audience enters the node</b`; // prettier-ignore
              }

              return { html: [`Delay until ${content}`] };
            }
            default:
              break;
          }

          return { html: [] };
        },
      ],
    },
  };

  function getUpdatedInfoInHtmlContent({ node, rootNode }) {
    try {
      const nodeType = node.type || node.actionType;

      const update =
        get(MODIFIED_INFO_TO_HTML_HELPER, `${nodeType}.UPDATE`) || [];

      return {
        html: update
          .map(getHtmlFn => getHtmlFn(node, rootNode))
          .reduce((acc, value) => {
            acc.push(...value.html);

            return acc;
          }, [])
          .map(html => `Updated: ${html}`),
      };
    } catch (err) {
      console.log(err);

      addMessageToQueue({
        path: 'app/modules/Dashboard/MarketingHub/Journey/Create/utils.js',
        func: getUpdatedInfoInHtmlContent.name,
        data: err.stack,
      });

      return { html: [] };
    }
  }

  function getInstance() {
    if (!instance) {
      // Init instance
      instance = {
        getUpdatedInfoInHtmlContent,
      };
    }

    return instance;
  }

  return {
    getInstance,
  };
})();

export const captureWorkflowThumbnail = async () => {
  try {
    const FLOWCHART_CAPTURE_SELECTOR =
      '#flowchart-wrapper > div > div:last-child';
    const ACTIVE_NODE_SELECTOR = '.dataflow-chart .label-node > div.active';

    // Remove active node style
    const activeNode = document.querySelector(ACTIVE_NODE_SELECTOR);
    if (activeNode) {
      activeNode.classList.remove('active');
    }

    const flowChartThumbnail = await toPng(
      document.querySelector(FLOWCHART_CAPTURE_SELECTOR),
      {
        quality: 1,
        backgroundColor: '#fcfdff',
        pixelRatio: 3,
        skipAutoScale: true,
        fontEmbedCSS: window.FONT_EMBED_CSS,
      },
    );

    // Reactive node
    if (activeNode) {
      activeNode.classList.add('active');
    }

    return flowChartThumbnail;
  } catch (error) {
    return '';
  }
};

export const handleUploadWorkflowThumbs = async ({
  isBlastCampaign = false,
  createCopy = false,
  captureWorkspaceOnly = false,
  captureWorkspace = true,
  variantThumbnails = [],
  inplace = false,
}) => {
  let workflowThumbnail;
  const cloneVariantThumbnails = inplace
    ? variantThumbnails
    : cloneDeep(variantThumbnails || []);

  if (!isBlastCampaign && captureWorkspace) {
    workflowThumbnail = await captureWorkflowThumbnail();
  }

  if (
    (cloneVariantThumbnails && cloneVariantThumbnails.length) ||
    workflowThumbnail
  ) {
    // const formData = new FormData();
    if (!isBlastCampaign && workflowThumbnail) {
      const currentWorkflowThumb = cloneVariantThumbnails.find(
        item => !item.variantId,
      );

      if (currentWorkflowThumb) {
        currentWorkflowThumb.url = workflowThumbnail;
      } else {
        cloneVariantThumbnails.unshift({
          id: `jn_thumb_${random(10)}`,
          url: workflowThumbnail,
        });
      }
    }

    if (captureWorkspaceOnly) {
      return cloneVariantThumbnails;
    }

    if (createCopy) {
      cloneVariantThumbnails.forEach(item => {
        if (item.id.length > 20) {
          item.id = item.id
            .split('-')
            .slice(0, -1)
            .join('-');
        }
        item.id = `${item.id}-${random(8)}`;
      });

      const uploadedThumbs = cloneVariantThumbnails.filter(item =>
        item.url.startsWith('http'),
      );

      const resultBase64 = await Promise.all(
        uploadedThumbs.map(thumb => getBase64FromUrl(thumb.url)),
      );

      uploadedThumbs.forEach(
        (item, idx) => (item.url = resultBase64[idx] || item.url),
      );
    }

    const imagesToUpload = cloneVariantThumbnails.filter(item =>
      item.url.startsWith('data:image'),
    );

    if (!imagesToUpload.length) {
      return cloneVariantThumbnails;
    }

    // const blobArr = await Promise.all(
    //   imagesToUpload.map(({ id, url }) => {
    //     return new Promise(resolve => {
    //       fetch(url)
    //         .then(res => res.blob())
    //         .then(blob => resolve({ id, blob }));
    //     });
    //   }),
    // );

    // blobArr.map(async ({ id, blob }) => {
    //   const blobFile = new File([blob], id, {
    //     type: 'image/png',
    //   });
    //   formData.append('files', blobFile);
    // });

    // const params = { form: formData };
    const arrResult = await Promise.all(
      imagesToUpload.map(({ id, url }) =>
        MediaService.mediaLibrary.uploadBase64Image({
          data: url,
          name: id,
          type: 'base64-img',
        }),
      ),
    );

    arrResult.forEach((res, idx) => {
      if (res.code === 200 && res.data.file_path) {
        imagesToUpload[idx].url = res.data.file_path;
      }
    });

    return cloneVariantThumbnails
      .filter(item => item.url.startsWith('http'))
      .map(item => ({
        ...item,
        url: `${item.url}${
          item.url.includes('?') ? '&' : '?'
        }type=${item.type || 'tactic'}`,
      }));
  }

  return [];
};

export const getListVariantId = (node, arrIds = []) => {
  try {
    if (node.actionType === 'END' || Object.keys(node).length === 0) {
      return arrIds;
    }

    if (node.branchs && node.branchs.length) {
      node.branchs.forEach(childNode => {
        if (childNode.actionType === NODE_TYPE.DESTINATION) {
          const { variants, variantIds } = childNode.metadata;

          if (Array.isArray(variantIds)) {
            variantIds.forEach(id => arrIds.push(id));
          } else if (Array.isArray(variants)) {
            variants.forEach(variant => {
              arrIds.push(variant.variantId);
            });
          }
        }

        getListVariantId(childNode, arrIds);
      });
    }
  } catch (error) {
    addMessageToQueue({
      path: 'app/modules/Dashboard/MarketingHub/Journey/Create/utils.js',
      func: 'getListVariantId',
      data: error.stack,
    });
  }

  return arrIds;
};

export const MAP_SELECTOR = {
  'story-schedule-detail-list': {
    reducerFilter: makeSelectListActionHistoryFilter(),
    reducerMain: makeSelectListActionHistoryDomainMain(),
    reducerTable: makeSelectListActionHistoryTable(),
    reducerDateRange: makeSelectDateRangeSchedule(),
  },
  'story-detail-action-history-list': {
    reducerFilter: makeSelectListActionsHistoryFilter(),
    reducerMain: makeSelectListActionsHistoryDomainMain(),
    reducerTable: makeSelectListActionsHistoryTable(),
    reducerDateRange: makeSelectDateRangeAction(),
  },
  'action-log-history-list': {
    reducerFilter: makeSelectListActionsHistoryFilterAllNodes(),
    reducerMain: makeSelectListActionsHistoryDomainMainAllNodes(),
    reducerTable: makeSelectListActionsHistoryTableAllNodes(),
    reducerDateRange: makeSelectDateRangeAction(),
  },
  'story-nodes-detail-list': {
    reducerFilter: makeSelectListScheduleHistoryFilter(),
    reducerMain: makeSelectListScheduleHistoryDomainMain(),
    reducerTable: makeSelectListScheduleHistoryTable(),
    reducerDateRange: makeSelectDateRangeSchedule(),
  },
};

export function isNestedErrorDifferent(error1, error2) {
  const getNestedKeys = obj =>
    flatMap(Object.keys(obj || {}), key => Object.keys(obj[key] || {}));

  return !isEqual(getNestedKeys(error1), getNestedKeys(error2));
}
export const getListTagOptions = data => {
  return reduce(
    data,
    (result, value) => {
      const tags = map(value.tags, (tag, tagKey) => ({
        id: tagKey,
        displayName: tag.displayName,
        type: tag.type,
      }));

      tags.forEach(tag => {
        result[tag.id] = tag;
      });

      return result;
    },
    {},
  );
};

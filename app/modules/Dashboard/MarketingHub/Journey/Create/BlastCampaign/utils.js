/* eslint-disable no-plusplus */
/* eslint-disable no-unused-vars */
/* eslint-disable prefer-destructuring */
/* eslint-disable indent */
/* eslint-disable consistent-return */
/* eslint-disable camelcase */
/* eslint-disable no-restricted-syntax */
import { get, isEmpty, isArray } from 'lodash';
import {
  generateKey,
  getObjectPropSafely,
  random,
  safeParse,
} from 'utils/common';
import { isMap as isImmutableMap } from 'immutable';
import { addMessageToQueue } from 'utils/web/queue';
import { PortalDate } from 'utils/date';
import { CHANNEL } from '../../constant';
import {
  DEFAULT_TRIGGER_TYPE,
  NODE_LABEL,
  NODE_TYPE,
} from '../Content/Nodes/constant';
import { initialSwitchTabsAudiences } from './constants';
import { getChannelCodeById } from '../../utils';
import { getVariantThumbnail } from '../Content/Nodes/Destination/utils';
import { mapInputToAPI } from '../_reducer/utils';
import { sanitizeRedundantVariantBlastCampaignMetadataTags } from '../utils.node';

const PATH = 'app/modules/Dashboard/MarketingHub/Journey/Create/utils.blast.js';

/**
 * Converts a third-party campaign journey object to a blast journey object.
 *
 * @param {Object} journey - The third-party campaign journey object.
 * @param {Object} journey.workflow_setting - The workflow settings for the journey.
 * @param {Array} journey.workflow_setting.branchs - Array containing branch metadata.
 * @param {string} journey.workflow_setting.actionType - The type of action in the workflow.
 * @param {Object} journey.workflow_setting.metadata - Metadata associated with the workflow.
 * @param {Array} journey.workflow_setting.metadata.audiences - Array of audience details.
 * @param {Object} journey.workflow_setting.metadata.scheduledThirdParty - Third-party scheduling details.
 * @param {string} journey.workflow_setting.actionId - The action ID for the workflow setting.
 * @param {Array} journey.custom_inputs - Custom inputs associated with the journey trigger.
 *
 * @returns {Object} The blast journey object with trigger and campaigns configured.
 *
 * @throws {Error} Throws an error if the conversion fails, logging it to a message queue.
 *
 */
export const convertThirdPartyCampaignToBlast = journey => {
  try {
    const blastJourney = {
      trigger: {},
      campaigns: [],
    };

    const { workflow_setting, custom_inputs: triggerCustomInputs } = journey;
    const { branchs, actionType, metadata, actionId } = workflow_setting;

    const {
      audiences,
      scheduledThirdParty = {},
      ...triggerMetadata
    } = metadata;
    // NOTE: third-party campaign can only have one destination
    const [destination] = branchs;

    blastJourney.trigger = {
      ...triggerMetadata,
      audiences,
      actionId: workflow_setting.actionId,
      actionType,
      customInputs: triggerCustomInputs,
      scheduledThirdParty: {
        value: scheduledThirdParty?.startTime,
        catalogCode: scheduledThirdParty?.catalogCode,
        sendMethod: scheduledThirdParty?.sendMethod,
        error: '',
      },
      moreInfo: {
        childId: destination.actionId,
      },
    };

    // update item type id to trigger node
    if (actionType === NODE_TYPE.SCHEDULED) {
      const itemTypeId = get(audiences, 'itemTypeId');

      if (!blastJourney.trigger.itemTypeId && itemTypeId) {
        blastJourney.trigger.itemTypeId = itemTypeId;
      }
    }

    const {
      campaignInfo,
      destinationId,
      variantIds,
      variantInfo,
      channelId,
      catalogId,
    } = destination.metadata;

    const {
      campaign_id: campaignId,
      campaign_name: campaignName,
      custom_inputs: customInputs,
      campaign_setting: campaignSetting,
    } = campaignInfo;
    const { deliveryTimeConfig } = campaignSetting;

    const blastCampaign = {
      campaignId,
      campaignName,
      sendAs: destinationId,
      catalogId,
      catalogLabel: destination.catalogLabel,
      nodeLabel: destination.label,
      customInputs,
      variantIds,
      variantInfo: variantInfo.map(variant => ({
        contentSetting: variant.content_setting,
        variantId: variant.variant_id,
        variantKey: variant.variant_id,
        variantName: variant.variant_name,
        custom_inputs: variant.custom_inputs,
        status: variant.status,
      })),
      actionId: destination.actionId,
      channelId,
      moreInfo: {
        parentId: actionId,
      },
      deliveryTimeConfig,
    };

    const blastFilter = convertDeliveryFilterToBlastFilter(
      audiences.filters[0],
    );

    blastCampaign.audiences = { ...blastFilter };
    blastCampaign.viewObject = audiences?.viewObjects?.length
      ? audiences?.viewObjects[0]
      : null;
    blastJourney.campaigns.push(blastCampaign);

    return blastJourney;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'convertThirdPartyCampaignToBlast',
      data: error.stack,
    });
  }
};

export const convertJourneyToBlast = journey => {
  try {
    const blastJourney = {
      trigger: {},
      campaigns: [],
    };

    const { workflow_setting, custom_inputs: triggerCustomInputs } = journey;
    const { branchs, actionType, metadata } = workflow_setting;
    const { audiences, filters: _filters, ...triggerMetadata } = metadata;

    const [nodeIfThen] = branchs;

    blastJourney.trigger = {
      ...triggerMetadata,
      actionId: workflow_setting.actionId,
      actionType,
      customInputs: triggerCustomInputs,
      moreInfo: {
        childId: nodeIfThen.actionId,
      },
    };
    const yesBranches = nodeIfThen.branchs.filter(
      node => node.actionType === NODE_TYPE.CONDITION_YES,
    );
    // update itemtypeid to trigger node
    if (actionType === NODE_TYPE.SCHEDULED) {
      const itemTypeId = getItemTypeIdNodeYes(yesBranches[0].metadata.filters);

      if (!blastJourney.trigger.itemTypeId && itemTypeId) {
        blastJourney.trigger.itemTypeId = itemTypeId;
      }
    }
    yesBranches.forEach((nodeYes, index) => {
      const [nodeDest] = nodeYes.branchs;
      const blastCampaign = convertNodeDestToCampaign({
        nodeDest,
        actionType,
        nodeYes,
        viewObject: audiences?.viewObjects?.length
          ? audiences.viewObjects[index]
          : null,
      });

      blastJourney.campaigns.push(blastCampaign);
    });
    // for (const nodeYes of yesBranches) {
    //   const [nodeDest] = nodeYes.branchs;
    //   const blastCampaign = convertNodeDestToCampaign({
    //     nodeDest,
    //     actionType,
    //     nodeYes,
    //   });

    //   blastJourney.campaigns.push(blastCampaign);
    // }

    return blastJourney;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'convertJourneyToBlast',
      data: error.stack,
    });
  }
};

const getActionId = (actionId, design) => {
  try {
    // if (design === 'create') {
    //   return generateKey();
    // }

    return actionId || generateKey();
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'getActionId',
      data: error.stack,
    });
  }
};

const MAP_ITEM_TYPE_ID_OBJECT = {
  '-1003': 'customer',
  '-1007': 'visitor',
};

/**
 * Converts a blast journey object into a format suitable for third-party campaigns.
 *
 * @param {Object} blastJourney - The original blast journey object containing various properties
 * for triggers, campaigns, design, thumbnails, and nodes information.
 * @param {Object} blastJourney.trigger - Information about the trigger action.
 * @param {string} blastJourney.trigger.actionType - The type of action to trigger.
 * @param {Object} blastJourney.trigger.customInputs - Custom inputs related to the trigger.
 * @param {string} blastJourney.trigger.itemTypeId - The ID of the item type associated with the trigger.
 * @param {Object} [blastJourney.trigger.moreInfo={}] - Additional information related to the trigger.
 * @param {Object} blastJourney.campaigns - An array of campaigns included in the blast journey.
 * @param {Object} blastJourney.design - Design configuration details for the journey.
 * @param {Object} blastJourney.thumbnails - Thumbnails related to the journey's design.
 * @param {Map} blastJourney.nodesInfo - A map containing nodes information keyed by action ID.
 *
 * @returns {Object} - A transformed object representing a journey configured for third-party campaigns.
 * @returns {Object} return.workflow_setting - Settings related to the workflow of the journey.
 * @returns {Object} return.custom_inputs - Custom inputs extracted and modified from the original journey.
 *
 * @throws {Error} If an error occurs during the conversion process, the function logs the error details
 * using the `addMessageToQueue` utility and returns `undefined`.
 *
 * @description
 * This function processes the `blastJourney` object to create a new object format compatible with third-party
 * campaign workflows. It extracts the trigger details, handles custom inputs, and constructs the necessary
 * nodes for each campaign included in the journey. If the trigger type is scheduled, audience data is also included.
 * The function handles potential errors gracefully by catching exceptions and logging the stack trace.
 */
export const convertBlastToThirdPartyCampaigns = blastJourney => {
  try {
    const normalJourney = {
      workflow_setting: {},
      custom_inputs: {},
    };

    const { trigger, campaigns, design, thumbnails, nodesInfo } = blastJourney;

    const {
      actionType,
      customInputs: triggerCustomInputs,
      itemTypeId,
      moreInfo: triggerMoreInfo = {},
      ...triggerInfo
    } = trigger;

    normalJourney.custom_inputs = { ...triggerCustomInputs };
    const scheduledThirdParty = get(triggerInfo, 'scheduledThirdParty.value');
    const scheduledThirdPartyMethod = get(
      triggerInfo,
      'scheduledThirdParty.sendMethod',
    );
    const catalogCodeThirdParty = get(
      triggerInfo,
      'scheduledThirdParty.catalogCode',
    );

    const currentDate = new Date();
    const portalDate = PortalDate.parseToPortalDate(currentDate)
    const nodeTrigger = {
      actionId: getActionId(triggerInfo.actionId, design),
      label: NODE_LABEL[actionType],
      branchs: [],
      metadata: {
        ...triggerInfo,
        startDate: portalDate.toISOString().split('T')[0],
        startTime: currentDate.getTime(),
        startTimeOfDay: {
          "hour": portalDate.getHours(),
          "minute": portalDate.getMinutes()
        },
        triggerType: 'after_activate',
        scheduledThirdParty: {
          startTime: scheduledThirdParty,
          catalogCode: catalogCodeThirdParty,
          sendMethod: scheduledThirdPartyMethod,
        },
        isThirdParty: true,
      },
      actionType,
    };
    const filters = [];

    // NOTE: only have one campaign, one variant
    for (const campaign of campaigns) {
      const nodeDest = buildNodeDestination({
        campaign,
        actionType,
        design,
        thumbnails,
        nodeInfo: nodesInfo.get(campaign.actionId),
      });

      if (actionType === NODE_TYPE.SCHEDULED) {
        const audiences = getObjectPropSafely(() => campaign.audiences, {});
        // Convert audience to filter
        const convertedFilters = convertBlastFilterToDeliveryFilter(audiences);
        filters.push(convertedFilters);
      }
      nodeTrigger.branchs.push(nodeDest);
    }

    // Add audience data to the trigger
    nodeTrigger.metadata.audiences = {
      filters,
      itemTypeId,
      object: MAP_ITEM_TYPE_ID_OBJECT[itemTypeId],
    };
    if (actionType === NODE_TYPE.SCHEDULED) {
      // viewObject
      const arrayViewObject = [];
      for (const campaign of campaigns) {
        const data = mapViewObject({ campaign });
        arrayViewObject.push(data);
      }
      nodeTrigger.metadata.audiences.viewObjects = [...arrayViewObject];
    }
    normalJourney.workflow_setting = { ...nodeTrigger };

    return normalJourney;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'convertBlastToThirdPartyCampaigns',
      data: error.stack,
    });
  }
};

export const convertBlastToJourney = blastJourney => {
  try {
    const normalJourney = {
      workflow_setting: {},
      custom_inputs: {},
    };

    const { trigger, campaigns, design, thumbnails, nodesInfo } = blastJourney;

    const {
      actionType,
      customInputs: triggerCustomInputs,
      itemTypeId,
      moreInfo: triggerMoreInfo = {},
      ...triggerInfo
    } = trigger;
    normalJourney.custom_inputs = { ...triggerCustomInputs };

    const nodeTrigger = {
      actionId: getActionId(triggerInfo.actionId, design),
      label: NODE_LABEL[actionType],
      branchs: [],
      metadata: {
        ...triggerInfo,
      },
      actionType,
    };

    if (actionType === NODE_TYPE.EVENT_BASED) {
      delete nodeTrigger.metadata.frequency;
      delete nodeTrigger.metadata.repeatInterval;
    }

    const ifThenActionId = getObjectPropSafely(
      () => triggerMoreInfo.childId,
      null,
    );
    const nodeIfThen = {
      actionId: getActionId(ifThenActionId, design),
      label: NODE_LABEL.CLASSIC_LIST_BRANCH,
      branchs: [],
      metadata: {
        numBranchs: campaigns.length + 1, // + 1 branch NO
      },
      actionType: NODE_TYPE.CLASSIC_LIST_BRANCH,
    };

    for (const campaign of campaigns) {
      const nodeYes = buildNodeYes({ campaign, actionType, design });
      const nodeDest = buildNodeDestination({
        campaign,
        actionType,
        design,
        thumbnails,
        nodeInfo: nodesInfo.get(campaign.actionId),
      });

      nodeYes.branchs.push(nodeDest);

      nodeIfThen.branchs.push(nodeYes);
    }

    if (actionType === NODE_TYPE.SCHEDULED) {
      const audiencesFilter = getObjectPropSafely(
        () => nodeIfThen.branchs.map(nodeYes => nodeYes.metadata.filters),
        [],
      );
      // viewObject
      const arrayViewObject = [];
      for (const campaign of campaigns) {
        const data = mapViewObject({ campaign });

        arrayViewObject.push(data);
      }

      nodeTrigger.metadata.audiences = {
        filters: audiencesFilter,
        itemTypeId,
        object: MAP_ITEM_TYPE_ID_OBJECT[itemTypeId],
        viewObjects: [...arrayViewObject],
      };
    }

    const nodeNo = buildNodeNo();

    nodeIfThen.branchs.push(nodeNo);

    nodeTrigger.branchs.push(nodeIfThen);

    normalJourney.workflow_setting = { ...nodeTrigger };

    return normalJourney;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'convertBlastToJourney',
      data: error.stack,
    });
  }
};

// convert audiences filter(node trigger) to delivery filter
export const convertBlastFilterToDeliveryFilter = filter => {
  try {
    const { includedAudiences, excludedAudiences, itemTypeId } = filter;

    const newIncludedFilter = buildFilterDelivery(
      includedAudiences.filters,
      includedAudiences.specificAudienceIds,
      itemTypeId,
    );
    const newExcludedFilter = buildFilterDelivery(
      excludedAudiences.filters,
      excludedAudiences.specificAudienceIds,
      itemTypeId,
    );
    return {
      includedFilters: newIncludedFilter,
      excludedFilters: newExcludedFilter,
    };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'convertBlastFilterToDeliveryFilter',
      data: error.stack,
    });
  }
};

// from audiences
const buildFilterDelivery = (filter, userIds, itemTypeId) => {
  try {
    const newFilter = {
      OR: [],
    };

    const isFilterSegment = getObjectPropSafely(
      () => !!filter.OR[0].AND[0],
      false,
    );
    const isFilterUser = userIds && userIds.length;

    if (isFilterSegment) {
      const filterSegment = buildFilterDeliverySegment(filter);

      newFilter.OR.push({
        AND: [{ ...filterSegment }],
      });
    }

    if (isFilterUser) {
      const filterUser = buildFilterDeliveryUser(userIds, itemTypeId);

      newFilter.OR.push({
        AND: [{ ...filterUser }],
      });
    }

    return newFilter;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'buildFilterDelivery',
      data: error.stack,
    });
  }
};

const buildFilterDeliverySegment = filter => {
  const MAP_OPERATOR = {
    includes: 'matches_any',
    doesnt_include: 'matches_all',
  };

  try {
    const segmentIds = filter.OR.reduce((acc, cur) => {
      for (const AND of cur.AND) {
        const { value } = AND;
        if (value && value.length) acc.push(...value);
      }

      return acc;
    }, []);

    const {
      feOperator,
      conditionType,
      column,
      data_type,
    } = getObjectPropSafely(() => filter.OR[0].AND[0], {});

    const filterSegment = {
      conditionType,
      column,
      data_type,
      operator: MAP_OPERATOR[feOperator],
      value: [...segmentIds],
    };

    return filterSegment;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'buildFilterDeliverySegment',
      data: error.stack,
    });
  }
};

const buildFilterDeliveryUser = (userIds, itemTypeId) => {
  const MAP_ITEM_TYPE_ID = {
    '-1003': 'customer_id',
    '-1007': 'user_id',
  };
  try {
    const filterUser = {
      conditionType: MAP_ITEM_TYPE_ID[itemTypeId],
      column: MAP_ITEM_TYPE_ID[itemTypeId],
      data_type: 'string',
      operator: 'matches',
      value: [...userIds],
    };

    return filterUser;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'buildFilterDeliveryUser',
      data: error.stack,
    });
  }
};

export const convertDeliveryFilterToBlastFilter = deliveryFilter => {
  try {
    const { includedFilters, excludedFilters } = deliveryFilter;

    const newIncludedFilters = buildFilterBlast(includedFilters);
    const newExcludedFilters = buildFilterBlast(excludedFilters);
    const { audienceSegmentType, itemTypeId } = getMoreInfoBlastFilter(
      includedFilters,
    );

    return {
      includedAudiences: newIncludedFilters,
      excludedAudiences: newExcludedFilters,
      audienceSegmentType,
      itemTypeId,
    };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'convertDeliveryFilterToBlastFilter',
      data: error.stack,
    });
  }
};

const getMoreInfoBlastFilter = filter => {
  const MAP_CONDITION_TYPE = {
    customer_segment: -1003,
    customer_id: -1003,
    user_segment: -1007,
    user_id: -1007,
  };
  try {
    const { conditionType } = filter.OR[0].AND[0];

    const audienceSegmentType = conditionType;
    const itemTypeId = MAP_CONDITION_TYPE[conditionType];

    return {
      audienceSegmentType,
      itemTypeId,
    };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'getInfoBlastFilter',
      data: error.stack,
    });
  }
};

const buildFilterBlast = filter => {
  try {
    const filters = { OR: [] };
    const specificAudienceIds = [];
    const audienceTypes = new Set();

    for (const OR of filter.OR) {
      for (const AND of OR.AND) {
        switch (AND.conditionType) {
          case 'customer_segment':
          case 'user_segment': {
            const filterSegment = buildFilterBlastSegment(AND);
            filters.OR.push(...filterSegment);

            audienceTypes.add('segmentAudiences');
            break;
          }
          case 'customer_id':
          case 'user_id': {
            const filterUser = buildFilterBlastUser(AND);
            specificAudienceIds.push(...filterUser);

            audienceTypes.add('specificAudiences');
            break;
          }
          default:
        }
      }
    }

    return {
      filters,
      specificAudienceIds,
      audienceTypes: [...audienceTypes],
    };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'buildFilterBlast',
      data: error.stack,
    });
  }
};

const buildFilterBlastSegment = filter => {
  const MAP_OPERATOR_TO_FE = {
    matches_all: 'doesnt_include',
    matches_any: 'includes',
  };
  try {
    const {
      column,
      conditionType,
      data_type,
      operator,
      value: segmentIds,
    } = filter;

    let filters = [];
    switch (operator) {
      case 'matches_any':
        filters = [
          {
            AND: segmentIds.map(segmentId => ({
              column,
              conditionType,
              data_type,
              feOperator: MAP_OPERATOR_TO_FE[operator],
              operator: 'matches',
              value: [segmentId],
            })),
          },
        ];
        break;
      case 'matches_all':
        filters = segmentIds.map(segmentId => ({
          AND: [
            {
              column,
              conditionType,
              data_type,
              feOperator: MAP_OPERATOR_TO_FE[operator],
              operator: 'matches',
              value: [segmentId],
            },
          ],
        }));

        break;
      default:
    }

    return filters;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'buildFilterBlastSegment',
      data: error.stack,
    });
  }
};

const buildFilterBlastUser = filter => {
  try {
    return [...filter.value];
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'buildFilterBlastUser',
      data: error.stack,
    });
  }
};

export const getActiveNode = (campaign, listNodes) => {
  const nodeDestination = listNodes.find(
    node => node.destination_id === campaign.sendAs,
  );
  const channelCode = getChannelCodeById(
    nodeDestination && nodeDestination.channel_code_id,
  );
  const triggerType = DEFAULT_TRIGGER_TYPE[channelCode] || NODE_TYPE.SCHEDULED;

  const activeNode = {
    nodeId: campaign.actionId,
    channelId: campaign.channelId,
    campaignId: campaign.campaignId,
    value: campaign.catalogId,
    channelCode,
    catalogCode: get(nodeDestination, 'catalog_code', ''),
    type: triggerType,
  };

  return activeNode;
};

export const validateSwitchTabAudiences = (
  activeNode = {},
  campaignList = [],
) => {
  const campaignActive = campaignList.find(
    campaignItem => campaignItem.actionId === activeNode.nodeId,
  );

  if (isEmpty(activeNode) || isEmpty(campaignList) || !campaignActive) {
    return initialSwitchTabsAudiences();
  }

  const { audiences = {} } = campaignActive;
  const {
    itemTypeId = '',
    includedAudiences = {},
    excludedAudiences = {},
  } = audiences;
  const includeAudienceTypes = includedAudiences.audienceTypes;
  const excludeAudienceTypes = excludedAudiences.audienceTypes;

  if (isEmpty(includeAudienceTypes) && isEmpty(excludeAudienceTypes)) {
    return initialSwitchTabsAudiences();
  }
  const use = [];
  let canSwitch = false;
  let switchToTab = {};
  let [hasIncludeSpecific, hasIncludeSegment] = [false, false];
  let [hasExcludeSpecific, hasExcludeSegment] = [false, false];

  if (includeAudienceTypes.length === 2) {
    includeAudienceTypes.forEach(includeItem => {
      if (includeItem === 'specificAudiences') {
        hasIncludeSpecific = !isEmpty(includedAudiences.specificAudienceIds);
      }
      if (includeItem === 'segmentAudiences') {
        const filtersOR = includedAudiences.filters.OR || [];

        if (filtersOR.length === 1) {
          filtersOR.forEach(filterItem => {
            hasIncludeSegment = !isEmpty(filterItem.AND);
          });
        }
      }
    });
  }

  if (excludeAudienceTypes.length === 2) {
    excludeAudienceTypes.forEach(excludeItem => {
      if (excludeItem === 'specificAudiences') {
        hasExcludeSpecific = !isEmpty(excludedAudiences.specificAudienceIds);
      }
      if (excludeItem === 'segmentAudiences') {
        const filtersOR = excludedAudiences.filters.OR || [];

        if (filtersOR.length === 1) {
          filtersOR.forEach(filterItem => {
            hasExcludeSegment = !isEmpty(filterItem.AND);
          });
        } else {
          hasExcludeSegment = true;
        }
      }
    });
  }

  if (
    (!hasIncludeSegment && !hasIncludeSpecific) ||
    (hasIncludeSegment && hasIncludeSpecific) ||
    (hasExcludeSegment &&
      hasExcludeSpecific &&
      !isEmpty(excludeAudienceTypes)) ||
    (!hasExcludeSegment &&
      !hasExcludeSpecific &&
      !isEmpty(excludeAudienceTypes))
  ) {
    return initialSwitchTabsAudiences();
  }

  if (hasIncludeSegment || hasIncludeSpecific) {
    canSwitch = true;
    use.push('include');
    switchToTab = {
      ...switchToTab,
      include: hasIncludeSegment ? 'segmentAudiences' : 'specificAudiences',
    };
  }

  if (hasExcludeSegment || hasExcludeSpecific) {
    canSwitch = true;
    use.push('exclude');
    switchToTab = {
      ...switchToTab,
      exclude: hasExcludeSpecific ? 'specificAudiences' : 'segmentAudiences',
    };
  }

  return {
    canSwitch,
    use,
    switchToTab,
    itemTypeId,
  };
};

const buildNodeYes = ({ campaign, actionType, design }) => {
  try {
    const { campaignName, moreInfo: campaignMoreInfo } = campaign;

    const yesActionId = getObjectPropSafely(() => campaignMoreInfo.parentId);

    let nodeYes;

    if (actionType === NODE_TYPE.EVENT_BASED) {
      const { filter } = campaign;

      // filters is optional
      const {
        filters = null,
        excludedFilters = null,
        audienceSegmentType = null,
        filterType = null,
        itemTypeId = null,
        sortFilter = [],
      } = filter;

      nodeYes = {
        actionId: getActionId(yesActionId, design),
        actionType: NODE_TYPE.CONDITION_YES,
        branchs: [],
        label: campaignName,
        metadata: {
          branchName: campaignName,
          filters,
          filterType,
          excludedFilters,
          audienceSegmentType,
          itemTypeId,
          sortFilter,
        },
      };
    } else if (actionType === NODE_TYPE.SCHEDULED) {
      const { audiences } = campaign;
      const filters = convertBlastFilterToDeliveryFilter(audiences);

      nodeYes = {
        actionId: getActionId(yesActionId, design),
        actionType: NODE_TYPE.CONDITION_YES,
        branchs: [],
        label: campaignName,
        metadata: {
          branchName: campaignName,
          filters,
        },
      };
    }

    return nodeYes;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'buildNodeYes',
      data: error.stack,
    });
  }
};
const mapViewObject = ({ campaign }) => {
  try {
    const { audiences } = campaign;

    return {
      excludedAudiences: { ...audiences.excludedAudiences?.viewObject },
      includedAudiences: { ...audiences.includedAudiences?.viewObject },
    };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'mapViewObject',
      data: error.stack,
    });
  }
};
const buildNodeNo = () => {
  try {
    return {
      actionId: generateKey(),
      actionType: NODE_TYPE.CONDITION_NO,
      branchs: [
        {
          actionId: generateKey(),
          actionType: NODE_TYPE.END,
          branchs: [],
          label: NODE_LABEL.END,
          metadata: {},
        },
      ],
      label: NODE_LABEL.CONDITION_NO,
      metadata: {
        branchName: NODE_LABEL.CONDITION_NO,
      },
    };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'buildNodeNo',
      data: error.stack,
    });
  }
};

const buildNodeDestination = ({
  campaign,
  actionType,
  design,
  thumbnails,
  nodeInfo,
}) => {
  try {
    const {
      actionId,
      variantIds,
      variantInfo,
      campaignId,
      campaignName,
      channelId,
      sendAs,
      catalogId,
      catalogLabel,
      nodeLabel,
      // customInputs, // ?? Custom input of campaign is not updated
      campaignSetting,
    } = campaign;

    const destination = nodeInfo.get('destination') || {};
    const { data = {} } = destination;
    const customInputs = mapInputToAPI(
      safeParse(
        data.campaignCustomInput && data.campaignCustomInput.workspaces,
        [],
      ),
    );

    const randomSetting = variantInfo.reduce((acc, cur) => {
      if (cur.variantKey) {
        acc[cur.variantKey] = campaignSetting?.random[cur.variantKey];
      }

      if (thumbnails) {
        const {
          thumbnail,
          type,
          viewThumbnails,
          storedViewThumbnails,
          viewId,
        } = getVariantThumbnail(cur.contentSetting);
        let variantThumbnails = [];
        const addThumb = thumbObj => {
          variantThumbnails.push({
            id: `v_thumb_${random(10)}`,
            url: thumbObj.thumbnail,
            name: cur.variantName,
            variantId: String(cur.variantId || cur.variantKey || ''),
            campaignName,
            type,
            viewId: thumbObj.viewId,
          });
        };

        if (storedViewThumbnails && storedViewThumbnails.length) {
          variantThumbnails = storedViewThumbnails.map(thumb => ({
            ...thumb,
            variantId: String(cur.variantId || cur.variantKey || ''),
            name: cur.variantName,
            campaignName,
          }));
        } else if (viewThumbnails && viewThumbnails.length) {
          viewThumbnails.forEach(addThumb);
        } else if (thumbnail) {
          addThumb({ thumbnail, viewId });
        }

        cur.contentSetting.thumbnails = variantThumbnails;
        variantThumbnails.forEach(thumb => thumbnails.push(thumb));
      }

      return acc;
    }, {});

    const nodeDestination = {
      actionId: getActionId(actionId, design),
      actionType: NODE_TYPE.DESTINATION,
      branchs: [
        {
          actionId: generateKey(),
          label: NODE_LABEL.END,
          branchs: [],
          metadata: {},
          actionType: NODE_TYPE.END,
        },
      ],
      label: nodeLabel,
      catalogLabel,
      metadata: {
        isUpdated: 1,
        catalogId,
        channelId,
        campaignId,
        variantIds,
        destinationId: sendAs,
        campaign: {
          campaignId,
          campaignName,
          status: 1,
          campaignSetting: {
            algoMethod: 'random',
            random: randomSetting,
          },
          custom_inputs: customInputs,
        },
        variants: variantInfo,
      },
    };

    if (actionType === NODE_TYPE.EVENT_BASED) {
      const { zoneId, priority, contentPlacement, isFitContent } = campaign;

      nodeDestination.metadata.zoneId = zoneId;
      nodeDestination.metadata.priority = priority;
      nodeDestination.metadata.contentPlacement = contentPlacement;
      nodeDestination.metadata.isFitContent = isFitContent;
    } else if (actionType === NODE_TYPE.SCHEDULED) {
      const { deliveryTimeConfig } = campaign;

      nodeDestination.metadata.campaign.campaignSetting.deliveryTimeConfig = deliveryTimeConfig;
    }

    return nodeDestination;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'buildNodeDestination',
      data: error.stack,
    });
  }
};

const convertNodeDestToCampaign = ({
  nodeDest,
  actionType,
  nodeYes,
  viewObject,
}) => {
  try {
    const {
      campaignInfo,
      destinationId,
      variantIds,
      variantInfo,
      channelId,
      catalogId,
    } = nodeDest.metadata;
    const {
      campaign_id: campaignId,
      campaign_name: campaignName,
      custom_inputs: customInputs,
      campaign_setting: campaignSetting,
    } = campaignInfo;

    const blastCampaign = {
      campaignId,
      campaignName,
      sendAs: destinationId,
      catalogId,
      catalogLabel: nodeDest.catalogLabel,
      nodeLabel: nodeDest.label,
      customInputs,
      campaignSetting,
      variantIds,
      variantInfo: variantInfo.map(variant => ({
        contentSetting: variant.content_setting,
        variantId: variant.variant_id,
        variantKey: variant.variant_id,
        variantName: variant.variant_name,
        custom_inputs: variant.custom_inputs,
        status: variant.status,
      })),
      actionId: nodeDest.actionId,
      channelId,
      moreInfo: {
        parentId: nodeYes.actionId,
      },
    };

    if (actionType === NODE_TYPE.EVENT_BASED) {
      const {
        zoneId,
        priority,
        contentPlacement,
        isFitContent,
      } = nodeDest.metadata;

      blastCampaign.zoneId = zoneId;
      blastCampaign.priority = priority;
      blastCampaign.contentPlacement = contentPlacement;
      blastCampaign.isFitContent = isFitContent;
      blastCampaign.filter = {
        itemTypeId: nodeYes.metadata.itemTypeId || '',
        audienceSegmentType: nodeYes.metadata.audienceSegmentType || '',
        excludedFilters: nodeYes.metadata.excludedFilters || null,
        sortFilter: nodeYes.metadata.sortFilter || [],
        filters: nodeYes.metadata.filters,
        filterType: nodeYes.metadata.filterType,
      };
    } else if (actionType === NODE_TYPE.SCHEDULED) {
      const { deliveryTimeConfig } = campaignSetting;
      const blastFilter = convertDeliveryFilterToBlastFilter(
        nodeYes.metadata.filters,
      );

      blastCampaign.audiences = { ...blastFilter };
      blastCampaign.deliveryTimeConfig = deliveryTimeConfig;
      if (viewObject) {
        blastCampaign.viewObject = viewObject;
      }
    }

    return blastCampaign;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'convertNodeDestToCampaign',
      data: error.stack,
    });
  }
};

const getItemTypeIdNodeYes = deliveryFilter => {
  try {
    const { includedFilters } = deliveryFilter;

    const { itemTypeId } = getMoreInfoBlastFilter(includedFilters);

    return itemTypeId;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'getItemTypeIdNodeYes',
      data: error.stack,
    });
  }
};

export const getCatalogByChannel = (destinations, channelId) => {
  try {
    let initNode;

    switch (channelId) {
      case CHANNEL.WEB_PERSONALIZATION.id:
        initNode = destinations.find(
          dest => dest.catalogCode === 'web_embedded',
        );
        break;
      default:
        initNode = destinations[0];
    }

    return initNode;
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'getInitNode',
      data: error.stack,
    });

    return destinations[0];
  }
};

export const toDestinationList = (listNodes, catalog) => {
  try {
    return listNodes.data.map(dest => ({
      catalog_dest_id: catalog.code,
      destination_name: dest.destinationName,
      catalog_id: catalog.name,
      channel_id: catalog.channelCode,
      destination_id: dest.destinationId,
      channel_code_id: catalog.channelId,
      catalog_code: catalog.catalogCode,
      // status: 1,
      // row_count: dest.rowCount,
      // logo_url:
      //   'https://sandbox-app.cdp.asia/hub/assets/images/logos/destinations/infobip.png',
      // isEdit: true,
    }));
  } catch (error) {
    return [];
  }
};

export const handleGenDefaultName = (listCampaign, defaultName) => {
  let listDefaultName = [];
  let number = 1;
  const regexDefaultName = /^Campaign (\d+)$/;
  if (listCampaign && listCampaign.length) {
    listDefaultName = listCampaign
      .filter(overview => regexDefaultName.test(overview.campaignName))
      .map(each => +each.campaignName.replace('Campaign ', ''))
      .sort((num1, num2) => num1 - num2)
      .reduce((accum, current) => {
        if (!accum.length || current !== accum[accum.length - 1]) {
          accum.push(current);
        }
        return accum;
      }, []);
  }

  if (listDefaultName.length) {
    let index = 0;
    while (index < listDefaultName.length) {
      if (listDefaultName[index] === number) {
        number++;
        index++;
      } else {
        break;
      }
    }
  }

  return `Campaign ${number}`;
};

/**
 * Maps campaign settings to API-compatible format.
 * @param {Object} options - Options object.
 * @param {Array} options.campaigns - Array of campaign objects.
 * @param {Array} options.destinationInfo - Array of destination info objects.
 * @returns {Array} Array of mapped campaign info objects.
 */
export const mapCampaignSettingToAPI = ({
  campaigns = [],
  destinationInfo = [],
}) => {
  try {
    if (!isArray(campaigns) || !isArray(destinationInfo)) return [];

    const draftCampaignInfo = campaigns.map(campaign => {
      let token;
      const { sendAs: destinationId = '', variantInfo = [] } = campaign;
      const richMenus = get(variantInfo[0], [
        'contentSetting',
        'destinationInput',
        'template',
        'richMenus',
      ]); // NOTE: has only 1 variant in a campaign for line rich menus

      const destination = destinationInfo.find(
        dest => +dest.destinationId === +destinationId,
      );

      if (destination) {
        token = get(destination, ['destinationSetting', 'token']);
      }

      return {
        token,
        destinationId: +destinationId,
        totalMenu: isArray(richMenus) ? richMenus.length : 0,
      };
    });

    return draftCampaignInfo;
  } catch (err) {
    addMessageToQueue({
      path: PATH,
      func: 'mapCampaignSettingToAPI',
      data: {
        err: err.stack,
        campaigns,
        destinationInfo,
      },
    });
  }
};
export const isDisableActive = data => {
  const journeyGoals = safeParse(data.get('journeyGoals'), {});
  return journeyGoals?.goal?.some(item => {
    return item.error.length > 0;
  });
};

export const sanitizeExtraDataCampaigns = (campaigns, mainProperties) => {
  if (!isArray(campaigns) || isEmpty(mainProperties))
    return { campaigns, newProperties: mainProperties };

  try {
    const removedInfo = {};
    const newCampaigns = campaigns.map(campaign => {
      const { actionId: nodeId, variantInfo } = campaign;

      const {
        variants,
        removedTagIds,
      } = sanitizeRedundantVariantBlastCampaignMetadataTags(variantInfo);

      if (removedTagIds.length) {
        removedInfo[nodeId] = { nodeId, removedTagIds };
        return { ...campaign, variantInfo: variants };
      }

      return campaign;
    });

    let newProperties = mainProperties;
    if (!isEmpty(removedInfo) && isImmutableMap(newProperties)) {
      newProperties = newProperties.withMutations(mProperties => {
        Object.values(removedInfo).forEach(({ nodeId, removedTagIds }) => {
          removedTagIds.forEach(tagId => {
            if (mProperties.hasIn(['nodes', nodeId, 'tags', tagId])) {
              mProperties.deleteIn(['nodes', nodeId, 'tags', tagId]);
            }
          });
        });
      });
    }

    return { campaigns: newCampaigns, newProperties };
  } catch (error) {
    addMessageToQueue({
      path: PATH,
      func: 'sanitizeExtraDataCampaigns',
      data: { error: error.stack, args: { campaigns } },
    });
    return { campaigns, newProperties: mainProperties };
  }
};

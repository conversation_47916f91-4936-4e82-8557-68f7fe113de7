/* eslint-disable consistent-return */
/* eslint-disable no-case-declarations */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
// Libraries
import produce from 'immer';
import { get, isEmpty, isObject, has } from 'lodash';

// Utils
import { toAPIPerformEvent } from 'components/common/UIPerformEvent/utils';
import { toAPISelectDayOfWeek } from 'containers/UIDev/SelectDayOfWeek/utils';
import { toEntryAPI } from '../../../../../../../components/Templates/TargetAudienceMulti/utils';
import { toAPIFrequencyCapping } from '../../../../../../../components/common/UIFrequencyCapping/utils';
import {
  toAPIScheduleTrigger,
  toAPISelectTime,
} from '../../../../../../../components/common/UISchedulerTrigger/utils';
import {
  generate<PERSON>ey,
  getObjectPropSafely,
  safeParse,
} from '../../../../../../../utils/common';
import { toVariantAPI } from '../../Content/Nodes/Destination/utils';
import { mapInputToAPI } from '../../_reducer/utils';
import { initCreateRolesAction } from '../../utils.story.rules';

// Constants
import ReduxTypes from '../../../../../../../redux/constants';
import { DEFAULT_TRIGGER_TYPE, NODE_TYPE } from '../../Content/Nodes/constant';
import { initialStateBlastCampaign } from '../constants';
import { toAPINodeFilter } from '../../../../../../../components/common/UINodeFilter/utils';
import { toConversonAPI } from '../../Content/Nodes/TriggerEventBased/utils';
import { getChannelCodeById } from '../../../utils';

const blastCampaignReducerFor = config => {
  const PREFIX = config.key;
  const triggerType =
    DEFAULT_TRIGGER_TYPE[config.channelActive] || NODE_TYPE.SCHEDULED;

  const mainReducer = (
    state = initialStateBlastCampaign(triggerType),
    action,
  ) =>
    produce(state, draft => {
      switch (action.type) {
        case `${PREFIX}${ReduxTypes.RESET}`: {
          const initState = initialStateBlastCampaign(triggerType);
          Object.keys(initState).forEach(key => {
            draft[key] = initState[key];
          });
          return;
        }
        case `${PREFIX}${ReduxTypes.INIT}`: {
          const {
            channelActive,
            design,
            activeRow,
            blastCampaign,
          } = action.payload;
          draft.channelId = channelActive.value;
          draft.design = design;
          if (design === 'create') {
            draft.activeRow = {
              accepted_actions: initCreateRolesAction(),
            };
          } else {
            // update or preview
            const { campaigns, trigger } = blastCampaign.blastCampaignData;
            draft.activeRow = activeRow;
            draft.campaigns = campaigns;
            if (
              campaigns.findIndex(
                campaignItem =>
                  campaignItem.actionId === state.activeNode.nodeId,
              ) === -1
            ) {
              draft.activeNode = {};
            }
            draft.trigger = { ...state.trigger, ...trigger };
            if (activeRow.status == 9) {
              draft.disabled = true;
              draft.actionDisabled = false;
            }
          }
          return;
        }

        case `${PREFIX}@@NODE_LIST@@${ReduxTypes.GET_LIST_DONE}`: {
          const { nodeList = [] } = action.payload;
          draft.listNodes = nodeList;
          return;
        }

        case `${PREFIX}@@INIT_DONE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { initDone = false } = action.payload;
          draft.initDone = initDone;
          return;
        }

        case `${PREFIX}@@ACTIVE_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.activeNode = { ...draft.activeNode, ...action.payload };
          return;
        }

        case `${PREFIX}@@CHANGE_ITEM_TYPE_ID@@${ReduxTypes.UPDATE_VALUE}`: {
          const { itemTypeId } = action.payload;
          draft.trigger.itemTypeId = +itemTypeId;

          if (draft.initCampaignNode) {
            const initAudience = {
              itemTypeId,
              isInit: false,
              backup: {},
              currentData: {
                includeCluster: [],
                excludeCluster: [],
                mapCluster: {},
              },
            };

            draft.initCampaignNode = state.initCampaignNode.set(
              'targetAudience',
              initAudience,
            );
          }
          return;
        }

        case `${PREFIX}@@UPDATE_CAMPAIGNS_LIST_DATA@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.campaigns = action.payload;
          return;
        }

        case `${PREFIX}@@ADD_CAMPAIGN@@${ReduxTypes.UPDATE_VALUE}`: {
          const { data = {} } = action.payload;

          draft.campaigns = [...state.campaigns, data];
          return;
        }

        case `${PREFIX}@@DUPLICATE_CAMPAIGN@@${ReduxTypes.UPDATE_VALUE}`: {
          const { index, data = {} } = action.payload;
          const temp = [...state.campaigns];
          temp.splice(index + 1, 0, {
            ...data,
            campaignId: null,
            variantInfo: [...data.variantInfo].map(variantItem => ({
              ...variantItem,
              variantId: null,
              variantKey: `fe_${generateKey()}`,
            })),
            isError: false,
          });

          draft.campaigns = temp;
          return;
        }

        case `${PREFIX}@@REMOVE_CAMPAIGN@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.campaigns = action.payload;
          // tắt condition validate sendTo để không tự động trigger validate trong
          // onchange sau khi remove một campaign
          // draft.canTriggerValidateSendTo = false; // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
          return;
        }

        case `${PREFIX}@@UPDATE_VALID_TIME_SCHEDULED@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { isValid = false } = action.payload;
          draft.isValidTimeScheduled = isValid;
          draft.canActionValidate = true;
          return;
        }

        case `${PREFIX}@@UPDATE_CAN_VALIDATE@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.canActionValidate = action.payload;
          return;
        }

        // Hiện tại mặc định ở Blast chọn Include Audiences và không thể xóa nên không dùng nữa
        // case `${PREFIX}@@UPDATE_CAN_TRIGGER_VALIDATE_SENDTO@@${
        //   ReduxTypes.UPDATE_VALUE
        // }`: {
        //   draft.canTriggerValidateSendTo = action.payload;
        //   return;
        // }

        case `${PREFIX}@@RESET_VALIDATE_FORECAST@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.previewForecast.isValid = false;
          draft.previewForecast.type = 'venn';
          return;
        }

        case `${PREFIX}@@VALIDATE_FORECAST@@${ReduxTypes.UPDATE_VALUE}`: {
          const { data = {} } = action.payload;
          const { excludedAudiences = {}, includedAudiences = {} } = data;

          if (isEmpty(includedAudiences) || isEmpty(excludedAudiences)) return;

          const includeAudiencesType = safeParse(
            includedAudiences.audienceTypes,
            [],
          );
          const excludeAudiencesType = safeParse(
            excludedAudiences.audienceTypes,
            [],
          );

          if (
            !isEmpty(includeAudiencesType) &&
            !isEmpty(excludeAudiencesType)
          ) {
            draft.previewForecast.isValid = true;
            draft.previewForecast.type = 'venn';
          } else if (
            !isEmpty(includeAudiencesType) ||
            !isEmpty(excludeAudiencesType)
          ) {
            draft.previewForecast.isValid = true;
            draft.previewForecast.type = 'pie';
          } else {
            draft.previewForecast.isValid = false;
            draft.previewForecast.type = 'pie';
          }
          return;
        }

        case `${PREFIX}@@UPDATE_VALIDATE_KEY_BLAST@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.validateKeyBlast = state.validateKeyBlast + 1;
          return;
        }

        case `${PREFIX}@@UPDATE_CAMPAIGNS_CREATE_COPY@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { campaigns = [] } = action.payload;
          draft.campaigns = campaigns;
          return;
        }

        case `${PREFIX}@@RESET_STATUS_ERROR_CAMPAIGN@@${ReduxTypes.RESET}`: {
          draft.campaigns = state.campaigns.map(item => ({
            ...item,
            isError: false,
          }));
          return;
        }

        case `${PREFIX}@@UPDATE_ERROR_CAMPAIGN@@${ReduxTypes.UPDATE_VALUE}`: {
          const { campaignId = '', isError = false } = action.payload;
          const newCampaigns = state.campaigns.map(campaignItem => {
            if (campaignItem.actionId === campaignId) {
              return {
                ...campaignItem,
                isError,
              };
            }

            return campaignItem;
          });

          draft.campaigns = newCampaigns;
          return;
        }

        case `${PREFIX}@@UPDATE_LIST_CAMPAIGN@@${ReduxTypes.UPDATE_VALUE}`: {
          const { campaign } = action.payload;
          if (
            draft.campaigns.some(camp => camp.actionId === campaign.actionId)
          ) {
            // update current campaign
            const campaignUpdate = draft.campaigns.find(
              camp => camp.nodeId === campaign.nodeId,
            );
            const indexCampaign = draft.campaigns.findIndex(
              camp => camp.actionId === campaign.actionId,
            );
            const newCampaignUpdate = { ...campaignUpdate, ...campaign };
            draft.campaigns.splice(indexCampaign, 1, newCampaignUpdate);
          } else if (campaign.channelId === state.channelId) {
            // add new campaign
            draft.campaigns.push(campaign);
          }
          return;
        }

        case `${PREFIX}@@GET_INIT_CAMPAIGN_NODE@@${ReduxTypes.UPDATE_VALUE}`: {
          const { design, dataNode, isCreateCopy = false } = action.payload;
          if (design === 'create') {
            const initTemp = dataNode
              .setIn(['destination'], {})
              .set('targetAudience', {
                currentData: {},
                ...(dataNode.get('targetAudience') || {}),
                backup: dataNode.has('targetAudience.backup')
                  ? {
                      includedAudiences: {
                        filters: {
                          OR: [],
                        },
                        specificAudienceIds: [],
                        audienceTypes: [
                          ...(dataNode.get('targetAudience').backup
                            .includedAudiences.audienceTypes || []),
                        ],
                      },
                      excludedAudiences: {
                        filters: {
                          OR: [],
                        },
                        specificAudienceIds: [],
                        audienceTypes: [
                          ...(dataNode.get('targetAudience').backup
                            .excludedAudiences.audienceTypes || []),
                        ],
                      },
                    }
                  : {},
              });

            // draft.initCampaignNode = isCreateCopy ? initTemp : dataNode;
            draft.initCampaignNode = initTemp;
          }
          break;
        }

        case `${PREFIX}@@REFRESH_KEY_VALIDATE_AUDIENCES@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.refreshKeyValidateAudiences =
            state.refreshKeyValidateAudiences + 1;
          return;
        }
        case `${PREFIX}@@UPDATE_DATA_VALIDATE_AUDIENCES@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          draft.validateSwitchTabsAudiences = action.payload;
          return;
        }

        case `${PREFIX}@@DESIGN_BLAST@@${ReduxTypes.UPDATE_VALUE}`: {
          const design = action.payload;
          draft.design = design;
          break;
        }

        case `${PREFIX}@@LOADING@@${ReduxTypes.UPDATE_VALUE}`: {
          draft.isLoading = action.payload || false;
          break;
        }

        case `${PREFIX}@@FREQUENCY_CAPPING_CREATE_COPY@@${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const { frequencyCapping = null } = action.payload;
          const newFrequencyCapping = toAPIFrequencyCapping(
            frequencyCapping,
            state.channelId,
          );
          draft.trigger.frequencyCapping = newFrequencyCapping;
          break;
        }

        case `${PREFIX}@@UPDATE_CAMPAIGN@@${ReduxTypes.UPDATE_VALUE}`: {
          const { data, name, nodeId } = action.payload;
          switch (name) {
            case 'peformEvent': {
              const peformEvent = toAPIPerformEvent(data);

              draft.trigger = { ...state.trigger, event: peformEvent };
              break;
            }
            case 'customInput':
              const convertCustomInput = mapInputToAPI(data);
              draft.trigger.customInputs = convertCustomInput;
              break;
            case 'frequencyCapping':
              const convertFrequencyCapping = toAPIFrequencyCapping(
                data,
                state.channelId,
              );

              draft.trigger.frequencyCapping = convertFrequencyCapping;
              break;

            // SCHEDULED fields
            case 'scheduled':
              const timeConvert = toAPIScheduleTrigger(data);
              if (timeConvert.repeatInterval) {
                timeConvert.repeatInterval = +timeConvert.repeatInterval;
              }
              draft.trigger = { ...state.trigger, ...timeConvert };
              break;

            // EVENT_BASED fields
            case 'selectDayOfWeek': {
              const selectDayOfWeek = toAPISelectDayOfWeek(data);
              draft.trigger = { ...state.trigger, ...selectDayOfWeek };
              break;
            }
            case 'journeyGoals': {
              const journeyGoals = toConversonAPI(data);
              draft.trigger = { ...state.trigger, journeyGoals };
              break;
            }
            case 'selectTime': {
              const selectTime = getObjectPropSafely(
                () => toAPISelectTime(data).frequencyTime,
                {},
              );

              draft.trigger = { ...state.trigger, ...selectTime };
              break;
            }

            default:
              const index = draft.campaigns.findIndex(
                camp => camp.actionId === nodeId,
              );
              const campaignUpdate = produce(draft.campaigns[index], item => {
                switch (name) {
                  case 'destination':
                    item.campaignName = data.campaignName;
                    item.catalogId = get(
                      data,
                      'data.workflowDestination.value.catalogId',
                      '',
                    );
                    item.catalogLabel = get(
                      data,
                      'data.workflowDestination.value.label',
                      '',
                    );
                    item.nodeLabel = get(
                      data,
                      'data.workflowDestination.value.destinationName',
                      '',
                    );
                    item.channelId = data.channelId;
                    item.deliveryTimeConfig = {
                      mode: get(data, 'data.timeTarget.hoursType', ''),
                      range: get(data, 'data.timeTarget.dataSelected', []),
                      type: get(data, 'data.timeTarget.triggerType', ''),
                    };
                    item.sendAs = get(
                      data,
                      'data.workflowDestination.value.destinationId',
                      '',
                    );

                    item.variantIds = [...get(data, 'data.variantIds', [])];
                    item.variantInfo = toVariantAPI({
                      //
                      design: state.design,
                      variants: data.variants,
                      variantIdArray: [],
                      destination: data,
                      copyId: null,
                    });

                    item.campaignSetting = {
                      ...(item.campaignSetting || {}),
                      random: {
                        ...get(item, 'campaignSetting.random', {}),
                        ...get(data, 'campaignSetting', {}),
                      },
                    };
                    item.zoneId = get(data, 'zoneId');
                    item.contentPlacement = get(data, 'contentPlacement');
                    item.priority = get(data, 'priority');
                    item.isFitContent = get(data, 'isFitContent');

                    // set templateDesgin when web-personalizatiọn  --  MEDIA TEMPLATE || MEDIA JSON
                    const templateDesign = get(data, 'data.templateDesign', '');
                    if (templateDesign) {
                      draft.templateDesign = templateDesign;
                    }

                    break;
                  // SCHEDULED fields
                  case 'targetAudience':
                    const convertTargetAudience = toEntryAPI(data);
                    item.audiences = convertTargetAudience;
                    break;

                  // EVENT_BASED fields
                  case 'filter':
                    const filter = toAPINodeFilter(data, true);
                    item.filter = filter;
                    break;
                  default:
                    return draft.campaigns[index];
                }
              });
              draft.campaigns[index] = campaignUpdate;

              break;
          }
          return;
        }

        case `${PREFIX}@@RESET_AUDIENCE_CAMPAIGN@@${ReduxTypes.UPDATE_VALUE}`: {
          const { itemTypeId, isResetAudience = false } = action.payload;
          if (isResetAudience) {
            const initAudience = toEntryAPI({
              itemTypeId,
              isInit: false,
              backup: {},
              currentData: {
                includeCluster: [],
                excludeCluster: [],
                mapCluster: {},
              },
            });
            draft.campaigns = state.campaigns.map(campaign => ({
              ...campaign,
              audiences: initAudience,
            }));
          }
          break;
        }

        case `${PREFIX}@@TRIGGER_DRAWER_JOURNEY@@${ReduxTypes.UPDATE_VALUE}`: {
          const { channelId } = action.payload;
          const channelCode = getChannelCodeById(channelId);
          const currentTriggerType =
            DEFAULT_TRIGGER_TYPE[channelCode] || NODE_TYPE.SCHEDULED;
          // use case create journey drawer khi khác nhau actionType (ex: web >>> sms)
          draft.trigger = initialStateBlastCampaign(currentTriggerType).trigger;
          break;
        }

        case `${PREFIX}@@SET_DATA_SCHEDULED_THIRD_PARTY${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const {
            isThirdParty = false,
            scheduledThirdParty = {},
          } = action.payload;

          if (isThirdParty) {
            draft.trigger.isThirdParty = isThirdParty;
            draft.trigger.scheduledThirdParty = scheduledThirdParty;
          }

          break;
        }

        case `${PREFIX}@@CHANGE_DATA_SCHEDULED_THIRD_PARTY${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const newData = action.payload;

          if (isObject(newData)) {
            draft.trigger.scheduledThirdParty = {
              ...state.trigger.scheduledThirdParty,
              ...newData,
            };
          }
          break;
        }

        case `${PREFIX}@@FORCE_UPDATE_START_TIME_ACTIVE_ROW_SCHEDULE_3RD_PARTY${
          ReduxTypes.UPDATE_VALUE
        }`: {
          const newStartTime = action.payload;
          const startTimePath =
            'workflow_setting.metadata.scheduledThirdParty.startTime';

          if (has(state.activeRow, startTimePath)) {
            draft.activeRow.workflow_setting.metadata.scheduledThirdParty.startTime = newStartTime;
          }
          break;
        }

        default:
          return state;
      }
    });
  return mainReducer;
};

export default blastCampaignReducerFor;

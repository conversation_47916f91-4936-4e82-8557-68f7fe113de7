/* eslint-disable indent */
/* eslint-disable no-else-return */
/* eslint-disable no-param-reassign */
/* eslint-disable react/prop-types */
import { UIIconButton, UILoading } from '@xlab-team/ui-components';
import TargetAudienceMultiFilter from 'components/Templates/TargetAudienceFilter';
import UIDropdownAction from 'components/common/UIDropdownAction';
import useUpdateEffect from 'hooks/useUpdateEffect';
import React, { useEffect, useMemo } from 'react';
import { useImmer } from 'use-immer';
import COLOR from 'utils/colors';
import { generateKey, safeParse, getObjectPropSafely } from 'utils/common';
import EventAttributes from './_UI/EventAttributes';
import SelectTypeFilter from './_UI/SelectTypeFilter';
import WrapperAudienceAttributes from './_UI/WrapperAudienceAttribute';
import { Button, WrapperSelectType } from './styled';
import {
  DATA_OPTIONS_FILTER,
  MAP_DATA_OPTIONS_FILTER,
  initDataByTypeFilter,
  initDataUIFilter,
} from './utils';

const wrapperStyle = {
  width: '100%',
  justifyContent: 'flex-start',
  gap: 20,
};

const UINodeFilter = props => {
  const { initValue, isViewMode, isBlastCampaign = false } = props;
  const [state, setState] = useImmer(initDataUIFilter());
  const [stateLocal, setStateLocal] = useImmer({
    isInitDone: false,
    initKey: null,
  });

  const canRenderEle = useMemo(
    () =>
      !isBlastCampaign ||
      (isBlastCampaign &&
        initValue &&
        initValue.filterType &&
        initValue.filterType.value),
    [
      isBlastCampaign,
      initValue && initValue.filterType && initValue.filterType.value,
    ],
  );

  useEffect(() => {
    setStateLocal(draft => {
      draft.isInitDone = false;
      draft.initKey = generateKey();
    });

    return () => {
      setState(() => initDataUIFilter());
      setStateLocal(draft => {
        draft.isInitDone = false;
        draft.initKey = null;
      });
    };
  }, [props.componentId]);

  useUpdateEffect(() => {
    // setState(() => initDataUIFilter());
    if (stateLocal.initKey !== null) {
      if (Object.keys(safeParse(initValue, {})).length > 0) {
        const filterType = getObjectPropSafely(
          () => MAP_DATA_OPTIONS_FILTER[initValue.filterType.value] || {},
          {},
        );

        setState(draft => {
          draft.isFetchInfoData = true;
          draft.filterType = filterType;
          if (initValue.event_attribute) {
            draft.event_attribute = initValue.event_attribute;
          }
          if (initValue.item_segment) {
            draft.item_segment = initValue.item_segment;
          }
          if (initValue.user_attributes) {
            draft.user_attributes = initValue.user_attributes;
          }
        });
      }
      setStateLocal(draft => {
        draft.isInitDone = true;
      });
    }

    // return () => {
    //   setState(() => initDataUIFilter());
    // };
  }, [stateLocal.initKey]);

  useUpdateEffect(() => {
    props.onChange(state);
  }, [state]);

  const setStateCommon = objects => {
    setState(draft => {
      Object.keys(objects).forEach(key => {
        draft[key] = objects[key];
      });
    });
  };

  const onChange = (type, data) => {
    setStateCommon({ [type]: data });
  };

  const onChangeTypeFilter = (key, data) => {
    setStateCommon({
      [`${key}`]:
        MAP_DATA_OPTIONS_FILTER[data] ||
        MAP_DATA_OPTIONS_FILTER.user_attributes,
      [`${data}`]: initDataByTypeFilter[data],
    });
  };

  const onChangeSegment = data => {
    setState(draft => {
      draft.item_segment = data;
    });
  };
  const onChangeEventAttributes = data => {
    setState(draft => {
      draft.event_attribute = data;
    });
  };

  const onChangeType = option => {
    setState(draft => {
      draft.filterType = option;
    });
  };

  const onRemoveFilter = () => {
    setState(draft => {
      draft.filterType = {};
    });
  };

  const renderContentByTypeDelay = () => {
    if (state.filterType.value === 'item_segment') {
      return (
        <div style={{ maxWidth: '50rem' }}>
          <TargetAudienceMultiFilter
            itemTypeId={props.itemTypeId}
            initData={
              Object.keys(safeParse(initValue, {})).length === 0
                ? undefined
                : initValue.item_segment
            }
            onChange={onChangeSegment}
            validateKey={props.validateKey}
            componentId={stateLocal.initKey}
            isViewMode={isViewMode}
            isBlastCampaign={isBlastCampaign}
          />
        </div>
      );
    } else if (state.filterType.value === 'user_attributes') {
      return (
        <WrapperAudienceAttributes
          onChange={onChange}
          initData={
            Object.keys(safeParse(initValue, {})).length === 0
              ? undefined
              : initValue.user_attributes
          }
          isInit={
            Object.keys(safeParse(initValue, {})).length === 0
              ? false
              : initValue.isInitUserAttrs
          }
          validateKey={props.validateKey}
          componentId={stateLocal.initKey}
          itemTypeId={props.itemTypeId}
          isShowFullOption={props.triggerType !== 'SCHEDULED'}
          disabled={props.disabled}
          isViewMode={isViewMode}
        />
      );
    } else if (state.filterType.value === 'event_attribute') {
      let dataRules;

      if (initValue.event_attribute && initValue.event_attribute.rules) {
        dataRules = initValue.event_attribute.rules;
      }
      return (
        <EventAttributes
          moduleConfig={props.moduleConfig}
          onChange={onChangeEventAttributes}
          eventValue={props.eventValue}
          initData={dataRules}
          validateKey={props.validateKey}
          componentId={stateLocal.initKey}
          disabled={props.disabled}
          isViewMode={isViewMode}
        />
      );
    }

    return null;
  };

  if (!stateLocal.isInitDone) {
    return <UILoading isLoading />;
  }

  if (!state.filterType.value) {
    return (
      <UIDropdownAction
        isShowAddIcon={false}
        disabled={isViewMode}
        PopoverAnchor={PopoverAnchor}
        options={DATA_OPTIONS_FILTER}
        callback={onChangeType}
      />
    );
  }

  return (
    <>
      <WrapperSelectType style={isBlastCampaign ? wrapperStyle : {}}>
        {canRenderEle && (
          <SelectTypeFilter
            onChange={onChangeTypeFilter}
            initData={initValue.filterType}
            componentId={stateLocal.initKey}
            isActionBase={props.triggerType !== 'SCHEDULED'}
            disabled={props.disabled}
            design={props.design}
            isBlastCampaign={isBlastCampaign}
            isViewMode={isViewMode}
          />
        )}
        {!isViewMode ? (
          <UIIconButton
            iconName="close"
            size="20px"
            color={COLOR.primary}
            onClick={onRemoveFilter}
          />
        ) : null}
      </WrapperSelectType>
      {canRenderEle && renderContentByTypeDelay()}
    </>
  );
};

const PopoverAnchor = props => (
  <Button
    iconName="add"
    theme="outline"
    reverse
    iconSize="18px"
    disabled={props.disabled}
    onClick={props.onClick}
  >
    <span>Add Targeting</span>
  </Button>
);

UINodeFilter.defaultProps = {
  initData: {},
  onChange: () => {},
  validateKey: 1,
  disabled: false,
};

export default UINodeFilter;

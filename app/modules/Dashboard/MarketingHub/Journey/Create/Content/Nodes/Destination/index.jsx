/* eslint-disable no-loop-func */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-restricted-syntax */
/* eslint-disable camelcase */
/* eslint-disable react/prop-types */
/* eslint-disable no-param-reassign */
/* eslint-disable indent */
import React, {
  memo,
  useCallback,
  useEffect,
  useRef,
  Fragment,
  useMemo,
  useState,
} from 'react';
import _isEmpty from 'lodash/isEmpty';
import _cloneDeep from 'lodash/cloneDeep';
import { useImmer } from 'use-immer';
import { original } from 'immer';
import { isMap as isImmutableMap } from 'immutable';
import _, { get, keyBy, isEqual, has, isEmpty } from 'lodash';

// Hooks
import { useFetchExtraSourceBitrixDestination } from './hooks';

import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import Grid from '@material-ui/core/Grid';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  UILoading as Loading,
  UITabs,
  TabPanel,
} from '@xlab-team/ui-components';

import {
  Flex,
  Spin,
  Steps,
  useSelectEmitContentSourceItemType,
} from '@antscorp/antsomi-ui';
import { createStructuredSelector } from 'reselect';
import classNames from 'classnames';
import UIIconXlab from 'components/common/UIIconXlab';
import ErrorBoundary from 'components/common/ErrorBoundary';
import { MODULE_CONFIG } from 'containers/Segment/Preview/config';
import PreviewForeCast from 'containers/Segment/Preview/PieForecast';
import { MODULE_CONFIG as JOURNEY_MODULE_CONFIG } from '../../../../config';
import VariantTab from './_UI/UIVariant';
import WebIframe from './Testing/WebIframe';

import JourneyServices from '../../../../../../../../services/Journey';
import DestinationServices from 'services/Destination';
import {
  RootContainer,
  Title,
  useStyles,
  DisplayContent,
  WrapperTextAudiences,
  IconWrapper,
  WrapperTargetAudience,
  Divider,
  AccordionCustom,
  AccordionSummaryCustom,
  HeadingAccordion,
  SubHeadingAccordion,
  AccordionDetailsCustom,
  Wrapper,
  WrapperTitleCustomInput,
  DividerDash,
  AccordionStyled,
  WrapperListTemplateZNS,
} from './styles';
import { useFetchInfoData } from './useFetchData';
import {
  checkIsHiddenInputComponent,
  getDataMappingGroupConfigs,
  getViewPagesDefault,
  handleSetDefaultURLSettingSmartInbox,
  getWidthCustomInput,
  isValidateFields,
  toDesinationInputAPI,
  toEntryReducer,
  toFormValue,
  getSelectedTemplate,
  mapCustomFunctionTemplate,
  validateCustomInputCampaign,
  validateCustomInputVariant,
  colGridWrapper,
  reUpdateDataByCatalog,
  isValidateWithCondition,
  getJourneySettings,
  getCurrentTemplateInputByChannel,
} from './utils';
import { DATA_CONTENT_PLACEMENT } from './variant.config';
import Preview from './Preview';
import { STORY_SETUP_ACTION } from '../../../utils.story.rules';
import Testing from './Testing';
import {
  makeSelectConfigureCreateWorkflow,
  makeSelectMainCreateWorkflow,
  makeSelectMainReducerCreateWorkflow,
  makeSelectOpenModalDestinationTesting,
  makeSelectUseAllocatedCodeStatus,
} from '../../../selectors';
import { remove, updateValue } from '../../../../../../../../redux/actions';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import {
  buildValueFormVariantFromCache,
  collectTagIdsFromVariantExtraData,
  createFeVarinatId,
  getNewDestinationInputConfigs,
  initDefaultData,
  initVariantExtraData,
  rebuildSettingDestinationInputs,
  reBuildValueCampaignSetting,
  setVariantCacheInfo,
  updateExtraAttribute,
  updateExtraDataTagProperty,
  validateVariantContent,
} from './utils.state';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';
import usePrevious from '../../../../../../../../hooks/usePrevious';
// import { StylesToggleButton } from '../../../../../../../../components/Atoms/ToggleButton/styles';
import ModalAlgorithms from '../../../../../../../../containers/UIDev/ModalVariantPriority';
import {
  getObjectPropSafely,
  safeParse,
} from '../../../../../../../../utils/common';
import PreviewObjectWidget from './Preview/PreviewObjectWidget';
import { mapValueToFe } from '../../../../../../../../containers/UIDev/ModalVariantPriority/utils';
import { toAPIPerformEvent } from '../../../../../../../../components/common/UIPerformEvent/utils';
import { CATALOG_CODE, CHANNEL_CODE, NODE_TYPE } from '../constant';
import {
  EMAIL_TEMPLATE,
  JSON_TEMPLATE,
  MEDIA_TEMPLATE,
} from '../../../../../../../../components/common/UIEditorPersonalization/utils';
import {
  makeSelectStoryDetailDomainMain,
  makeSelectStoryDetailVersionId,
} from '../../../../Detail/selectors';
import { getPrefixDetail } from '../../../../Detail/VersionHistory2/Detail/utils';
import WrapperSelectHoursOfDay from '../../../../../../../../containers/UIDev/SelectHoursOfDay/WrapperSelectHoursOfDay';
import {
  DESTINATION_TYPE,
  OBSERVER_VIEW_DEVICES,
  PRE_SELECT_STICKER_SET,
  SNAPSHOT_RENDER_FIELD,
  UPDATE_REQUIRED_FIELDS,
  LIST_FIELD_BY_CATALOG,
  ICON_TYPE_KEY,
  GEN_RATING_TEXT,
  RATING_SETTING_KEYS,
  ICON_OPTION,
  initRatingTextValue,
  DEFAULT_ICON_SELECT_VARIANTS,
} from './constants';
import TargetAudienceV2 from '../../../../../../../../components/Templates/TargetAudienceMulti/TargetAudienceMultiV2';
import {
  TEMPLATE_LIST,
  TEMPLATE_TYPES,
  TEMPLATE_ZALO_OA_TYPES,
} from '../../../../../Destination/CreateV2/Design/Templates/constants';
import { STICKER_FACTORY } from '../../../../../../../../components/Molecules/Sticker/constants';
import { filterCustomInputAttributes } from '../utils';

import { serializeSticker } from '../../../../../../../../components/Molecules/Sticker/utils';
import AntsomiLineFieldRender from '../../../../../../../../components/Organisms/DestinationSnapRender';
import useDebounce from '../../../../../../../../hooks/useDebounce';
import {
  makeSelectCreatingJourneyInfo,
  makeSelectThirdPartyCampaignInfo,
} from '../../../../selectors';
import { ListTemplates } from './_UI/ListTemplates/ListTemplates';
import { useDeepCompareEffect } from '../../../../../../../../hooks';
import { MAP_INPUT_TYPE, MAP_VALIDATE } from './utils.form';
import {
  TEMPLATES,
  TEMPLATE_KEYS,
} from 'containers/UIPreview/AppPushPreview/AppPushTemplate/constants';
import {
  getInputValueObjType,
  resetDataVariantAntsomiAppPush,
} from './utils.variant';
import { getChannelCodeById, isDetailDrawer } from '../../../../utils';
import { useHistory } from 'react-router-dom';
import makeSelectSegmentPreview from '../../../../../../../../containers/Segment/Preview/selectors';
import PreHeading from './_UI/PreHeading';
import { updateTags } from '../../../../../../../../components/Molecules/Personalizable/utils';
import { initSyncExtraDataProperties } from '../../../_reducer/configure';
import { defaultState } from './_UI/WhatsappTemplate/utils';

const MAX_VARIANTS = 5;

// Khi copy +1 thêm 1 variants mà nhỏ hơn 5 variant thì cho tiếp tục
const MY_SELF_VARIANT = 1;

const PATH =
  'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/index.jsx';

export const InputComponent = memo(props => {
  const preHeadingProps = get(props, 'preHeading', {
    title: '',
    showInfoIcon: false,
  });

  const content =
    typeof props.componentEl === 'function' ? props.componentEl(props) : null;

  const dataTest = [
    'input-component-wrapper',
    `key-${props.name}` || '',
    `comp-${props.componentEl?.displayName || props.componentEl?.name || ''}`,
  ];

  return (
    <Grid
      data-test={dataTest.join(' ')}
      container
      className={`${props.classes.item} ${
        props.classes.isCustomSpace &&
        props.isViewMode &&
        props.catalogCode === 'trello'
          ? 'custom-space'
          : ''
      }`}
      style={{ ...props.style, display: props.isHidden && 'none' }}
    >
      <PreHeading {...preHeadingProps} inputType={props.inputType} />
      {content}
    </Grid>
  );
});

const mapTranslateMessage = {
  settings: getTranslateMessage(TRANSLATE_KEY._MENU_SETTINGS, 'Settings'),
  compose: getTranslateMessage(TRANSLATE_KEY._TITL_COMPOSE, 'Compose'),
  maximumVariant: getTranslateMessage(
    TRANSLATE_KEY._TITL_MAXIMUM_VARIANT,
    'Maximum 5 variants',
  ),
  labelActionPreview: getTranslateMessage(
    TRANSLATE_KEY._ACT_PREVIEW,
    'Preview',
  ),
  labelAudiences: getTranslateMessage(
    TRANSLATE_KEY._ACT_VARIANT_PRIORITY,
    '% Audiences',
  ),
  warnNoTemplate: getTranslateMessage(
    TRANSLATE_KEY._,
    'No template available for this destination',
  ),
};

function Destination(props) {
  const classes = useStyles();
  const {
    initData = {},
    onChange,
    activeNode,
    itemTypeId,
    eventValue,
    triggerType,
    main,
    isEnableTesting,
    errorKey,
    componentId,
    isUseTemplateCampaign,
    isUseTemplateCampaignSameJr,
    itemSquareSelected = {},
    configure,
    versionId,
    isViewMode,
    detailJourney,
    isDefaultRichMenu = false,
    isBlastCampaign = false,
    isJourneyTemplateMode = false,
    isDisplayDiagramBlastCampaign = false,
    mainReducer = {},
    creatingJourneyInfo = {},
    isThirdPartyCampaigns = false,
    // sendMethodThirdParty = null,
  } = props;
  const { activeRow = {}, design } = main;
  const {
    actionNode,
    nodes,
    triggerNode,
    triggerEvent = {},
    disabledIncreAmount = false,
    errors,
    properties,
    syncExtraDataProperties,
  } = configure.main;
  const { story_id, is_blast_campaign } = activeRow;
  const selectedDesign = itemSquareSelected.value;
  const toOutput = dataIn => {
    const { channelCode } = props.activeNode;
    const out = toEntryReducer({
      ...dataIn,
      channelCode,
    });
    onChange('destination', out);
  };
  const [previewData, setPreviewData] = useState({});
  const [activeStep, setActiveStep] = useState(0);
  const [selectionStartIndex, setSelectionStartIndex] = useState(null);
  const [targetAudienceData, setTargetAudienceData] = useState(null);
  const [disabledInc, setDisabledInc] = useState(false);
  const [observeDeviceType, setObserveDeviceType] = useState('ios');
  const [scrollIndexView, setScrollIndexView] = useState(null);
  const [activeTabDisplay, setActiveTabDisplay] = useState('');
  const [refreshComponentKey, setRefreshComponentKey] = useState(1);
  const [emitSetDefaultSmartInbox, setEmitSetDefaultSmartInbox] = useState(1);
  const [templateSelectedZNS, setTemplateSelectedZNS] = useState({});
  const scrollIndexDebounce = useDebounce(scrollIndexView, 500);

  const searchParams = new URLSearchParams(window.location.search);

  const [audiencesState, setAudiencesState] = useImmer({
    isOpenModal: false,
    data: {
      list: [],
      priority: {},
    },
  });

  const [testState, setTestState] = useImmer({
    data: {
      itemTypeId,
      destinationId: '',
      catalogId: '',
      destinationInput: '',
      variantExtraData: {},
      dynamicFields: [],
      objectWidgetInput: {},
      testingAudienceIds: [],
      storyId: design === 'create' ? 0 : story_id,
      nodeIds: activeNode.nodeId,
    },
    isOpenModal: false,
  });

  const [isFetchData, setIsFetchData] = useState(false);
  const previousComponentId = usePrevious(componentId);

  const timerUpdateExtraTagPropertyRef = useRef(null);

  const journeyNodeTagProperties = useMemo(() => {
    const pathProperties = ['nodes', props.activeNode?.nodeId, 'tags'];

    // Come from Journey Tactic
    if (
      isJourneyTemplateMode &&
      isImmutableMap(props.journeyNodeTagProperties)
    ) {
      return props.journeyNodeTagProperties.getIn(pathProperties);
    }

    if (!isImmutableMap(properties)) return undefined;
    return properties.getIn(pathProperties);
  }, [
    properties,
    isJourneyTemplateMode,
    props.journeyNodeTagProperties,
    props.activeNode?.nodeId,
  ]);

  const selectedTemplateDashboard = useMemo(() => {
    if (!isBlastCampaign) return props.selectedTemplate;

    return getObjectPropSafely(
      () =>
        (creatingJourneyInfo.data && creatingJourneyInfo.data.template) || {},
    );
  }, [props.selectedTemplate, creatingJourneyInfo]);

  const [{ data, isLoading, isLoadingCompose }, { setData }] = useFetchInfoData(
    {
      ServiceFn: JourneyServices.data.getDestinationByCatalogIds,
      ServiceOptionsFn: JourneyServices.data.getOptions,
      ServiceCustomInput: JourneyServices.data.getAttrsCustomInput,
      initData,
      activeNode,
      itemTypeId,
      eventValue,
      triggerType,
      isUseTemplateCampaign,
    isUseTemplateCampaignSameJr,
      selectedDesign: itemSquareSelected.value,
      design,
      actionNode,
      versionId,
      extendListDestFilterFn: listDest => {
        const { channelCode = '', catalogCode = '' } = activeNode;
        const currentTab = searchParams.get('tab');
        const isScheduleHistory = currentTab === 'schedule-history';

        // In case journey orchestration
        if (!isBlastCampaign && !isScheduleHistory) {
          const isChannelSms = CHANNEL_CODE.SMS === channelCode;

          const { SMS_FPT, SMS_FPT_NEW } = CATALOG_CODE;

          const isSmsFPT = [SMS_FPT, SMS_FPT_NEW].includes(catalogCode);

          // Need to filter out sendCSKH method from list for SMSFPT
          if (isChannelSms && isSmsFPT) {
            return listDest.filter(dest => {
              const method = get(dest, 'destinationSetting.method', '');

              return method !== 'sendCSKH';
            });
          }
        }

        // old case
        return listDest;
      },
      destinationInfo: {
        isBlastCampaign,
        amount: configure.main && configure.main.amountDestinations,
        disabledIncreAmount,
        selectedTemplateDashboard,
        onUpdateAmount: props.onUpdateAmountDestination,
        flattenNodes: configure.main && configure.main.flattenNodes,
        current: [...(props.campaignList || [])].find(
          item => props.activeNode && item.actionId === props.activeNode.nodeId,
        ),
        configComponent: props.configComponent,
      },
    },
  );

  useFetchExtraSourceBitrixDestination({
    data,
    setData,
    nodeId: activeNode?.nodeId,
    onMergeListTagProperties: props.onMergeListTagProperties,
  });

  const emitItemTypeContentSource = useSelectEmitContentSourceItemType();
  // For case update journey
  const designType = get(data, 'variantExtraData.content.type', '');
  const isChannelWebPersonalization =
    activeNode && activeNode.channelCode === 'web_personalization';
  const { channelCode = getChannelCodeById(data.channelId) } = activeNode || {};
  const isShowPreview =
    !['email', 'web_personalization'].includes(channelCode) &&
    ![CATALOG_CODE.LINE_RICH_MENU].includes(get(activeNode, 'catalogCode', ''));
  const colLeftCampaign = 'auto';
  const colRightCampaign = 'auto';
  const colLeftVariant = 'auto';
  const colRightVariant = 'auto';

  const displayForecast =
    isBlastCampaign && triggerType === NODE_TYPE.SCHEDULED;

  const variantHardFieldSettings = useMemo(() => {
    const currentMethod = get(
      data,
      'workflowDestination.value.destinationSetting.method',
    );
    return (
      get(data, ['inputConfig', currentMethod, 'variantSetting'], {}) || {}
    );
  }, [data]);

  const safeVariantHFSetting = useCallback(
    (fieldName, attr, fallback) =>
      get(variantHardFieldSettings, [fieldName, attr], fallback),
    [variantHardFieldSettings],
  );

  // Used for ZaloOA
  const messageType = useMemo(
    () => data?.destinationInput?.transactionType?.value,
    [data?.destinationInput?.transactionType?.value],
  );

  const isLineRichMenu = useMemo(
    () => get(activeNode, 'catalogCode', '') === CATALOG_CODE.LINE_RICH_MENU,
    [activeNode],
  );

  const [
    insightPropertyId,
    zoneId,
    zoneRenderType,
    variantExtraData,
  ] = useMemo(() => {
    if (
      activeNode &&
      activeNode.channelCode === 'web_personalization' &&
      nodes &&
      (isBlastCampaign || (triggerNode && Object.keys(triggerNode).length))
    ) {
      const { variantExtraData: variantData } = data;

      if (!variantData) {
        return [];
      }

      const zoneInfo = getObjectPropSafely(
        () => data.zoneId.value.value || data.zoneId.value,
        '',
      );
      const contentPlacement = getObjectPropSafely(
        () => data.contentPlacement.value.value,
        '',
      );

      if (isBlastCampaign && isChannelWebPersonalization) {
        const event = getObjectPropSafely(() => mainReducer.trigger.event, {});

        return [
          getObjectPropSafely(() => event.insightPropertyIds[0]),
          zoneInfo,
          contentPlacement,
          variantData,
        ];
        // eslint-disable-next-line no-else-return
      } else {
        const triggerNodeMap = nodes.get(triggerNode.nodeId);

        const triggerPerformEvent = triggerNodeMap.get('peformEvent');

        const dataPerformEvent = toAPIPerformEvent(triggerPerformEvent);

        return dataPerformEvent
          ? [
              safeParse(dataPerformEvent.insightPropertyIds[0]),
              zoneInfo,
              contentPlacement,
              variantData,
            ]
          : [];
      }
    }

    return [];
  }, [nodes, triggerNode, activeNode, isBlastCampaign, data]);

  const dataDestinationSettings = useMemo(() => {
    const workflowDestinationValue = getObjectPropSafely(
      () => data.workflowDestination.value || {},
    );

    if (!isBlastCampaign) {
      return getObjectPropSafely(
        () => workflowDestinationValue.destinationSetting,
        {},
      );
    }

    if (has(workflowDestinationValue, 'destinationSetting'))
      return workflowDestinationValue.destinationSetting;

    const { cachedWorkflowDestinationInfo = [] } = data;

    if (
      Array.isArray(cachedWorkflowDestinationInfo) &&
      !_isEmpty(workflowDestinationValue)
    ) {
      const destinationInfo = cachedWorkflowDestinationInfo.find(
        eachItem =>
          +eachItem.destinationId === +workflowDestinationValue.destinationId,
      );

      if (!_isEmpty(destinationInfo)) {
        return getObjectPropSafely(() => destinationInfo.destinationSetting);
      }
    }

    return {};
  }, [data]);

  const contentAccentType = useMemo(
    () => get(dataDestinationSettings, 'contentType', ''),
    [dataDestinationSettings],
  );

  const tokenLineRichMenu = useMemo(() => {
    if (!isLineRichMenu) return '';
    return get(dataDestinationSettings, 'token', '');
  }, [isLineRichMenu, dataDestinationSettings]);

  const smartInboxConfigs = useMemo(
    () =>
      getObjectPropSafely(
        () =>
          (dataDestinationSettings &&
            dataDestinationSettings.smartInboxConfigs) ||
          {},
      ),
    [dataDestinationSettings],
  );

  const isTitleAlignLeftVariant = useMemo(() => {
    if (!activeNode || !activeNode.catalogCode) return false;
    const {
      ONE_SIGNAL_APP_PUSH,
      FIRE_BASE_APP_PUSH,
      CARESOFT,
      VIETTEL,
      ACFC_APP_PUSH,
      INFOBIP_WHATSAPP_TEMPLATE,
    } = CATALOG_CODE;

    return [
      ONE_SIGNAL_APP_PUSH,
      FIRE_BASE_APP_PUSH,
      CARESOFT,
      VIETTEL,
      ACFC_APP_PUSH,
      INFOBIP_WHATSAPP_TEMPLATE,
    ].includes(activeNode.catalogCode);
  }, [activeNode && activeNode.catalogCode]);

  const contentType = useMemo(() => {
    let result = '';
    if (!has(data, 'destinationInput.contentType')) return result;

    result = get(data, 'destinationInput.contentType.value', '');
    if (_.isObject(result)) result = result.value;
    return result;
  }, [data && data.destinationInput && data.destinationInput.contentType]);
  const isRecommendation = contentType === 'recommendation';

  const isWrapInPanelBox = useMemo(
    () =>
      isRecommendation &&
      [CATALOG_CODE.ONE_SIGNAL_APP_PUSH].includes(activeNode.catalogCode),
    [isRecommendation, activeNode && activeNode.catalogCode],
  );

  const contentSourceGroups = useMemo(() => {
    if (!_.has(data, 'destinationInput.contentSources')) return [];

    const groups = _.get(data, 'destinationInput.contentSources.value.groups');

    if (_.isArray(groups) && groups.length > 0) {
      return groups.map(group =>
        _.pick(group, [
          'groupId',
          'itemTypeDisplay',
          'itemTypeName',
          'itemTypeId',
        ]),
      );
    }
    return [];
  }, [data && data.destinationInput && data.destinationInput.contentSources]);

  const appendPersonalizeType = useMemo(
    () =>
      contentSourceGroups.map(group => ({
        groupId: group.groupId,
        label: group.itemTypeDisplay,
        name: group.itemTypeName,
        itemTypeId: group.itemTypeId,
        color: '#FFDD9F',
      })),
    [contentSourceGroups],
  );

  const triggerNodeInNodes = useMemo(() => {
    const empty = new Map();
    if (_.isEmpty(triggerNode) || nodes.size === 0) return empty;

    const triggerNodeId = _.get(triggerNode, 'nodeId');
    if (nodes.has(triggerNodeId)) return nodes.get(triggerNodeId);

    return empty;
  }, [nodes, triggerNode]);

  const journeySettings = useMemo(
    () =>
      getJourneySettings({
        isBlastCampaign,
        triggerType,
        triggerNode: triggerNodeInNodes,
        itemTypeId: configure.main.itemTypeId || itemTypeId,
        triggerEvent: isBlastCampaign
          ? mainReducer?.trigger?.event
          : triggerEvent,
      }),
    [
      triggerEvent,
      triggerType,
      isBlastCampaign,
      triggerNodeInNodes,
      configure.main.itemTypeId || itemTypeId,
      mainReducer?.trigger?.event,
    ],
  );

  useEffect(() => {
    if (
      syncExtraDataProperties?.isSync &&
      isImmutableMap(nodes) &&
      nodes.has(activeNode?.nodeId)
    ) {
      setData(draft => {
        draft.isCallback = false;

        if (_.isSet(syncExtraDataProperties.variantIds)) {
          syncExtraDataProperties.variantIds.forEach(id => {
            const propertiesPath = [
              'variants',
              'cacheInfo',
              id,
              'variantExtraData',
              'properties',
            ];

            const path = [activeNode.nodeId, 'destination', ...propertiesPath];
            const newProperties = nodes.getIn(path);

            if (newProperties) {
              _.set(draft, propertiesPath, newProperties);

              // if changed variant active
              // Need to reset variantExtraData at root state
              if (draft.variants.activeId === id) {
                _.set(draft, ['variantExtraData', 'properties'], newProperties);
              }
            }
          });
        }
      });

      props.updateSyncExtraDataProperties(
        initSyncExtraDataProperties({ isSync: false, variantIds: [] }),
      );
    }
  }, [syncExtraDataProperties, nodes, activeNode?.nodeId]);

  useEffect(() => {
    // Initial active first tab
    if (activeTabDisplay === '' && !_isEmpty(groupConfigsMapping)) {
      setActiveTabDisplay(groupConfigsMapping[0].key);
    }
  }, [groupConfigsMapping, data && data.destinationInput]);

  useEffect(() => {
    clickPreview();
  }, [data.variants.activeId]);
  useEffect(() => {
    if (props.updateFreshNodesKey > 0) {
      setData(draft => {
        draft.isCallback = false;
        draft.isFetchInfoData = initData.isFetchInfoData;
        draft.variantIds = initData.variantIds;
        draft.campaignId = initData.campaignId;
      });
    }
  }, [props.updateFreshNodesKey]);

  useEffect(() => {
    if (
      isBlastCampaign &&
      props.campaignList &&
      props.campaignList.length > 1 &&
      design !== 'create' &&
      !disabledInc
    ) {
      props.onUpdateAmountDestination({
        amount: props.campaignList.length,
      });
      setDisabledInc(true);
    }

    return () => {
      props.onUpdateAmountDestination({
        amount: 0,
        disabledIncreAmount: false,
      });
    };
  }, [props.campaignList]);

  useEffect(
    () =>
      // clean up timeTarget khi chuyển sang Campaign khác
      () => {
        if (isBlastCampaign) {
          setData(draft => initDefaultData());
        }
      },
    [componentId],
  );

  useEffect(() => {
    // if (props.updateFreshNodesKey > 0) {
    try {
      if (initData.data) {
        const { invalidFields = [] } = initData.data;
        setData(draft => {
          draft.isCallback = true;
          invalidFields.forEach(each => {
            draft.invalidFields = invalidFields;
            draft[each].errors = initData.data[each].errors;
            draft[each].isValidate = initData.data[each].isValidate;
            const isValidate1 = [
              ...data.defaultFields,
              ...data.variantHardFields,
            ].every(each1 => draft[each1].isValidate);
            const isValidate2 = [...draft.dynamicFields].every(
              each1 => draft.destinationInput[each1].isValidate,
            );

            const isValidateCustomInputCampaign = validateCustomInputCampaign(
              draft,
            );

            const isValidateCustomInputVariant = validateCustomInputVariant(
              draft,
            );

            draft.isValidate =
              isValidate1 &&
              isValidate2 &&
              isValidateCustomInputCampaign &&
              isValidateCustomInputVariant;
          });
        });
      }
    } catch (err) {
      addMessageToQueue({
        path:
          'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/index.jsx',
        func: 'useEffect [errorKey]',
        data: err.stack,
      });
      console.log(err);
    }
    // }
    // props.initData
  }, [errorKey]);

  useEffect(
    () => () => {
      setPreviewData({});
    },
    [],
  );

  useEffect(() => {
    if (audiencesState.isOpenModal) {
      setAudiencesState(draft => {
        draft.data.list = mapValueToFe({
          list: data.variants.list,
          priority: data.campaignSetting.priority,
          cacheInfo: data.variants.cacheInfo,
        });
      });
    }
  }, [audiencesState.isOpenModal]);

  useEffect(() => {
    let isEnableButtonQuickTest = false;
    if (data.isInitDone) {
      setData(draft => {
        draft.isCallback = true;
        // draft.workflowDestination.disabled =
        //   !props.roleActions.has(
        //     STORY_SETUP_ACTION.EDIT_NODE_DESTINATION_TYPE,
        //   ) && props.previousNodes[componentId] !== undefined;
        // validateCustomInputCampaign(draft);
        // validateCustomInputVariant(draft);
      });

      // set data preview tu data cua node
      const newPreviewData = {};
      // console.log('data.dynamicFields', data, data.dynamicFields);
      data.dynamicFields.forEach(each => {
        // console.log('each', each);

        const item = data.destinationInput[each];
        if (!_isEmpty(item.previewFieldCode)) {
          newPreviewData[item.previewFieldCode] = item.value;
        }
      });

      if (initData.role !== 'RESET') {
        setPreviewData(newPreviewData);
      }
      isEnableButtonQuickTest = true;
    } else {
      isEnableButtonQuickTest = false;
      // props.updateEnabledButtonTesting(false);
    }

    const isJsonTemplate =
      +selectedDesign === DESTINATION_TYPE.JSON_TEMPLATE ||
      designType === JSON_TEMPLATE;

    if (activeNode.channelCode === 'web_personalization' && isJsonTemplate) {
      isEnableButtonQuickTest = false;
    }

    if (isBlastCampaign && props.configComponent) {
      const { defaultFields, updateComponent } = props.configComponent;
      setData(draft => {
        draft.defaultFields = defaultFields;
        Object.keys(updateComponent).forEach(key => {
          draft[key] = {
            ...draft[key],
            ...updateComponent[key],
          };

          if (
            key === 'workflowDestination' &&
            _.has(updateComponent, key) &&
            design === 'create'
          ) {
            const { options = [] } = updateComponent[key];
            const originalState = original(draft);
            const destinationId = _.get(
              originalState,
              'workflowDestination.value.destinationId',
            );

            if (destinationId && _.isArray(options)) {
              const destinationIdx = options.findIndex(
                opt => opt.destinationId === destinationId,
              );

              if (destinationIdx === -1) {
                const workflowDestinationValue = options[0];
                if (workflowDestinationValue) {
                  onChangeData('workflowDestination')(workflowDestinationValue);
                }
              }
            }
          }
        });
      });
    }

    props.updateEnabledButtonTesting(isEnableButtonQuickTest);
  }, [data.isInitDone, data.campaignId, selectedDesign]);

  useEffect(() => {
    // Vi di tu 1 node destination khac sang, thi gia tri initData vs data bi update nhieu phai lam loi phan validate
    if (data.isInitDone && previousComponentId === componentId) {
      setData(draft => {
        draft.isCallback = true;
        [
          ...data.defaultFields,
          ...data.webChannelFields,
          ...data.variantHardFields,
        ].forEach(each => {
          const { errors, isValidate } = data[each].validate(data[each]);
          draft[each].errors = errors;
          draft[each].isValidate = isValidate;
          return isValidate;
        });
        [...data.dynamicFields].forEach(each => {
          // console.log('each', {
          //   each,
          //   dataEach: data.destinationInput[each],
          //   data,
          // });
          // const { errors, isValidate } =
          //   draft.destinationInput[each] ||
          //   {}.validate(draft.destinationInput[each]);
          // console.log('object', each, '----', JSON.parse(JSON.stringify(data.destinationInput[each])));
          // if (typeof data.destinationInput[each].validate === 'function') {
          const { errors, isValidate } = data.destinationInput[each].validate({
            ...data.destinationInput[each],
            channelCode: data.channelCode,
            catalogCode: data.catalogCode,
            destinationInput: data.destinationInput,
            contentAccentType,
          });

          draft.destinationInput[each].errors = errors;
          draft.destinationInput[each].isValidate = isValidate;
          return isValidate;
          // }
          // return true;
        });
        // draft.isValidate = [
        //   ...draft.defaultFields,
        //   ...draft.dynamicFields,
        // ].every(each => draft[each].isValidate);
        const isValidate1 = [
          ...data.defaultFields,
          ...data.variantHardFields,
        ].every(each => draft[each].isValidate);
        const isValidate2 = [...draft.dynamicFields].every(
          each => draft.destinationInput[each].isValidate,
        );

        // validate customInput

        const isValidateCustomInputCampaign = validateCustomInputCampaign(
          draft,
        );
        // console.log(
        //   'draft___',
        //   JSON.parse(JSON.stringify(draft.campaignCustomInput)),
        // );
        const isValidateCustomInputVariant = validateCustomInputVariant(draft);

        draft.isValidate =
          isValidate1 &&
          isValidate2 &&
          isValidateCustomInputCampaign &&
          isValidateCustomInputVariant;
      });
    }
  }, [props.validateKey]);

  useEffect(() => {
    if (!data.isExistedDestination || data.isCallback === false) return;
    toOutput(data);
  }, [data]);

  useEffect(() => {
    if (isZaloZNS) {
      setTemplateSelectedZNS(creatingJourneyInfo);
    }
  }, [isZaloZNS, creatingJourneyInfo]);

  useEffect(() => {
    if (props.isOpenModalDestinationTesting) {
      const { channelCode } = props.activeNode;
      const out = toEntryReducer(
        {
          ...data,
          channelCode,
        },
        initData,
      );
      const { catalogId, destinationId } = out;
      const { variantExtraData } = data;
      const destinationInput = toDesinationInputAPI(
        data.dynamicFields,
        data.destinationInput,
        channelCode,
      );

      setTestState(draft => {
        // console.log(data.destinationInput);
        draft.isOpenModal = true;
        draft.data.itemTypeId = `${props.itemTypeId}`;
        draft.data.catalogId = catalogId;
        draft.data.destinationId = destinationId;
        draft.data.destinationInput = destinationInput;
        draft.data.variantExtraData = variantExtraData;
        draft.data.dynamicFields = data.dynamicFields;
        draft.data.objectWidgetInput = data.objectWidgetInput;
        draft.data.testingAudienceIds = data.testingAudienceIds;
      });

      if (isDetailDrawer()) {
        props.updateFullScreen(true);
      }
    } else {
      setTestState(draft => {
        draft.isOpenModal = false;
      });

      props.updateIsOpenSubDrawer(false);
    }
  }, [props.isOpenModalDestinationTesting]);

  /** Effect show error by story error */
  useDeepCompareEffect(() => {
    if (
      data?.isInitDone &&
      errors &&
      activeNode?.nodeId &&
      errors?.[activeNode?.nodeId]
    ) {
      setData(draft => {
        [
          ...data.defaultFields,
          ...data.webChannelFields,
          ...data.variantHardFields,
        ].forEach(each => {
          const hasError = errors?.[activeNode?.nodeId]?.[each] || [];
          if (hasError?.length) {
            draft[each].errors = hasError;
            draft[each].isValidate = !hasError?.length;
          }
          return !hasError?.length;
        });
        [...data.dynamicFields].forEach(each => {
          const hasError = errors?.[activeNode?.nodeId]?.[each] || [];
          if (hasError?.length) {
            draft.destinationInput[each].errors = hasError;
            draft.destinationInput[each].isValidate = !hasError?.length;
          }
          return !hasError?.length;
        });

        const isValidate1 = [
          ...data.defaultFields,
          ...data.variantHardFields,
        ].every(each => draft[each].isValidate);
        const isValidate2 = [...draft.dynamicFields].every(
          each => draft.destinationInput[each].isValidate,
        );
        draft.isValidate = isValidate1 && isValidate2;
      });
    }
  }, [errors, activeNode, data?.isInitDone]);

  //
  // console.log('data', data);

  const toggleWebIframe = value => {
    setTestState(draft => {
      draft.isOpenModal = Boolean(value) === value ? value : !draft.isOpenModal;
    });

    // props.toggleDestinationModalTesting(value);

    // if (isDetailDrawer()) {
    //   props.updateFullScreen(Boolean(value));
    // } else {
    //   props.updateIsOpenSubDrawer(Boolean(value));
    // }
  };

  // console.log({
  //   zoneId,
  //   zoneRenderType,
  //   isChannelWebPersonalization,
  //   data,
  // });

  const handleChangeExtraDataTagProperty = useCallback(
    (newTagProperty = {}) => {
      const { tagId, property, extraAttribute } = newTagProperty;

      if (!tagId || !property) return;

      try {
        clearTimeout(timerUpdateExtraTagPropertyRef.current);
        // Delayed state update to prevent React's "setState in render" error.
        // `setTimeout` ensures the update happens after the current render cycle.
        // described in https://fb.me/setstate-in-render
        timerUpdateExtraTagPropertyRef.current = setTimeout(() => {
          setData(draft => {
            const { activeId } = draft.variants;

            updateExtraDataTagProperty({
              draft,
              tagId,
              property,
              activeId,
              executeType: 'set',
            });
            updateExtraAttribute({
              draft,
              extraAttribute,
              activeId,
            });

            props.onToggleButtonSaveChange(false);
          });
        }, 200);
      } catch (error) {
        addMessageToQueue({
          path: PATH,
          func: 'handleChangeExtraDataTagProperty',
          data: {
            error: error.stack,
            args: { newTagProperty },
          },
        });
      }
    },
    [],
  );

  const handleTagRemove = useCallback(
    (removeTag, extraAttribute) => {
      if (!removeTag?.tagId) return;

      try {
        setData(draft => {
          const { activeId } = draft.variants;

          updateExtraDataTagProperty({
            draft,
            tagId: removeTag.tagId,
            property: null,
            activeId,
            executeType: 'unset',
          });

          updateExtraAttribute({
            draft,
            activeId,
            extraAttribute,
          });
        });

        props.onRemoveTagProperty({
          nodeId: activeNode?.nodeId,
          tagId: removeTag.tagId,
        });
      } catch (error) {
        addMessageToQueue({
          path: PATH,
          func: 'handleTagRemove',
          data: {
            error: error.stack,
            args: { removeTag },
          },
        });
      }
    },
    [activeNode?.nodeId, props.onRemoveTagProperty],
  );

  const callback = (type, dataIn) => {
    switch (type) {
      case 'ON_CHANGE_AUDIENCEIDS': {
        setData(draft => {
          draft.isCallback = true;
          draft.testingAudienceIds = dataIn;
        });
        break;
      }
      case 'CONFIRM_CHANGE_ALGORITHMS': {
        setData(draft => {
          draft.isCallback = true;
          draft.campaignSetting = dataIn;
        });
        break;
      }
      case 'UPDATE_OPTIONS_GROUP_DESTINATION': {
        const { name = '', oldOptions = {}, data: _initData = {} } = dataIn;
        if (!name || _.isEmpty(oldOptions) || _.isEmpty(_initData)) return;

        const itemDestinationInput = getObjectPropSafely(
          () => data.destinationInput[name],
          {},
        );
        const result = [];
        const temp = Object.keys(_initData || {});

        temp.forEach(value => {
          if (typeof key === 'string') {
            const num = parseInt(value.replace(/[^0-9]/g, ''));
            result.push({
              ...oldOptions,
              label: `Button ${num}`,
              value,
            });
          }
        });
        setData(draft => {
          draft.destinationInput = {
            ...data.destinationInput,
            [name]: {
              ...itemDestinationInput,
              options: result,
            },
          };
        });
        break;
      }
      case 'UPDATE_VALUE_TESTING': {
        props.callback('UPDATE_VALUE_TESTING', dataIn);
        break;
      }
      case 'SET_DEFAULT_URL_SMART_INBOX': {
        const { name = '', status = false } = dataIn || {};
        handleSetDefaultURLSettingSmartInbox(name, status, data, setData);
        break;
      }
      case 'SET_ACTIVE_FIRST_LAYOUT_TAB': {
        // For case UI has layout Tabs
        let groupConfigsFiltered = _cloneDeep(groupConfigsMapping);

        if (isOnlyInbox && !_isEmpty(groupConfigsMapping)) {
          groupConfigsFiltered = groupConfigsMapping.filter(
            groupItem => groupItem.key === 'inbox',
          );
        }
        // Active first tab
        setActiveTabDisplay(
          groupConfigsFiltered[0] && groupConfigsFiltered[0].key,
        );
        break;
      }
      case 'UPDATE_DYNAMIC_FIELDS': {
        // NOTE: Only for special case of Antsomi Line
        if (isAntsomiLine) {
          const { validateConfigs = {} } = dataIn;
          setData(draft => {
            draft.dynamicFields = [SNAPSHOT_RENDER_FIELD];

            if (has(draft.destinationInput, SNAPSHOT_RENDER_FIELD)) {
              draft.destinationInput[
                SNAPSHOT_RENDER_FIELD
              ].validateConfigs = validateConfigs;
            }
          });
        }
        break;
      }
      case 'UPDATE_CONFIG_RATING': {
        let { data: ratingIconType = {} } = dataIn;
        let newConfig = {
          placeholder: '',
          isRequired: true,
          canImportFromMediaLibrary: false,
        };
        const label = {
          selected: getTranslateMessage(
            TRANSLATE_KEY._,
            'Selected Rating Icon',
          ),
          selectedColor: getTranslateMessage(TRANSLATE_KEY._, 'Selected Color'),
          unselected: getTranslateMessage(
            TRANSLATE_KEY._,
            'Unselected Rating Icon',
          ),
          unselectedColor: getTranslateMessage(
            TRANSLATE_KEY._,
            'Unselected Color',
          ),
        };
        if (_.isObject(ratingIconType)) ratingIconType = ratingIconType.value;
        const isTextEmoji = ratingIconType === ICON_TYPE_KEY.TEXT_EMOJI;
        const isSystem = ratingIconType === ICON_TYPE_KEY.SYSTEM;
        const isUpload = ratingIconType === ICON_TYPE_KEY.UPLOAD;

        if (isUpload) {
          newConfig = {
            ...newConfig,
            inputType: 'text',
            canImportFromMediaLibrary: true,
            validate: MAP_VALIDATE.imageUrl,
            componentEl: MAP_INPUT_TYPE.imageUrl,
            placeholder: 'Upload or input icon',
          };
        }
        if (isSystem) {
          newConfig = {
            ...newConfig,
            inputType: 'selectIcon',
            validate: MAP_VALIDATE.selectIcon,
            componentEl: MAP_INPUT_TYPE.selectIcon,
          };
        }
        if (isTextEmoji) {
          newConfig = {
            ...newConfig,
            inputType: 'color',
            isRequired: false,
            validate: MAP_VALIDATE.validated,
            componentEl: MAP_INPUT_TYPE.color,
          };
        }

        const { selected = '', unSelected = '' } =
          DEFAULT_ICON_SELECT_VARIANTS[ratingIconType] || {};

        setData(draft => {
          if (
            _.has(draft.destinationInput, 'ratingSelected') &&
            _.has(draft.destinationInput, 'ratingUnselected')
          ) {
            const { ratingSelected, ratingUnselected } = draft.destinationInput;

            draft.destinationInput.ratingSelected = {
              ...ratingSelected,
              ...newConfig,
              label: isTextEmoji ? label.selectedColor : label.selected,
              options: isSystem ? ICON_OPTION.SELECTED : [],
              initValue: selected,
              value: selected,
            };
            draft.destinationInput.ratingUnselected = {
              ...ratingUnselected,
              ...newConfig,
              options: isSystem ? ICON_OPTION.UNSELECTED : [],
              label: isTextEmoji ? label.unselectedColor : label.unselected,
              iniValue: unSelected,
              value: unSelected,
              isHidden: isTextEmoji,
            };

            ['ratingSelected', 'ratingUnselected'].forEach(key => {
              const pathUpdateCache = `variants.cacheInfo.${
                draft.variants.activeId
              }.destinationInput.${key}`;
              const value = key === 'ratingSelected' ? selected : unSelected;

              if (_.has(draft, pathUpdateCache)) {
                _.set(draft, pathUpdateCache, value);
              }
            });
          }

          GEN_RATING_TEXT.forEach(ratingKey => {
            const ratingNumber = Number(ratingKey.replace(/[^0-9]/g, ''));
            let ratingScale = getObjectPropSafely(
              () =>
                data.destinationInput[RATING_SETTING_KEYS.SCALE] &&
                data.destinationInput[RATING_SETTING_KEYS.SCALE].value,
              '',
            );
            if (_.isObject(ratingScale)) ratingScale = ratingScale.value;

            if (_.has(draft.destinationInput, ratingKey)) {
              const isRequired = isTextEmoji && ratingNumber <= ratingScale;
              const initRatingText = _.get(
                initRatingTextValue,
                ratingNumber,
                '',
              );
              const initValue = isRequired ? initRatingText : '';

              draft.destinationInput[ratingKey].isRequired = isRequired;
              draft.destinationInput[ratingKey].initValue = initValue;
              draft.destinationInput[ratingKey].value = initValue;

              const pathUpdateCache = `variants.cacheInfo.${
                data.variants.activeId
              }.destinationInput.${ratingKey}`;
              if (_.has(draft, pathUpdateCache)) {
                _.set(draft, pathUpdateCache, initValue);
              }
            }
          });
        });
        break;
      }
      case 'UPDATE_ANTSOMI_APP_PUSH_EFFECT_EXTRA': {
        // eslint-disable-next-line prefer-const
        let { name = '', data: dataInner = {} } = dataIn;
        if (_.isObject(dataInner)) dataInner = dataInner.value;

        if (name === 'template') {
          const isRating = dataInner === TEMPLATE_KEYS.RATING;

          resetDataVariantAntsomiAppPush({
            data,
            setData,
            template: dataInner,
            callback,
          });
          setRefreshComponentKey(prev => prev + 1);

          if (_.has(data.destinationInput, 'advButton')) {
            const allowLength = isRating ? 1 : 2;
            setData(draft => {
              draft.destinationInput.advButton = {
                ...draft.destinationInput.advButton,
                currentTemplateInput: dataInner,
                minOptions: 1,
                maxOptions: allowLength,
                isRequired: isRating,
              };
            });
          }
        } else if (name === RATING_SETTING_KEYS.SCALE) {
          GEN_RATING_TEXT.forEach(ratingKey => {
            const ratingNumber = Number(ratingKey.replace(/[^0-9]/g, ''));
            if (
              _.has(data.destinationInput, ratingKey) &&
              ratingNumber > dataInner
            ) {
              onChangeDataDesInput(ratingKey)('');
            }
            const pathUpdateCache = `variants.cacheInfo.${
              data.variants.activeId
            }.destinationInput.${ratingKey}`;

            setData(draft => {
              if (_.has(draft.destinationInput, ratingKey)) {
                draft.destinationInput[ratingKey].isRequired =
                  dataInner >= ratingNumber;

                if (_.has(draft, pathUpdateCache)) {
                  _.set(draft, pathUpdateCache, '');
                }
              }
            });
          });
        }
        break;
      }
      case 'SET_INPUT_CONFIGS_ANTSOMI_APP_PUSH': {
        const { template = '' } = dataIn;
        const isRating = template === TEMPLATE_KEYS.RATING;

        setData(draft => {
          if (_.has(draft.destinationInput, 'advButton.maxOptions')) {
            draft.destinationInput.advButton.maxOptions = isRating ? 1 : 2;

            if (
              _.has(
                draft.destinationInput,
                'advButton.options[0].inputs.advBtnLabel',
              )
            ) {
              draft.destinationInput.advButton.options[0].inputs.advBtnLabel.isRequired = isRating;
            }
          }
        });
        break;
      }
      case 'CHECKED_LIMIT_IMAGE_SIZE': {
        const { name = '', isValid = true, errors = [] } = dataIn;

        setData(draft => {
          if (_.has(draft.destinationInput, name)) {
            draft.destinationInput[name].isValidate = isValid;
            draft.destinationInput[name].errors = errors;
          }
        });
        break;
      }
      case 'UPDATE_ZALO_OA_EFFECT_EXTRA': {
        if (dataIn?.data?.value === TEMPLATE_ZALO_OA_TYPES.TRANSACTION) {
          setData(draft => {
            const originalState = original(draft);

            if (
              originalState?.destinationInput?.transactionType &&
              !originalState?.destinationInput?.transactionType?.value
            ) {
              const initTrans = get(
                originalState.destinationInput,
                ['transactionType', 'default'],
                {},
              );

              draft.destinationInput.transactionType.initValue = initTrans;
              draft.destinationInput.transactionType.value = initTrans;
            }
          });
        }
        break;
      }
      case 'UPDATE_EXTRA_LINE_MESSAGE': {
        const { nodeTagProperties, tagProperties = {} } = dataIn;

        setData(draft => {
          draft.isCallback = false;
          const path = [
            'cacheInfo',
            draft.variants.activeId,
            'variantExtraData',
            'properties',
            'tags',
          ];

          const mergedTagProperties = Object.assign(
            {},
            draft.variantExtraData?.properties?.tags,
            tagProperties,
          );

          _.set(
            draft.variantExtraData,
            ['properties', 'tags'],
            mergedTagProperties,
          );
          _.set(draft.variants, path, mergedTagProperties);
        });

        props.onMergeListTagProperties({
          nodeId: activeNode.nodeId,
          tags: nodeTagProperties,
        });
        break;
      }
      default: {
        props.callback(type, dataIn);
        break;
      }
    }
  };

  // async function asyncCall({ setCacheNodeInfo, setCampaignSettingVariant }) {
  //   await setCacheNodeInfo();
  //   await setCampaignSettingVariant;
  // }

  const callbackVariant = (type, dataIn) => {
    switch (type) {
      case 'ON_ADD': {
        const isValidateVariantContent = validateVariantContent(data, setData, {
          contentAccentType,
        });
        // console.log('isValidateVariantContent', isValidateVariantContent);

        if (isValidateVariantContent) {
          const genId = createFeVarinatId();
          let initialNumberOrder = 1;
          let label = `Variant ${initialNumberOrder}`;
          const variantList = getObjectPropSafely(() => data.variants.list, []);

          if (Array.isArray(variantList)) {
            let indexVariant = variantList.findIndex(
              variant => variant.label === label,
            );

            while (indexVariant !== -1) {
              initialNumberOrder += 1;
              label = `Variant ${initialNumberOrder}`;

              indexVariant = variantList.findIndex(
                // eslint-disable-next-line no-loop-func
                variant => variant.label === label,
              );
            }
          }

          const dataAdd = [
            ...data.variants.list,
            {
              value: genId,
              label,
            },
          ];

          const objectWidgetInput = {};
          const variantExtraData = initVariantExtraData(
            data.inputFields,
            data.variantExtraData,
          );

          // Đổ  lại giá trị cho algorithms variant
          reBuildValueCampaignSetting({
            dataVariantList: dataAdd,
            setData,
            dataInit: data,
            type: 'RE_BUILD_VALUE',
          });

          // set variant cache info
          setVariantCacheInfo(data, setData, {
            activeId: genId,
            variantExtraData,
            objectWidgetInput,
            templateWorkspaces: data.templateWorkspaces,
          });

          setData(draft => {
            draft.isCallback = true;
            draft.objectWidgetInput = objectWidgetInput;
            draft.variantExtraData = variantExtraData;
            draft.variants.list = dataAdd;
            draft.variants.activeId = genId;
            draft.variantName.activeId = genId;
            draft.variantName.value = label;
            draft.variantName.initValue = label;

            Object.keys(data.destinationInput).forEach(key => {
              const {
                default: defaultValue = '',
                inputType = '',
                mapOptions = {},
              } = getObjectPropSafely(() => data.destinationInput[key]);

              if (
                inputType === 'select' &&
                typeof defaultValue === 'string' &&
                typeof mapOptions === 'object'
              ) {
                draft.destinationInput[key].value =
                  mapOptions[defaultValue] || '';
                draft.destinationInput[key].initValue =
                  mapOptions[defaultValue] || '';
              } else {
                draft.destinationInput[key].value = defaultValue || '';
                draft.destinationInput[key].initValue = defaultValue || '';
              }
            });
          });
        }

        break;
      }
      case 'ON_DELETE': {
        // ko dc phep xoa het
        if (data.variants.list.length < 2) return;
        const dataTmp = data.variants.list.filter(
          item => item.value !== dataIn.value,
        );
        const tagIds = collectTagIdsFromVariantExtraData(
          get(
            data.variants,
            ['cacheInfo', dataIn.value, 'variantExtraData'],
            {},
          ),
        );

        // neu item dc xoa khac trung vs active item thi reset value
        if (dataIn.value == data.variants.activeId) {
          const newActiveItem = dataTmp[0] || {};
          const initDataVariant = data.variants.cacheInfo[
            newActiveItem.value
          ] || {
            objectWidgetInput: {},
            destinationInput: {},
            variantExtraData: {},
            status: true,
          };

          buildValueFormVariantFromCache(
            setData,
            initDataVariant,
            data.destinationInput,
            newActiveItem,
          );
        }

        // Đổ  lại giá trị cho algorithms variant
        reBuildValueCampaignSetting({
          dataVariantList: dataTmp,
          setData,
          dataInit: data,
          type: 'RE_BUILD_VALUE',
        });

        // Delete => remove key in mapVariantName
        const mapVariantNameTmp = {};

        for (const [key, value] of Object.entries(
          data.variantName.mapVariantName,
        )) {
          if (key != dataIn.value) {
            mapVariantNameTmp[key] = value;
          }
        }

        // set data
        setData(draft => {
          draft.isCallback = true;
          draft.variants.list = dataTmp;
          draft.variantName.mapVariantName = mapVariantNameTmp;
          delete draft.variants.cacheInfo[dataIn.value];
          // draft.variants.activeId = dataTmp[0].value;
        });

        // Also remove list tags in properties at root workflow
        if (tagIds.length) {
          props.onRemoveTagByIds({
            removedTagIds: tagIds,
            nodeId: activeNode.nodeId,
          });
        }
        break;
      }
      case 'ON_ACTIVE': {
        // console.log(
        //   'destinationInput',
        //   toDesinationInputAPI(data.dynamicFields, data.destinationInput),
        // );

        const isValidateVariantContent = validateVariantContent(data, setData, {
          contentAccentType,
        });
        // console.log('isValidateVariantContent', isValidateVariantContent);

        if (isValidateVariantContent) {
          const initDataVariant = data.variants.cacheInfo[dataIn.value] || {
            objectWidgetInput: {},
            destinationInput: {},
            variantExtraData: {},
            status: true,
          };

          const initDataTmp = _.cloneDeep(
            data.variants.cacheInfo[dataIn.value] || {
              objectWidgetInput: {},
              destinationInput: {},
              variantExtraData: {},
              status: true,
            },
          );
          // set variant cache info
          // setVariantCacheInfo(data, setData, {
          //   activeId: dataIn.value,
          //   variantExtraData: initDataVariant.variantExtraData,
          //   objectWidgetInput: initDataVariant.objectWidgetInput,
          // });

          buildValueFormVariantFromCache(
            setData,
            initDataTmp,
            data.destinationInput,
            dataIn,
          );

          rebuildSettingDestinationInputs({
            data,
            setData,
            dataIn,
          });
        }

        break;
      }
      // case 'ON_CHANGE_NAME': {
      //   setData(draft => {
      //     const findIndex = data.variants.list.findIndex(
      //       item => item.value === data.variants.activeId,
      //     );
      //     if (data.variants.list[findIndex]) {
      //       data.variants.list[findIndex].label = dataIn;
      //     }
      //     draft.variants.activeInfo.name = dataIn;
      //   });
      //   break;
      // }

      default: {
        props.callback(type, dataIn);
        break;
      }
    }
  };
  const onChangeData = useCallback(
    name => value => {
      setData(draft => {
        draft.isCallback = true;
        draft[name].value = value;
        draft[name].errors = [];

        if (isBlastCampaign && name === 'filter') {
          draft[name].initValue = value;
        }
      });

      if (isBlastCampaign) {
        props.onChange(name, value);
      }
      if (name === 'workflowDestination') {
        const { INFOBIP_WHATSAPP_TEMPLATE } = CATALOG_CODE;
        const isIFBWSapp = data.catalogCode === INFOBIP_WHATSAPP_TEMPLATE;
        const currentDestId = get(
          data,
          'workflowDestination.value.destinationId',
        );

        if (isIFBWSapp && currentDestId !== value?.destinationId) {
          setData(draft => {
            draft.isChangedWorkflowDestWhatsappTemplate = isIFBWSapp;
          });
        }

        if (isBlastCampaign) {
          // const isChangedCatalog = getObjectPropSafely(
          //   () =>
          //     +data.workflowDestination.value.catalogId !== +value.catalogId,
          // );

          // if (isChangedCatalog) {
          setData(draft => {
            draft.triggerFetchCatalogInfo += 1;
            draft.catalogCodeBeforeFetchInfo = data.catalogCode;
            draft.previousCatalogId = data.catalogId;
            draft.previousMethod = _.get(
              data,
              'workflowDestination.value.method',
              'send',
            );
            draft.catalogId = value.catalogId;
            draft.catalogCode = value.catalogCode;
            draft.destinationId = value.destinationId;
            draft.isCallback = true;
          });
          const newActiveNode = {
            ...activeNode,
            value: value.catalogId,
            catalogCode: value.catalogCode,
            catalogId: value.catalogId,
          };
          props.onChangeActiveNode(newActiveNode);
          // }
        } else if (
          value.method !== (data.workflowDestination.value || {}).method
        ) {
          setData(draft => {
            draft.triggerFetchContent += 1;
          });
        }
      }

      if (name === 'variantName') {
        const findIndex = data.variants.list.findIndex(
          item => item.value === data.variants.activeId,
        );
        if (data.variants.list[findIndex]) {
          setData(draft => {
            draft.variants.list[findIndex].label = value;
          });
        }
        if (
          props.isBlastCampaign &&
          data.variantName.mapVariantName.activeId !== data.variants.activeId
        ) {
          setData(draft => {
            draft.variantName.activeId = data.variants.activeId;
            delete draft.variantName.mapVariantName.activeId;
          });
        }
        setData(draft => {
          draft.variantName.mapVariantName[data.variants.activeId] = value;
        });
      }
      if (name !== 'campaignName' && name !== 'workflowDestination') {
        // set cache info data for case no use click button save => call API with newest data
        const destinationInput = toDesinationInputAPI(
          data.dynamicFields,
          data.destinationInput,
          activeNode.channelCode,
        );
        // cache lại data hiện tại
        // console.log('onChangeData', 'onChangeData');
        setData(draft => {
          if (name === 'zoneId') {
            const zoneSetting = safeParse(value.zoneSetting, {});
            if (Object.keys(zoneSetting).length > 0) {
              draft.contentPlacement.value =
                DATA_CONTENT_PLACEMENT.map[zoneSetting.contentPlacement];
            }
          }
          draft.variants.cacheInfo[data.variants.activeId] = {
            ...data.variants.cacheInfo[data.variants.activeId],
            destinationInput,
            objectWidgetInput: data.objectWidgetInput || {},
            variantExtraData: data.variantExtraData || {},
            // status: draft.variants.cacheInfo[data.variants.activeId].status,
          };
        });
      }
    },

    [componentId, data],
  );
  const onChangeDataCustomInputVia = ({ value, type, workspaces }) => {
    const { infor, isValidate } = value;
    const { rowId, cellId } = infor;
    const workspacesTmp = _.cloneDeep(workspaces);

    // let dataCustomInputVia = _.cloneDeep(
    //   data.variants.cacheInfo[data.variants.activeId].customInputVia.workspaces,
    // );
    const indexRow = workspacesTmp.findIndex(each => each.id === rowId);
    const indexCell = workspacesTmp[indexRow].sections.findIndex(
      each => each.id === cellId,
    );

    workspacesTmp[indexRow].sections[indexCell].properties.value = value.value;
    workspacesTmp[indexRow].sections[
      indexCell
    ].properties.isValidate = isValidate;
    workspacesTmp[indexRow].sections[indexCell].errors = [];

    const updateValueCustomInputAttributes = get(
      data,
      type === 'campaign'
        ? 'campaignCustomInput.customInputAttributes'
        : 'customInputAttributes',
      [],
    ).map(attr => {
      const res = {
        ...attr,
      };
      workspacesTmp.forEach(row => {
        row.sections.forEach(cell => {
          if (cell.id === attr.itemPropertyName) {
            res.propertiesValue = cell.properties.value;
          }
        });
      });
      return res;
    });

    const newCustomInputAttributes = filterCustomInputAttributes(
      updateValueCustomInputAttributes,
    );
    const mapNewCustomInputAttributes = keyBy(
      newCustomInputAttributes,
      'itemPropertyName',
    );

    workspacesTmp.forEach((row, rowIndex) => {
      row.sections.forEach((cell, cellIndex) => {
        if (cell.inputViaUiValue.isAssociateDropdown) {
          const { value: options, isMultiSelect } = mapNewCustomInputAttributes[
            cell.id
          ].inputViaUiValue;
          workspacesTmp[rowIndex].sections[cellIndex].option = options || [];
          // reset value
          const isExist = options.find(
            option =>
              !option.isHidden &&
              option.value ===
                workspacesTmp[rowIndex].sections[cellIndex].properties.value,
          );
          if (!isExist && !isMultiSelect) {
            workspacesTmp[rowIndex].sections[
              cellIndex
            ].properties.value = undefined;
          }

          // const isReset =
          //   newCustomInputAttributes.findIndex(
          //     item =>
          //       item.itemPropertyName === cellId &&
          //       item.inputViaUiValue.isPreSelectDropdown &&
          //       item.associateWiths.includes(cell.id),
          //   ) > -1;

          // if (isReset && cell.id !== cellId) {
          //   // reset all
          //   workspacesTmp[rowIndex].sections[
          //     cellIndex
          //   ].properties.value = undefined;
          // }
        }
      });
    });

    if (type === 'variant') {
      setData(draft => {
        draft.variants.cacheInfo[
          data.variants.activeId
        ].customInputVia.workspaces = workspacesTmp;
        draft.isCallback = true;
      });
    }

    if (type === 'campaign') {
      setData(draft => {
        draft.campaignCustomInput.workspaces = workspacesTmp;
        draft.isCallback = true;
      });
    }
  };

  const handleChangeDataDesInput = useCallback(
    (name, value, isWithInitValue = false, otherParams = {}) => {
      // on change nhieu lần bị rớt data variantExtraData + objectWidgetInput
      // chỗ nào set lại param thì truyền từ bên ngoài vào để không bị mất data
      const { variantExtraData = null, objectWidgetInput = null } = otherParams;
      // console.log('handleChangeDataDesInput', {
      //   variantExtraData,
      //   objectWidgetInput,
      // });

      const relatedChangePath = ['destinationInput', name, 'relatedOnChange'];
      const relatedConfigs = _.get(data, relatedChangePath, {});
      const hasChangeRelated = !_.isEmpty(relatedConfigs);
      let destInput = _.get(data, 'destinationInput', {});
      let dynamicFields = _.get(data, 'dynamicFields', []);

      if (hasChangeRelated) {
        const {
          destinationInput,
          dynamicFields: fields,
        } = getNewDestinationInputConfigs({
          name,
          newValue: value,
          relatedConfigs,
          allData: data,
        });

        destInput = destinationInput;
        dynamicFields = fields;
      }

      const destinationInput = toDesinationInputAPI(
        dynamicFields,
        destInput,
        activeNode.channelCode,
      );
      const textValue = toFormValue(
        destInput[name] || {},
        value,
        destInput,
        activeNode.channelCode,
      );
      destinationInput[name] = textValue;
      setData(draft => {
        draft.isCallback = true;
        // get value API, for some case value is object, not a text
        if (hasChangeRelated) {
          draft.destinationInput = destInput;
        } else if (has(draft.destinationInput, name)) {
          draft.destinationInput[name].value = value;
        }
        // const isUpdateRealtime = ['content', 'text', 'imageUrl'].includes(name);

        // if (
        //   (['message', 'content'].includes(name) &&
        //     ['sms', 'telegram', 'conversation'].includes(
        //       props.activeNode.channelCode,
        //     )) ||
        //   (isUpdateRealtime && isBlastCampaign)
        // ) {
        //   const newPreviewData = {};
        //   newPreviewData[draft.destinationInput[name].previewFieldCode] = value;
        //   setPreviewData(newPreviewData);
        // }
        if (isWithInitValue && !hasChangeRelated) {
          if (has(draft.destinationInput, name)) {
            draft.destinationInput[name].initValue = value;
          }
        }

        if (objectWidgetInput !== null) {
          draft.objectWidgetInput = objectWidgetInput;
        }
        if (variantExtraData !== null) {
          draft.variantExtraData = variantExtraData;
        }

        if (has(draft.destinationInput, name)) {
          draft.destinationInput[name].errors = [];
        }
        draft.variants.cacheInfo[data.variants.activeId] = {
          ...data.variants.cacheInfo[data.variants.activeId],
          destinationInput,
          objectWidgetInput:
            objectWidgetInput !== null
              ? objectWidgetInput
              : data.objectWidgetInput || {},
          variantExtraData:
            variantExtraData !== null
              ? variantExtraData
              : draft.variantExtraData || {},
          customInputVia:
            // draft.variants.cacheInfo[data.variants.activeId].customInputVia,
            get(
              draft,
              `variants.cacheInfo[${data.variants.activeId}].customInputVia`,
              {},
            ),
          // status: draft.variants.cacheInfo[data.variants.activeId].status,
        };
      });
    },
    [componentId, data],
  );

  const onChangeDataDesInput = useCallback(
    name => value => {
      handleChangeDataDesInput(name, value);
    },
    [componentId, data],
  );

  const onChangeOthersData = useCallback(
    // callback current only for objectWidgetInput
    // for more use case when callback
    name => (code, value, otherParams = {}) => {
      const {
        isWithInitValue = false,
        isReuse = false,
        isUseTemplate = false,
        isBlank = false,
        resetCreatingJourneyInfo,
        templateName = '',
      } = otherParams;
      let objectWidgetInput = null;
      let variantExtraData = null;
      let useDesign = null;
      if (code === '__MULTI__') {
        const multi = safeParse(value, {});
        Object.keys(multi).forEach(key => {
          const dataIn = multi[key];
          if (key === 'objectWidgetInput') {
            // cho param objectWidgetInput
            objectWidgetInput = dataIn;
          } else if (key === 'variantExtraData') {
            // cho param objectWidgetInput
            variantExtraData = data.variantExtraData || {};
            variantExtraData[name] = dataIn;
            useDesign = dataIn.design;
          }
        });

        if (multi.__ROOT__) {
          handleChangeDataDesInput(name, multi.__ROOT__ || '', true, {
            variantExtraData,
            objectWidgetInput,
          });
        } else {
          // safe parse data
          variantExtraData = variantExtraData || data.variantExtraData || {};
          objectWidgetInput = objectWidgetInput || data.objectWidgetInput || {};
          setData(draft => {
            draft.isCallback = true;
            draft.objectWidgetInput = objectWidgetInput;
            draft.variantExtraData = variantExtraData;
            draft.variants.cacheInfo[data.variants.activeId] = {
              ...draft.variants.cacheInfo[data.variants.activeId],
              destinationInput: data.destinationInput || {},
              objectWidgetInput,
              variantExtraData,
            };
          });
        }

        if (useDesign !== null) {
          setData(draft => {
            draft.useDesign = useDesign;
          });
        }
      }
      if (code === 'design' || code === 'emailConfig') {
        // else for email template
        // const { variantExtraData = {} } = data;
        // variantExtraData bị refer nên data bị mất thống nhất. cần gán lại biết mới
        variantExtraData = {};
        if (data.variantExtraData) {
          variantExtraData = {
            ...data.variantExtraData,
          };
        }
        if (!variantExtraData[name]) {
          variantExtraData[name] = {
            design: 'template',
            emailConfig: {},
          };
        }
        // debugger;
        if (code === 'design') {
          variantExtraData[name].design = value;
          // Refresh data when change design
          if (!isReuse) {
            variantExtraData[name].emailConfig = {};
          }
        } else if (code === 'emailConfig') {
          // read value.design for get internal config email editor
          variantExtraData[name].emailConfig = value.design || {};
        }
        setData(draft => {
          draft.isCallback = true;
          draft.variantExtraData = variantExtraData;
          draft.variants.cacheInfo[data.variants.activeId] = {
            ...draft.variants.cacheInfo[data.variants.activeId],
            destinationInput: data.destinationInput || {},
            objectWidgetInput: data.objectWidgetInput || {},
            status:
              data.status ||
              data.variants.cacheInfo[data.variants.activeId].status,
            variantExtraData,
          };
        });
        if (code === 'emailConfig') {
          // read value.design for get internal config email editor
          // case output null data => safeParse empty
          handleChangeDataDesInput(name, value.content || '', true, {
            variantExtraData,
          });
        }
      }
      if ([MEDIA_TEMPLATE, JSON_TEMPLATE, EMAIL_TEMPLATE].includes(code)) {
        if (data.variantExtraData) {
          variantExtraData = {
            ...data.variantExtraData,
          };
        }
        const variantExtraDataMT = {
          ...variantExtraData,
          [name]: {
            design: code,
            type: code,
            template_settings: value.template_setting,
            properties: value.properties,
            fe_config_id: value.fe_config_id || null,
          },
        };
        // map customFunction template to CPD
        if (props.activeNode.channelCode === 'web_personalization') {
          const customFuntionTemplate = mapCustomFunctionTemplate(value);
          if (
            customFuntionTemplate &&
            Object.keys(customFuntionTemplate).length > 0
          ) {
            variantExtraDataMT.customFunction = customFuntionTemplate;
          }
        }
        if ([MEDIA_TEMPLATE, EMAIL_TEMPLATE].includes(code)) {
          const views = getViewPagesDefault(value);
          variantExtraDataMT[name].views = views;
        }
        handleChangeDataDesInput(name, value, false, {
          variantExtraData: variantExtraDataMT,
        });
        // Update Name Variant to Name Template
        if (isUseTemplate) {
          const findIndex = data.variants.list.findIndex(
            item => item.value === data.variants.activeId,
          );
          const nameVariant = `${templateName} ${findIndex + 1}`;
          setData(draft => {
            draft.isCallback = true;
            draft.variantName.value = nameVariant;
            draft.variantName.errors = [];
          });

          if (data.variants.list[findIndex]) {
            _.set(data.variants.list[findIndex], 'label', nameVariant);
          }
          setData(draft => {
            draft.variantName.mapVariantName[
              data.variants.activeId
            ] = nameVariant;
          });
        }
        if (isBlank) {
          let initialNumberOrder = 1;
          let label = `Variant ${initialNumberOrder}`;
          const variantList = getObjectPropSafely(() => data.variants.list, []);
          const findIndex = data.variants.list.findIndex(
            item => item.value === data.variants.activeId,
          );
          if (Array.isArray(variantList)) {
            let indexVariant = variantList.findIndex(
              variant => variant.label === label,
            );

            while (indexVariant !== -1) {
              initialNumberOrder += 1;
              label = `Variant ${initialNumberOrder}`;

              indexVariant = variantList.findIndex(
                // eslint-disable-next-line no-loop-func
                variant => variant.label === label,
              );
            }
          }
          setData(draft => {
            draft.isCallback = true;
            draft.variantName.value = label;
            draft.variantName.errors = [];
          });
          if (data.variants.list[findIndex]) {
            data.variants.list[findIndex].label = label;
          }
          setData(draft => {
            draft.variantName.mapVariantName[data.variants.activeId] = label;
          });
        }
        if (resetCreatingJourneyInfo) {
          props.onChangeCreatingJourneyInfo({
            templateType: '',
            channelCode: null,
            data: {},
          });
        }
      }

      if (isWithInitValue) {
        // Refresh data when change design
        handleChangeDataDesInput(name, '', true, {
          objectWidgetInput,
          variantExtraData,
        });
      }
    },
    [componentId, data],
  );

  const handleCallbackAudience = data => {
    props.onChange('targetAudience', data);
    setTargetAudienceData(data);
  };

  const clickPreview = () => {
    const newPreviewData = {};
    data.dynamicFields.forEach(each => {
      const item = data.destinationInput[each];
      if (!_isEmpty(item && item.previewFieldCode)) {
        newPreviewData[item.previewFieldCode] = item.value;
      }
    });
    setPreviewData(newPreviewData);
  };

  const toggleModalTesting = value => {
    props.toggleDestinationModalTesting(value);

    if (isDetailDrawer()) {
      props.updateFullScreen(value);
    } else {
      props.updateIsOpenSubDrawer(value);
    }
  };

  const onChangeSelectHoursOfDay = value => {
    setData(draft => {
      draft.isCallback = true;
      draft.timeTarget = value;
    });
  };
  const handleStep = step => {
    if (
      isZaloZNS &&
      !isValidateWithCondition({
        step: 0,
        catalogCode: activeNode.catalogCode,
        data: data.destinationInput,
      })
    ) {
      return;
    }
    setActiveStep(step);
  };

  const checkingBtnQuickTestStatus = () => {
    if (data.isInitDone) {
      return validateVariantContent(data, setData, {
        contentAccentType,
      });
    }

    return false;
  };

  const toggleModalAlgorithms = value => {
    setAudiencesState(draft => {
      draft.isOpenModal = value;
    });
  };
  const handleCopyVariant = id => {
    // Check còn chỗ  trống để thêm variant không
    if (data.variants.list.length + MY_SELF_VARIANT <= MAX_VARIANTS) {
      // console.log('data', data);
      const { variants, variantName } = data;
      const { cacheInfo, activeId } = variants;
      const selectedVariant =
        variants.list.find(item => item.value === id) || {};

      const isValidateVariantContent = validateVariantContent(data, setData, {
        contentAccentType,
      });

      if (isValidateVariantContent) {
        const genId = createFeVarinatId();
        const label = `${variantName.mapVariantName[id] ||
          selectedVariant.label} copied`;
        // || variantName.value} copied`;
        const dataAdd = [
          ...data.variants.list,
          {
            value: genId,
            label,
          },
        ];

        // const objectWidgetInput = {};
        // const variantExtraData = initVariantExtraData(data.inputFields);

        // Đổ  lại giá trị cho algorithms variant
        reBuildValueCampaignSetting({
          dataVariantList: dataAdd,
          setData,
          dataInit: data,
          type: 'RE_BUILD_VALUE',
        });

        // Set variant active là variant mới tạo ra
        setVariantCacheInfo(data, setData, {
          activeId: genId,
          // variantExtraData,
          // objectWidgetInput,
        });

        const initDataVariantExtra = _.cloneDeep(
          cacheInfo[id].variantExtraData,
        );
        if (!_.isEmpty(initDataVariantExtra)) {
          // const key = Object.keys(initDataVariantExtra);
          Object.keys(initDataVariantExtra).forEach(key => {
            if (
              key !== 'customFunction' &&
              key !== 'formatAttributes' &&
              key !== 'properties'
            ) {
              initDataVariantExtra[key].copy_id =
                initDataVariantExtra[key].fe_config_id;
              initDataVariantExtra[key].fe_config_id = null;
            }
          });
        }

        const destinationInput = toDesinationInputAPI(
          data.dynamicFields,
          data.destinationInput,
          channelCode,
        );

        const nodeTagProperties = isImmutableMap(properties)
          ? properties.getIn(['nodes', activeNode?.nodeId, 'tags'], {})
          : {};

        const tagProperties = get(
          initDataVariantExtra,
          ['properties', 'tags'],
          {},
        );

        // Generate new custom tag id if needed
        const {
          updatedObject: updatedDestinationInput,
          newTagProperties,
          newNodeTagProperties,
        } = updateTags(destinationInput, tagProperties, nodeTagProperties);

        if (!isEmpty(newNodeTagProperties)) {
          // Sync node tag properties to properties in root
          props.onMergeListTagProperties({
            nodeId: activeNode?.nodeId,
            tags: newNodeTagProperties,
          });
        }
        _.set(initDataVariantExtra, ['properties', 'tags'], newTagProperties);

        setData(draft => {
          draft.isCallback = true;
          draft.objectWidgetInput = cacheInfo[id].objectWidgetInput;
          draft.variantExtraData = initDataVariantExtra;
          // draft.variantExtraData.body.fe_config_id = null;
          draft.variants.list = dataAdd;
          draft.variants.activeId = genId;
          draft.variantName.activeId = genId;
          draft.variantName.value = label;
          draft.variantName.initValue = label;

          // mapVariantName sử dụng để check trùng name
          draft.variantName.mapVariantName[genId] = label;
          draft.variants.mapVariantName[genId] = label;

          draft.variants.cacheInfo[genId] = {
            // destinationInput: cacheInfo[id].destinationInput,
            destinationInput: updatedDestinationInput,
            objectWidgetInput: cacheInfo[id].objectWidgetInput,
            variantExtraData: initDataVariantExtra,
            status: true,
            customInputVia: {
              workspaces: _.cloneDeep(
                data.variants.cacheInfo[id].customInputVia.workspaces,
              ),
            },
          };

          // draft.destinationInput = cacheInfo[activeId].destinationInput;
          // draft.variants.cacheInfo[genId].destinationInput =
          // cacheInfo[activeId].destinationInput;andleCopy

          draft.variants.cacheInfo[id] = {
            ...data.variants.cacheInfo[id],
            destinationInput,
          };

          Object.keys(data.destinationInput).forEach(key => {
            draft.destinationInput[key].value = updatedDestinationInput[key];
            draft.destinationInput[key].initValue =
              updatedDestinationInput[key];

            if (key === 'template' && isZaloZNS) {
              const value = data.destinationInput.template.options.find(
                item => item.value === cacheInfo[id].destinationInput[key],
              );

              if (value) {
                draft.destinationInput[key].value = value;
              }
            }
          });
        });
      }
    }
  };
  const getIsToggle = id =>
    data.isInitDone && data.variants.cacheInfo[id]
      ? data.variants.cacheInfo[id].status
      : true;

  const isSmartInbox = getObjectPropSafely(
    () => props.activeNode.catalogCode === 'smart_inbox_app',
    '',
  );
  const isAntsomiLine = getObjectPropSafely(
    () => props.activeNode.catalogCode === CATALOG_CODE.LINE,
  );
  const isAntsomiAppPush = getObjectPropSafely(
    () => props.activeNode.catalogCode === CATALOG_CODE.ANTSOMI_APP_PUSH,
  );
  const isAeonMallInAppMessage = getObjectPropSafely(
    () =>
      props.activeNode.catalogCode === CATALOG_CODE.AEON_MALL_IN_APP_MESSAGE,
  );
  const isSmsIMedia = getObjectPropSafely(
    () => props.activeNode.catalogCode === CATALOG_CODE.SMS_IMEDIA,
  );
  const isZaloIMedia = getObjectPropSafely(
    () => props.activeNode.catalogCode === CATALOG_CODE.ZALO_IMEDIA,
  );
  const isFirebaseAppPush = getObjectPropSafely(
    () => props.activeNode.catalogCode === CATALOG_CODE.FIRE_BASE_APP_PUSH,
  );
  const isZaloOA = getObjectPropSafely(
    () => props.activeNode.catalogCode === CATALOG_CODE.ZALO_OA,
  );
  const isZaloZNS = getObjectPropSafely(
    () => props.activeNode.catalogCode === CATALOG_CODE.ZNS,
  );
  const isZaloPayZNS = getObjectPropSafely(
    () => props.activeNode.catalogCode === CATALOG_CODE.ZALO_PAY_ZNS,
  );
  const isWebhookGoldenGate = getObjectPropSafely(
    () => props.activeNode.catalogCode === CATALOG_CODE.WEBHOOK_GOLDEN_GATE,
  );

  const isPNJZNSV2 = getObjectPropSafely(
    () => props.activeNode.catalogCode === CATALOG_CODE.PNJ_ZNS_V2,
  );

  const selectedTemplate = getObjectPropSafely(
    () => data.destinationInput.inboxTemplate.value,
    {},
  );
  const isOnlyInbox = getObjectPropSafely(
    () => data.destinationInput.inboxButton.value,
    {},
  );
  const stickerSet = getObjectPropSafely(
    () => data.destinationInput.stickerType.value || {},
  );

  const groupConfigsMapping = useMemo(
    () =>
      getDataMappingGroupConfigs({
        basicInputs: data.dynamicFields,
        inputs: data.destinationInput,
      }),
    [data && data.destinationInput],
  );

  const currentTemplateInput = useMemo(() => {
    let templateResult = null;
    if (!activeNode) return templateResult;
    const {
      channelCode: channelCodeActive = '',
      catalogCode: catalogCodeActive = '',
    } = activeNode;

    templateResult = getCurrentTemplateInputByChannel({
      channelCode: channelCodeActive,
      catalogCode: catalogCodeActive,
      destinationInput: data.destinationInput,
    });

    if (_.isObject(templateResult)) {
      return templateResult.value;
    }

    return templateResult;
  }, [data && data.destinationInput, activeNode]);

  const handleClickToggleStatusVariant = id => {
    // Set lại cacheInfo
    setData(draft => {
      draft.variants.cacheInfo[id] = {
        ...draft.variants.cacheInfo[id],
        status: !draft.variants.cacheInfo[id].status,
      };
      draft.isCallback = true;
    });
  };

  const handleToggleDestModalTesting = () => {
    const isValid = checkingBtnQuickTestStatus();

    if (!isValid || !isEnableTesting) return;

    if (isChannelWebPersonalization) {
      toggleWebIframe();
    } else {
      props.toggleDestinationModalTesting();
    }
  };

  const callbackWhatsappTemplate = useCallback(
    (newOptions, callbackSyncValue) => {
      setData(draft => {
        if (!has(draft, 'destinationInput.template')) return draft;

        const isEmptyTemplate = isEmpty(
          draft.destinationInput?.template?.value,
        );
        draft.isCallback =
          isEmptyTemplate || draft.isChangedWorkflowDestWhatsappTemplate;
        _.set(draft, 'destinationInput.template.options', newOptions);

        // Reset template value
        if (isEmptyTemplate || draft.isChangedWorkflowDestWhatsappTemplate) {
          const newValue = defaultState(newOptions[0] ?? {});
          _.set(draft, 'destinationInput.template.initValue', newValue);
          _.set(draft, 'destinationInput.template.value', newValue);
          _.set(draft, 'destinationInput.template.errors', []);

          // Reset isChangedWorkflowDest
          draft.isChangedWorkflowDestWhatsappTemplate = false;

          if (_.isFunction(callbackSyncValue)) callbackSyncValue(newValue);
        }
      });
    },
    [],
  );

  const checkHideInputSpecific = useCallback(
    ({ name }) => {
      let catalogCode = _.get(props.activeNode, 'catalogCode', '');

      // In case blast campaign
      if (isBlastCampaign && data?.catalogCodeBeforeFetchInfo) {
        // Sử dụng để chặn việc catalog code đã change
        // nhưng destination input vẫn mới chưa fetch done
        // -> if fetch info done -> catalogCodeBeforeFetchInfo === activeNode.catalogCode
        catalogCode = data?.catalogCodeBeforeFetchInfo;
      }

      const { ZALO_OA, LINE, ZNS } = CATALOG_CODE;
      const catalogSpecified = [ZALO_OA, LINE, ZNS].includes(catalogCode);

      const isHiddenInput = checkIsHiddenInputComponent({
        name,
        data: data.destinationInput[name],
        selectedTemplate: getSelectedTemplate(
          catalogCode,
          data.destinationInput,
        ),
        catalogCode,
        extraData: {
          data,
        },
      });

      const isHiddenWithProperty = _.get(
        data,
        `destinationInput.${name}.isHidden`,
        false,
      );

      return (isHiddenInput && catalogSpecified) || isHiddenWithProperty;
    },
    [data, props.activeNode?.catalogCode, isBlastCampaign],
  );

  useEffect(() => {
    if (isZaloZNS) {
      setTemplateSelectedZNS({});
    }
  }, [isZaloZNS, componentId]);

  useEffect(() => {
    const elementsObserver = document.querySelectorAll('#observer_devices_id');

    const observer = new IntersectionObserver(
      (entries, _observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const { typeObserve = '' } = entry.target.dataset;

            if (OBSERVER_VIEW_DEVICES.includes(typeObserve)) {
              setObserveDeviceType(typeObserve);
            }
          }
        });
      },
      {
        threshold: 0.5,
        root: null,
      },
    );

    if (elementsObserver) {
      elementsObserver.forEach(each => {
        observer.observe(each);
      });
    }

    return () => {
      observer.disconnect();
    };
  }, [data && data.destinationInput]);

  // Reset Data Heading & Content after changed template for channel SmartInbox
  useEffect(() => {
    if (isSmartInbox && !_isEmpty(selectedTemplate)) {
      const defaultHeadingValue = getObjectPropSafely(
        () => data.destinationInput.inboxHeading.default,
        '',
      );
      const defaultContentValue = getObjectPropSafely(
        () => data.destinationInput.inboxContent.default,
        '',
      );

      setData(draft => {
        draft.destinationInput.inboxHeading.value = defaultHeadingValue;
        draft.destinationInput.inboxHeading.initValue = defaultHeadingValue;
        draft.destinationInput.inboxContent.value = defaultContentValue;
        draft.destinationInput.inboxContent.initValue = defaultContentValue;
      });
      setRefreshComponentKey(prev => prev + 1);
    }
  }, [isSmartInbox, emitSetDefaultSmartInbox]);

  // re-update required field for case Smart Inbox only
  useEffect(() => {
    try {
      if (isSmartInbox) {
        UPDATE_REQUIRED_FIELDS.forEach(eachField => {
          if (data.destinationInput[eachField]) {
            setData(draft => {
              draft.destinationInput[eachField].isRequired = !isOnlyInbox;
            });
          }
        });
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'useEffect',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }, [isOnlyInbox]);

  // Update templates options in variants for destination smart inbox only
  useEffect(() => {
    if (isSmartInbox) {
      const dataInboxTemplate = getObjectPropSafely(
        () => data.destinationInput.inboxTemplate,
        {},
      );
      const { value = {}, options = [] } = dataInboxTemplate || {};

      if (!_isEmpty(smartInboxConfigs)) {
        const { template = [] } = get(
          smartInboxConfigs,
          ['webviewSettings', 'notificationBox', 'bubble'],
          {},
        );

        if (!_isEmpty(template) && Array.isArray(template)) {
          const mapOptions = {};
          const temp = [];

          template.forEach(each => {
            const snap = {
              label: each.label,
              value: each.id,
            };

            temp.push(snap);
            mapOptions[each.id] = snap;
          });

          // Update lại template options khi change delivery destinations
          // (mỗi delivery destination có thể có list template settings khác nhau)
          if (!isEqual(temp, options)) {
            setData(draft => {
              draft.destinationInput.inboxTemplate.options = temp;
              draft.destinationInput.inboxTemplate.mapOptions = mapOptions;
            });
          }

          // Init default data first option select in case create
          if (_isEmpty(value) && !_isEmpty(temp)) {
            onChangeDataDesInput('inboxTemplate')(temp[0]);
          } else if (typeof value === 'string' && value && !_isEmpty(temp)) {
            const item = temp.find(each => each.value === value);

            if (!_isEmpty(item)) {
              onChangeDataDesInput('inboxTemplate')(item);
            }
          }
        }
      }
    }
  }, [isSmartInbox, data, design]);

  useDeepCompareEffect(() => {
    if (
      !isAntsomiAppPush ||
      data.triggerFetchCatalogInfo !== 0 ||
      _.isEmpty(data.destinationInput)
    )
      return;

    const antsAppPushTemplate = _.get(data.destinationInput, 'template', {});
    let template = _.get(antsAppPushTemplate, 'value', '');
    const templateOpts = _.get(antsAppPushTemplate, 'options', []);

    const needUpdate = _.isEmpty(template) || _.isEmpty(templateOpts);

    if (needUpdate) {
      const templates = getObjectPropSafely(
        () =>
          dataDestinationSettings.templates.selectedList.map(({ id }) => id),
        [],
      );

      let options = [];
      const mapOptions = {};
      if (_.isArray(templates)) {
        options = templates.map(templateItem => {
          const item = TEMPLATES[templateItem];
          mapOptions[templateItem] = item;

          return item;
        });
      }

      if (_.isObject(template)) template = template.value;
      const templateOuter = getInputValueObjType(selectedTemplateDashboard);

      // Re-append template if template saved in the past has been removed
      if (
        template &&
        !_.has(mapOptions, template) &&
        _.isEmpty(templateOuter)
      ) {
        const temp = TEMPLATES[template];

        mapOptions[template] = temp;
        options.push(temp);
      }

      const fallbackTemplate = options[0]?.value || '';
      const templateSelected = template || templateOuter || fallbackTemplate;
      const isCarousel =
        templateSelected === TEMPLATE_KEYS.SIMPLE_IMAGE_CAROUSEL;
      const isRating = templateSelected === TEMPLATE_KEYS.RATING;
      const isSmallImage = templateSelected === TEMPLATE_KEYS.SMALL_IMAGE;

      setData(draft => {
        if (_.has(draft.destinationInput, 'imageCarousel')) {
          draft.destinationInput.imageCarousel.isRequired = isCarousel;
        }
        if (_.has(draft.destinationInput, 'imageUrl')) {
          draft.destinationInput.imageUrl.isRequired = isSmallImage;
        }
        if (_.has(draft.destinationInput, 'advButton')) {
          draft.destinationInput.advButton.maxOptions = isRating ? 1 : 2;

          if (
            _.has(
              draft.destinationInput,
              'advButton.options[0].inputs.advBtnLabel.isRequired',
            )
          ) {
            draft.destinationInput.advButton.options[0].inputs.advBtnLabel.isRequired = isRating;
          }
        }

        const destInputParsed = JSON.parse(
          JSON.stringify(draft.destinationInput),
        );
        const ratingIconType = getInputValueObjType(
          _.get(destInputParsed, 'ratingIconType.value', ''),
        );
        const ratingScale = getInputValueObjType(
          _.get(destInputParsed, 'ratingScale.value', 0),
        );
        GEN_RATING_TEXT.forEach(ratingKey => {
          const ratingNumber = Number(ratingKey.replace(/[^0-9]/g, ''));
          const isRequired =
            +ratingNumber <= +ratingScale &&
            ratingIconType === ICON_TYPE_KEY.TEXT_EMOJI;

          if (_.has(draft.destinationInput, ratingKey)) {
            draft.destinationInput[ratingKey].isRequired = isRequired;
          }
        });

        if (
          isRating &&
          ratingIconType &&
          _.has(draft.destinationInput, 'ratingSelected') &&
          _.has(draft.destinationInput, 'ratingUnselected')
        ) {
          const isRequired = [
            ICON_TYPE_KEY.SYSTEM,
            ICON_TYPE_KEY.UPLOAD,
          ].includes(ratingIconType);
          let [componentEl, validateFunc] = [
            () => null,
            MAP_VALIDATE.validated,
          ];

          switch (ratingIconType) {
            case ICON_TYPE_KEY.TEXT_EMOJI: {
              componentEl = MAP_INPUT_TYPE.color;
              validateFunc = MAP_VALIDATE.validated;
              break;
            }
            case ICON_TYPE_KEY.SYSTEM: {
              componentEl = MAP_INPUT_TYPE.selectIcon;
              validateFunc = MAP_VALIDATE.selectIcon;
              break;
            }
            case ICON_TYPE_KEY.UPLOAD: {
              componentEl = MAP_INPUT_TYPE.imageUrl;
              validateFunc = MAP_VALIDATE.imageUrl;
              break;
            }
            default: {
              break;
            }
          }

          ['ratingSelected', 'ratingUnselected'].forEach(key => {
            draft.destinationInput[key].componentEl = componentEl;
            draft.destinationInput[key].validate = validateFunc;
            draft.destinationInput[key].isRequired = isRequired;
          });
        }
      });

      if (!_.isEqual(templateOpts, options)) {
        setData(draft => {
          if (_.has(draft.destinationInput, 'template')) {
            draft.destinationInput.template.options = options;
            draft.destinationInput.template.mapOptions = mapOptions;
          }
        });

        if (
          !_.isEmpty(selectedTemplateDashboard) &&
          _.isEmpty(template) &&
          _.has(mapOptions, templateOuter)
        ) {
          onChangeDataDesInput('template')(selectedTemplateDashboard);
        } else {
          onChangeDataDesInput('template')(
            mapOptions[template || fallbackTemplate],
          );
        }
      }
    }
  }, [
    isAntsomiAppPush,
    data.triggerFetchCatalogInfo,
    dataDestinationSettings,
    selectedTemplateDashboard,
    data.variants.activeId,
  ]);

  // Update list template for [isAntsomiLine]
  useDeepCompareEffect(() => {
    try {
      const { LINE } = CATALOG_CODE;

      const isLine = LINE === data?.catalogCode;
      const destId = get(data, 'workflowDestination.value.destinationId', '');

      if (isLine && destId) {
        const dataTemplates = getObjectPropSafely(
          () => dataDestinationSettings?.templates,
          {},
        );
        const currentTemplateOptions = getObjectPropSafely(
          () => data.destinationInput.template.options,
        );
        const { activeTemplate = '', selectedList = [] } = dataTemplates || {};

        if (
          !_isEmpty(activeTemplate) &&
          !_isEmpty(selectedList) &&
          Array.isArray(selectedList)
        ) {
          const mapOptions = {};
          const options = [];

          selectedList.forEach(selectedItem => {
            const item = {
              label: selectedItem.label,
              value: selectedItem.id,
            };

            mapOptions[selectedItem.id] = item;
            options.push(item);
          });

          const templateVariant = getObjectPropSafely(
            () =>
              data.destinationInput &&
              data.destinationInput.template &&
              data.destinationInput.template.value,
          );
          const templateVariantValue =
            typeof templateVariant === 'object'
              ? templateVariant.value
              : templateVariant;

          // Re-append template if template saved in the past has been removed
          if (!has(mapOptions, templateVariantValue) && templateVariantValue) {
            const catalogCode = getObjectPropSafely(
              () => props.activeNode.catalogCode,
            );

            const templateTmp = (TEMPLATE_LIST[catalogCode] || []).find(
              each => each.id === templateVariantValue,
            );

            if (!_isEmpty(templateTmp)) {
              const templateAppend = {
                label: templateTmp.label,
                value: templateTmp.id,
              };
              mapOptions[templateTmp.id] = templateAppend;
              options.push(templateAppend);
            }
          }

          if (!isEqual(currentTemplateOptions, options)) {
            setData(draft => {
              const originalState = original(draft);

              if (has(originalState.destinationInput, 'template')) {
                draft.destinationInput.template.options = options;
                draft.destinationInput.template.mapOptions = mapOptions;
              }
            });
          }
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'useEffect',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }, [
    data?.campaignId,
    data?.catalogCode,
    dataDestinationSettings?.templates,
    selectedTemplateDashboard,
    data.variants.activeId,
    data?.workflowDestination?.value,
  ]);

  // For case Zalo ZNS
  useDeepCompareEffect(() => {
    if (!isZaloZNS) return;

    const workflowDestId = get(
      data,
      'workflowDestination.value.destinationId',
      '',
    );
    const destinationId = data?.destinationId || workflowDestId;
    const templateOpts = get(data, 'destinationInput.template.options', []);

    if (!data.catalogCode || !destinationId) return;

    const isFetch =
      isEmpty(templateOpts) && has(data, 'destinationInput.template.value');

    if (isFetch) {
      const templateActive = get(data, 'destinationInput.template.value', {});

      DestinationServices.info
        .getListZNS({
          destinationId,
          catalogCode: data?.catalogCode,
        })
        .then(res => {
          if (res.code === 200 && res.data && res.data.length) {
            const mapOptions = {};
            const options = [];

            res.data.forEach(template => {
              const { templateId, templateName } = template;
              const item = {
                ...template,
                label: `${templateId} | ${templateName}`,
                value: templateId,
              };

              options.push(item);
              mapOptions[templateId] = item;
            });
            setData(draft => {
              const originalState = original(draft);

              if (has(originalState.destinationInput, 'template')) {
                draft.destinationInput.template.options = options;
                draft.destinationInput.template.mapOptions = mapOptions;
              }
            });

            if (isEmpty(templateActive)) {
              const createJrnInfoTemplate = get(
                creatingJourneyInfo,
                'data.template.value',
              );

              const initValue = get(
                data,
                'destinationInput.template.initValue',
                '',
              );
              let templateUpdate = options[0];
              if (initValue) {
                templateUpdate = mapOptions[initValue] || {};
              } else if (
                createJrnInfoTemplate &&
                has(mapOptions, createJrnInfoTemplate)
              ) {
                templateUpdate = mapOptions[createJrnInfoTemplate];
              }

              onChangeDataDesInput('template')(templateUpdate);
            }
          } else {
            setData(draft => {
              const originalState = original(draft);

              if (has(originalState.destinationInput, 'template')) {
                draft.destinationInput.template.options = [];
                draft.destinationInput.template.mapOptions = {};
                draft.destinationInput.template.value = {};
              }
            });
          }
        })
        .catch(err => {
          console.log(err);
        });
    }
  }, [
    isZaloZNS,
    data?.catalogCode,
    data?.destinationId,
    data?.workflowDestination?.value?.destinationId,
    data?.destinationInput?.template?.value,
  ]);

  // Update & reset & refill data when change template in [isAntsomiLine, isZaloOA]
  useDeepCompareEffect(() => {
    try {
      if (isAntsomiLine || isZaloOA) {
        const currentTemplate = getObjectPropSafely(
          () => data.destinationInput.template.value,
        );
        const catalogCode = getObjectPropSafely(
          () => props.activeNode.catalogCode,
        );

        if (typeof currentTemplate === 'object') {
          const { resetList = [], refillList = [] } = reUpdateDataByCatalog({
            catalogCode,
            destinationInput: data.destinationInput,
            templateType: currentTemplate.value,
          });

          (resetList || []).forEach(eachName => {
            setData(draft => {
              draft.destinationInput[eachName] = {
                ...data.destinationInput[eachName],
                isRequired: false,
                value: '',
                initValue: '',
              };
            });

            if (eachName === 'text') {
              setRefreshComponentKey(refreshComponentKey + 1);
            }
          });

          (refillList || []).forEach(eachName => {
            let isRequired = true;
            if (['transactionMessage', 'mediaMessage'].includes(eachName)) {
              isRequired = false;
            }

            setData(draft => {
              draft.destinationInput[eachName].isRequired = isRequired;
            });
          });

          // Preselect default sticker set
          if (
            [TEMPLATE_TYPES.STICKER, TEMPLATE_ZALO_OA_TYPES.STICKER].includes(
              currentTemplate.value,
            )
          ) {
            const stickerSetOptions = getObjectPropSafely(
              () => data.destinationInput.stickerType.options || {},
            );

            if (
              !_isEmpty(stickerSetOptions) &&
              Array.isArray(stickerSetOptions)
            ) {
              const preselectSet = stickerSetOptions.find(eachSet =>
                PRE_SELECT_STICKER_SET.includes(eachSet.value),
              );
              const stickerSetTmp =
                typeof stickerSet === 'object' ? stickerSet.value : stickerSet;

              if (preselectSet && !stickerSetTmp) {
                onChangeDataDesInput('stickerType')(preselectSet);
              }
            }
          }
        }
      } else if (
        isZaloZNS &&
        data.destinationInput &&
        data.destinationInput.template &&
        data.destinationInput.template.value &&
        data.destinationInput.template.value.templateId
      ) {
        const templateData = {
          ...creatingJourneyInfo,
          catalogInfo: data.workflowDestination.value,
          data: {
            template: data.destinationInput.template.value,
          },
        };
        setTemplateSelectedZNS(templateData);

        // set templateId
        if (has(data.destinationInput, 'templateId')) {
          const templateId = get(
            data,
            'destinationInput.template.value.value',
            '',
          );
          onChangeDataDesInput('templateId')(`${templateId}`);
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'useEffect',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }, [
    getObjectPropSafely(
      () => data.destinationInput && data.destinationInput.template.value,
    ),
  ]);

  // Set default sticker when change Sticker set
  useEffect(() => {
    try {
      if ((isAntsomiLine || isZaloOA) && typeof stickerSet === 'object') {
        const { value: valueSet = '' } = stickerSet;

        const catalogCode = getObjectPropSafely(
          () => props.activeNode.catalogCode,
        );
        const stickerValue = getObjectPropSafely(
          () => data.destinationInput.sticker.value,
          '',
        );
        const stickerList = STICKER_FACTORY[catalogCode];

        if (isEmpty(stickerList)) return;

        const stickerSetMapping = getObjectPropSafely(
          () => stickerList[valueSet],
        );

        let isInvalidSticker =
          valueSet && stickerValue && Array.isArray(stickerSetMapping);

        if (isAntsomiLine) {
          isInvalidSticker =
            isInvalidSticker && !stickerSetMapping.includes(stickerValue);
        }

        if (isZaloOA) {
          isInvalidSticker =
            isInvalidSticker &&
            stickerSetMapping.findIndex(each => each.id === stickerValue) ===
              -1;
        }

        if ((valueSet && !stickerValue) || isInvalidSticker) {
          const { stickerId = '' } = serializeSticker({
            catalogCode,
            stickerInfo: stickerSetMapping[0],
          });

          onChangeDataDesInput('sticker')(stickerId);
        }
      }
    } catch (error) {
      addMessageToQueue({
        path: PATH,
        func: 'useEffect',
        data: error.stack,
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }, [stickerSet]);

  // In case has recommended content source, if has bo item type change
  // -> need to reset value of the rest all fields except content source and template if exist
  useDeepCompareEffect(() => {
    if (emitItemTypeContentSource) {
      setData(draft => {
        draft.dynamicFields.forEach(field => {
          const dynamicFields =
            JSON.parse(JSON.stringify(draft.dynamicFields)) || [];
          const destinationInput = JSON.parse(
            JSON.stringify(draft.destinationInput),
          );
          const ignoreResetFields = [
            'contentSources',
            'contentType',
            'template',
          ];
          const excludeDynamicFieldIgnore = dynamicFields.filter(
            inputItem => !ignoreResetFields.includes(inputItem),
          );

          const allowReset = excludeDynamicFieldIgnore.some(
            inputKey =>
              !_.isEmpty(_.get(destinationInput, [inputKey, 'value'])),
          );

          if (
            allowReset &&
            _.has(draft.destinationInput, field) &&
            !ignoreResetFields.includes(field)
          ) {
            const inputSetting = _.get(destinationInput, field, {});

            let fallbackValue = '';
            if (inputSetting) {
              if (inputSetting.inputType === 'select') fallbackValue = {};
            }

            let resetValue = _.get(
              destinationInput,
              [field, 'default'],
              fallbackValue,
            );

            // in case reset value has default property and it is null/undefined
            if (!resetValue) resetValue = fallbackValue;

            // Mutate all field to set value of null except coming field name & ignore reset fields
            _.set(draft.destinationInput, [field, 'initValue'], resetValue);
            _.set(draft.destinationInput, [field, 'value'], resetValue);
          }
        });

        return draft;
      });
    }
  }, [emitItemTypeContentSource]);

  const handleChangeTemplateSample = template => {
    if (has(data.destinationInput, 'template')) {
      onChangeDataDesInput('template')(template);
    }
  };

  const renderBlastFilter = () => {
    let element = null;

    if (triggerType === NODE_TYPE.SCHEDULED) {
      element = (
        <Grid
          container
          style={{
            position: isViewMode && isBlastCampaign ? 'relative' : '',
          }}
        >
          <Grid
            item
            xs={colLeftCampaign}
            className={classNames('grid-col-left', {
              [classes.adjustCol]: !isViewMode,
            })}
          >
            <div
              style={{
                textAlign: 'left',
                marginRight: '30px',
                paddingTop: '10px',
                color: '#666',
                fontSize: '12px',
              }}
            >
              <span>
                {getTranslateMessage(
                  TRANSLATE_KEY._BLAST_CAMPAIGN_SEND_TO,
                  'Send to',
                )}
              </span>
              {!isViewMode && (
                <span
                  style={{
                    color: 'red',
                  }}
                >
                  {' '}
                  *
                </span>
              )}
            </div>
          </Grid>
          <Grid
            item
            xs={colRightCampaign}
            style={
              !isViewMode
                ? {
                    flex: 1,
                    maxWidth: '82%',
                  }
                : {}
            }
            className={classNames('grid-col-right')}
          >
            <WrapperTargetAudience>
              {isDefaultRichMenu ? (
                <div
                  style={{
                    color: '#000000',
                    fontSize: '12px',
                    paddingTop: 5,
                  }}
                >
                  {getTranslateMessage(TRANSLATE_KEY._, 'All Line followers')}
                </div>
              ) : (
                <TargetAudienceV2
                  activeRow={activeRow}
                  initData={props.initValueTargetAudience.get('targetAudience')}
                  isShowAlert={isThirdPartyCampaigns}
                  onChange={val => handleCallbackAudience(val)}
                  validateKey={props.validateKey}
                  validateKeyBlast={props.validateKeyBlast}
                  componentId={props.componentId}
                  callback={props.callback}
                  errors={[]}
                  disabled={false}
                  isViewMode={isViewMode}
                  errorsSchedule={[]}
                  isBlastCampaign={isBlastCampaign}
                  dataValidateSwitchTabAudiences={
                    props.dataValidateSwitchTabAudiences
                  }
                  refreshKeyValidateAudiences={
                    props.refreshKeyValidateAudiences
                  }
                  hasOpenModalConfirm={props.hasOpenModalConfirm}
                  isCollapsed={false}
                  itemTypeId={itemTypeId}
                  isFirstCampaign={props.isFirstCampaign}
                />
              )}
              {/* <StyledFormHelperText
        id="component-helper-text"
        error={props.errorSendTo.has(props.activeNode.nodeId)}
      >
        {props.errorSendTo.has(props.activeNode.nodeId) &&
          !isViewMode &&
          props.errorSendTo
            .get(props.activeNode.nodeId)
            .get('message')}
      </StyledFormHelperText> */}
            </WrapperTargetAudience>
          </Grid>
        </Grid>
      );
    }

    return element;
  };

  const renderTabs = tabList => {
    if (!tabList || !Array.isArray(tabList)) return null;

    return tabList.map(tab => {
      const isActiveTab = [activeTabDisplay].includes(tab.key);

      return (
        <TabPanel
          key={tab.key}
          label={
            <span
              style={{
                fontSize: '14px',
                fontFamily: 'Roboto',
                fontWeight: isActiveTab ? 'bold' : 'normal',
                color: isActiveTab ? '#1F5FAC' : '#000000',
              }}
            >
              {tab.label}
            </span>
          }
          eventKey={tab.key}
        />
      );
    });
  };

  const renderChildDynamicFieldOptions = (options = [], configs = {}) => {
    if (_isEmpty(options) || options.length === 0) return null;

    const { channelCode = '', catalogCode = '', step = '' } = configs;

    return options.map((option, index) => {
      const { label = '', value = '', basicInputs = [] } = option;
      let contentChild = null;

      if (basicInputs && Array.isArray(basicInputs)) {
        contentChild = basicInputs.map(each => (
          <InputComponent
            {...data.destinationInput[each]}
            selectionStartIndex={selectionStartIndex}
            componentKey={`${each}-${data.variants.activeId}`}
            moduleConfig={props.moduleConfig}
            onTagRemove={handleTagRemove}
            onChangeExtraDataTagProperty={handleChangeExtraDataTagProperty}
            isNewCustomTag
            useAllocatedCode={props.useAllocatedCode}
            objectWidgetInput={data.objectWidgetInput}
            // variantExtraData={data.variantExtraData[each]}
            variantExtraData={
              !_.isEmpty(data.variantExtraData) && data.variantExtraData[each]
            }
            journeyNodeTagProperties={journeyNodeTagProperties}
            callback={callback}
            onChange={onChangeDataDesInput}
            otherData={data.variants.cacheInfo[data.variants.activeId]}
            onChangeOthers={onChangeOthersData}
            key={`${value}-${each}`}
            classes={{
              ...classes,
              isCustomSpace: true,
            }}
            disabled={
              isJourneyTemplateMode
                ? false
                : !props.roleActions.has(
                    STORY_SETUP_ACTION.EDIT_NODE_DESTINATION_CONTENT,
                  ) && props.previousNodes[componentId] !== undefined
            }
            isRecommendation={isRecommendation}
            design={design}
            colLeft={colLeftCampaign}
            colRight={colRightCampaign}
            validateKey={props.validateKey}
            style={{
              position: 'relative',
            }}
            isViewMode={isViewMode}
            channelElement={channelCode}
            catalogCode={catalogCode}
            step={step + 1}
            setIsFetchData={setIsFetchData}
            configure={configure}
            appendPersonalizeType={appendPersonalizeType}
            journeySettings={journeySettings}
            destinationSettings={dataDestinationSettings}
            setSelectionStartIndex={setSelectionStartIndex}
            itemTypeId={itemTypeId}
            isBlastCampaign={isBlastCampaign}
            isTitleAlignLeft={
              isBlastCampaign || isJourneyTemplateMode || isAntsomiAppPush
            }
            isFullWidth={isBlastCampaign || isAntsomiAppPush}
            isAdjustCol={!isBlastCampaign}
          />
        ));
      }

      return (
        // eslint-disable-next-line react/no-array-index-key
        <Fragment key={`${label}-${value}-${index}`}>
          <AccordionDetailsCustom
            key={value}
            id={
              OBSERVER_VIEW_DEVICES.includes(value) ? `observer_devices_id` : ''
            }
            data-type-observe={value}
          >
            <SubHeadingAccordion>{label}</SubHeadingAccordion>
            {contentChild}
          </AccordionDetailsCustom>
          {options.length - 1 !== index && (
            <Divider
              style={{
                margin: '8px 0px',
              }}
            />
          )}
        </Fragment>
      );
    });
  };

  const renderContent = () => {
    if (isLoading) return null;
    const { catalogCode } = props.activeNode;
    let isShowPreviewObjectWidget = false;

    if (isChannelWebPersonalization) {
      // console.log(
      //   'Object.values(data.variantExtraData)',
      //   Object.values(data.variantExtraData)[0],
      // );
      if (Object.keys(data.variantExtraData).length > 0) {
        const tmp = Object.values(data.variantExtraData)[0];
        if (tmp.design === 'template' && tmp.type === 'htmlEditor') {
          isShowPreviewObjectWidget = true;
        }
      }
    }

    const renderNormalComposeFields = ({ step }) => {
      const contentNode = renderDynamicFieldsCompose({
        step,
        type: 'NORMAL',
        isRenderLayoutTabs: false,
        isRenderLayoutGroupPanel: false,
        isForceHide: isSmartInbox,
      });

      if (isWrapInPanelBox && _.isArray(contentNode)) {
        const recommendationGroupNodes = _.remove(
          contentNode,
          node =>
            _.get(node, 'props.isGroupRecommendation', false) ||
            ['contentType', 'contentSources'].includes(
              _.get(node, 'props.name', ''),
            ),
        );

        return (
          <Fragment>
            {recommendationGroupNodes}
            <AccordionStyled defaultExpanded>
              <AccordionSummaryCustom
                expandIcon={
                  <ExpandMoreIcon
                    style={{
                      color: '#585858',
                    }}
                  />
                }
                aria-controls="content-recommendation"
                id="header-recommendation"
                IconButtonProps={{
                  edge: 'start',
                }}
              >
                <HeadingAccordion
                  style={{
                    fontSize: '12px',
                    color: '#000000',
                  }}
                >
                  {getTranslateMessage(TRANSLATE_KEY._, 'Notification Content')}
                </HeadingAccordion>
              </AccordionSummaryCustom>
              <AccordionDetailsCustom>{contentNode}</AccordionDetailsCustom>
            </AccordionStyled>
          </Fragment>
        );
      }

      return contentNode;
    };

    // console.log('data.variants 3', data.objectWidgetInput);
    // console.log('props.objectWidgetInput 1', data.objectWidgetInput);
    const renderDynamicFieldsCompose = ({
      step: _step = '',
      type = 'NORMAL',
      isRenderLayoutTabs = false,
      isRenderLayoutGroupPanel = true,
      isForceHide = false,
    }) => {
      if (isForceHide) return null;

      // Render Dynamic data by default
      if (type === 'NORMAL') {
        return data.dynamicFields.map(each => {
          const hasGroup = getObjectPropSafely(
            () => !_isEmpty(data && data.destinationInput[each].groupConfigs),
            false,
          );

          if (hasGroup) return null;

          const isHiddenInput = checkHideInputSpecific({
            name: each,
          });

          if (isHiddenInput) return null;

          const catalogCodeActive =
            props.activeNode && props.activeNode.catalogCode;

          const enableShortLinkSpecific = (
            LIST_FIELD_BY_CATALOG[catalogCodeActive] || []
          ).includes(each);

          return (
            <InputComponent
              {...data.destinationInput[each]}
              selectionStartIndex={selectionStartIndex}
              componentKey={`${each}-${
                data.variants.activeId
              }-${refreshComponentKey}-${props.catalogCode}`}
              objectWidgetInput={data.objectWidgetInput}
              // variantExtraData={data.variantExtraData[each]}
              variantExtraData={
                !_.isEmpty(data.variantExtraData) && data.variantExtraData[each]
              }
              onChange={onChangeDataDesInput}
              otherData={data.variants.cacheInfo[data.variants.activeId]}
              onChangeOthers={onChangeOthersData}
              callback={callback}
              moduleConfig={props.moduleConfig}
              onTagRemove={handleTagRemove}
              onChangeExtraDataTagProperty={handleChangeExtraDataTagProperty}
              isNewCustomTag
              useAllocatedCode={props.useAllocatedCode}
              journeyNodeTagProperties={journeyNodeTagProperties}
              appendPersonalizeType={appendPersonalizeType}
              journeySettings={journeySettings}
              destinationSettings={dataDestinationSettings}
              key={each}
              stickerSet={isAntsomiLine || isZaloOA ? stickerSet : {}}
              classes={{
                ...classes,
                isCustomSpace: true,
              }}
              disabled={
                isJourneyTemplateMode
                  ? false
                  : !props.roleActions.has(
                      STORY_SETUP_ACTION.EDIT_NODE_DESTINATION_CONTENT,
                    ) && props.previousNodes[componentId] !== undefined
              }
              setEmitSetDefaultSmartInbox={setEmitSetDefaultSmartInbox}
              isRecommendation={isRecommendation}
              colLeft={colLeftVariant}
              colRight={colRightVariant}
              messageType={isZaloOA ? messageType : {}}
              design={design}
              validateKey={props.validateKey}
              style={{
                position: 'relative',
              }}
              isHidden={isHiddenInput}
              callbackWhatsappTemplate={callbackWhatsappTemplate}
              isFullWidth={
                isSmartInbox ||
                isAntsomiLine ||
                isAntsomiAppPush ||
                isAeonMallInAppMessage ||
                isZaloOA ||
                isWebhookGoldenGate ||
                isBlastCampaign ||
                getObjectPropSafely(
                  () => data.destinationInput[each].isFullWidth,
                  false,
                ) ||
                isJourneyTemplateMode
              }
              isTitleAlignLeft={
                isSmartInbox ||
                isAntsomiLine ||
                isAntsomiAppPush ||
                isAeonMallInAppMessage ||
                isZaloOA ||
                isBlastCampaign ||
                getObjectPropSafely(
                  () => data.destinationInput[each].isTitleAlignLeft,
                  false,
                ) ||
                isJourneyTemplateMode
              }
              isViewMode={isViewMode}
              channelElement={channelCode}
              catalogCode={catalogCode}
              step={_step + 1}
              setIsFetchData={setIsFetchData}
              configure={configure}
              setSelectionStartIndex={setSelectionStartIndex}
              selectedTemplateDashboard={selectedTemplateDashboard}
              isDefaultRichMenu={isDefaultRichMenu}
              tokenLineRichMenu={tokenLineRichMenu}
              itemTypeId={itemTypeId}
              isAdjustCol={!isBlastCampaign}
              isBlastCampaign={isBlastCampaign}
              enableShortLinkSpecific={enableShortLinkSpecific}
              creatingJourneyInfo={creatingJourneyInfo}
              templateSelectedZNS={isZaloZNS ? templateSelectedZNS : {}}
              destinationInfo={data.workflowDestination.value}
              destinationInput={data.destinationInput}
              contentAccentType={contentAccentType}
            />
          );
        });
      }

      if (
        type !== 'GROUPS' ||
        (!isRenderLayoutGroupPanel && !isRenderLayoutTabs)
      )
        return null;

      // Render Dynamic data with Layout Tabs
      if (isRenderLayoutTabs) {
        let groupConfigsFiltered = _cloneDeep(groupConfigsMapping);

        if (isOnlyInbox && !_isEmpty(groupConfigsMapping)) {
          groupConfigsFiltered = groupConfigsMapping.filter(
            groupItem => groupItem.key === 'inbox',
          );
        }

        return (
          <>
            <Wrapper
              style={{
                position: 'relative',
                width: '100%',
                marginTop: 8,
              }}
            >
              <UITabs
                height="36px"
                activeTab={activeTabDisplay}
                classNameTabNav="d-flex justify-center"
                // tippy
                isBoxShadow={false}
                onChange={tabToActive => setActiveTabDisplay(tabToActive)}
              >
                {renderTabs(groupConfigsFiltered)}
              </UITabs>
            </Wrapper>
            {data.dynamicFields.map(each => {
              const tabKey = getObjectPropSafely(
                () =>
                  data.destinationInput[each] &&
                  data.destinationInput[each].groupConfigs.panelCode,
                false,
              );
              const hasDividerAfter = getObjectPropSafely(
                () =>
                  data.destinationInput[each] &&
                  data.destinationInput[each].hasDividerAfter,
                false,
              );
              const isHidden = checkIsHiddenInputComponent({
                name: each,
                data: data.destinationInput[each],
                tabKey,
                activeTabDisplay,
                selectedTemplate,
                catalogCode: props.activeNode.catalogCode,
                extraData: {
                  data,
                },
              });

              const catalogCodeActive =
                props.activeNode && props.activeNode.catalogCode;

              const enableShortLinkSpecific = (
                LIST_FIELD_BY_CATALOG[catalogCodeActive] || []
              ).includes(each);

              return (
                <Fragment key={each}>
                  <InputComponent
                    {...data.destinationInput[each]}
                    selectionStartIndex={selectionStartIndex}
                    componentKey={`${each}-${
                      data.variants.activeId
                    }-${refreshComponentKey}`}
                    objectWidgetInput={data.objectWidgetInput}
                    // variantExtraData={data.variantExtraData[each]}
                    variantExtraData={
                      !_.isEmpty(data.variantExtraData) &&
                      data.variantExtraData[each]
                    }
                    onChange={onChangeDataDesInput}
                    onChangeOthers={onChangeOthersData}
                    appendPersonalizeType={appendPersonalizeType}
                    destinationSettings={dataDestinationSettings}
                    journeySettings={journeySettings}
                    callback={callback}
                    design={design}
                    key={each}
                    classes={{
                      ...classes,
                      isCustomSpace: true,
                    }}
                    disabled={
                      isJourneyTemplateMode
                        ? false
                        : !props.roleActions.has(
                            STORY_SETUP_ACTION.EDIT_NODE_DESTINATION_CONTENT,
                          ) && props.previousNodes[componentId] !== undefined
                    }
                    colLeft={colLeftCampaign}
                    colRight={colRightCampaign}
                    isRecommendation={isRecommendation}
                    moduleConfig={props.moduleConfig}
                    onTagRemove={handleTagRemove}
                    onChangeExtraDataTagProperty={
                      handleChangeExtraDataTagProperty
                    }
                    isNewCustomTag
                    useAllocatedCode={props.useAllocatedCode}
                    journeyNodeTagProperties={journeyNodeTagProperties}
                    setEmitSetDefaultSmartInbox={setEmitSetDefaultSmartInbox}
                    validateKey={props.validateKey}
                    style={{
                      position:
                        isSmartInbox && each === 'inboxButton'
                          ? 'unset'
                          : 'relative',
                    }}
                    isViewMode={isViewMode}
                    isFullWidth={
                      isSmartInbox ||
                      isBlastCampaign ||
                      getObjectPropSafely(
                        () => data.destinationInput[each].isFullWidth,
                        false,
                      )
                    }
                    isTitleAlignLeft={
                      isSmartInbox ||
                      isBlastCampaign ||
                      getObjectPropSafely(
                        () => data.destinationInput[each].isTitleAlignLeft,
                        false,
                      ) ||
                      isJourneyTemplateMode
                    }
                    isHidden={isHidden}
                    channelElement={channelCode}
                    catalogCode={catalogCode}
                    step={_step + 1}
                    setIsFetchData={setIsFetchData}
                    configure={configure}
                    setSelectionStartIndex={setSelectionStartIndex}
                    isAdjustCol={!isBlastCampaign}
                    isBlastCampaign={isBlastCampaign}
                    itemTypeId={itemTypeId}
                    enableShortLinkSpecific={enableShortLinkSpecific}
                    destinationInfo={data.workflowDestination.value}
                    destinationInput={data.destinationInput}
                  />
                  {hasDividerAfter && !isHidden && (
                    <Divider
                      style={{
                        margin: '8px 0px',
                      }}
                    />
                  )}
                </Fragment>
              );
            })}
          </>
        );
      }

      // Render Group Layout Panels
      return groupConfigsMapping.map((groupItem, indexGroup) => {
        const {
          label = '',
          value = '',
          options = [],
          basicInputs = [],
        } = groupItem;

        const hasBasicInputs =
          basicInputs && Array.isArray(basicInputs) && basicInputs.length > 0;
        let contentBasicInputChild = null;

        if (hasBasicInputs) {
          contentBasicInputChild = basicInputs.map(each => {
            const isHidden = checkIsHiddenInputComponent({
              name: each,
              data: data.destinationInput[each],
              selectedTemplate: currentTemplateInput,
              catalogCode,
              extraData: {
                data,
              },
            });

            if (isHidden) return null;
            return (
              <InputComponent
                {...data.destinationInput[each]}
                selectionStartIndex={selectionStartIndex}
                componentKey={`${each}-${
                  data.variants.activeId
                }-${refreshComponentKey}`}
                objectWidgetInput={data.objectWidgetInput}
                // variantExtraData={data.variantExtraData[each]}
                variantExtraData={
                  !_.isEmpty(data.variantExtraData) &&
                  data.variantExtraData[each]
                }
                callback={callback}
                onChange={onChangeDataDesInput}
                onChangeOthers={onChangeOthersData}
                key={each}
                classes={{
                  ...classes,
                  isCustomSpace: true,
                }}
                disabled={
                  isJourneyTemplateMode
                    ? false
                    : !props.roleActions.has(
                        STORY_SETUP_ACTION.EDIT_NODE_DESTINATION_CONTENT,
                      ) && props.previousNodes[componentId] !== undefined
                }
                colLeft={colLeftCampaign}
                colRight={colRightCampaign}
                appendPersonalizeType={appendPersonalizeType}
                journeySettings={journeySettings}
                destinationSettings={dataDestinationSettings}
                validateKey={props.validateKey}
                currentTemplateInput={currentTemplateInput}
                style={{
                  position: 'relative',
                }}
                setEmitSetDefaultSmartInbox={setEmitSetDefaultSmartInbox}
                isViewMode={isViewMode}
                channelElement={channelCode}
                isRecommendation={isRecommendation}
                catalogCode={catalogCode}
                step={_step + 1}
                setIsFetchData={setIsFetchData}
                moduleConfig={props.moduleConfig}
                onTagRemove={handleTagRemove}
                onChangeExtraDataTagProperty={handleChangeExtraDataTagProperty}
                isNewCustomTag
                useAllocatedCode={props.useAllocatedCode}
                journeyNodeTagProperties={journeyNodeTagProperties}
                configure={configure}
                setSelectionStartIndex={setSelectionStartIndex}
                itemTypeId={itemTypeId}
                isAdjustCol={!isBlastCampaign}
                isBlastCampaign={isBlastCampaign}
                isFullWidth={isBlastCampaign || isAntsomiAppPush}
                isTitleAlignLeft={
                  isBlastCampaign || isAntsomiAppPush || isJourneyTemplateMode
                }
              />
            );
          });
        }

        if (
          _.isArray(contentBasicInputChild) &&
          contentBasicInputChild.every(_.isNull)
        )
          return null;

        return (
          <AccordionCustom key={value} defaultExpanded={indexGroup === 0}>
            <AccordionSummaryCustom
              expandIcon={<ExpandMoreIcon />}
              aria-controls="panel1a-content"
              id="panel1a-header"
            >
              <HeadingAccordion>{label}</HeadingAccordion>
            </AccordionSummaryCustom>
            {contentBasicInputChild}
            {/* {hasBasicInputs && <Divider style={{ margin: '8px 0px' }} />} */}
            {renderChildDynamicFieldOptions(options, {
              isShowPreview,
              channelCode,
              catalogCode,
              step: _step,
            })}
          </AccordionCustom>
        );
      });
    };

    const renderSettings = step => {
      const widthOfCustomInput = getWidthCustomInput(channelCode, step);

      return (
        <Grid container>
          <Grid
            item
            xs={colGridWrapper({
              isBlastCampaign,
              isViewMode,
            })}
            style={{
              ...(isBlastCampaign
                ? {
                    padding: '10px 40px 12px 15px',
                    maxWidth: isViewMode ? '' : '100%',
                    flex: '1',
                    overflowX: isViewMode ? 'unset' : 'auto',
                  }
                : {
                    padding: 'unset',
                  }),
            }}
          >
            <DisplayContent isShow={step === 0}>
              {!isBlastCampaign && (
                <h3 className="m-bottom-0 m-top-0 width-100">
                  {mapTranslateMessage.settings}
                </h3>
              )}
              {data.defaultFields.map(each => (
                <InputComponent
                  {...data[each]}
                  onChange={onChangeData}
                  onChangeOthers={onChangeOthersData}
                  key={each}
                  classes={classes}
                  colLeft={colLeftCampaign}
                  colRight={colRightCampaign}
                  // colLeft={isShowPreview ? 3 : 2}
                  // colRight={isShowPreview ? 9 : 10}
                  isViewMode={isViewMode}
                  channelElement={channelCode}
                  catalogCode={catalogCode}
                  step={step + 1}
                  eventValue={eventValue}
                  // isAdjustCol // cho phép css các col width (dùng cho BlastCampaign)
                  isBlastCampaign={isBlastCampaign}
                  isTitleAlignLeft={isBlastCampaign || isJourneyTemplateMode}
                  componentId={componentId}
                  isAdjustCol={!isBlastCampaign}
                />
              ))}
              {channelCode === 'web_personalization' &&
                data.webChannelFields.map(each => (
                  <InputComponent
                    {...data[each]}
                    onChange={onChangeData}
                    onChangeOthers={onChangeOthersData}
                    key={each}
                    classes={classes}
                    colLeft={colLeftCampaign}
                    colRight={colRightCampaign}
                    isViewMode={isViewMode}
                    step={step + 1}
                    isAdjustCol={!isBlastCampaign}
                    isBlastCampaign={isBlastCampaign}
                    isTitleAlignLeft={isBlastCampaign || isJourneyTemplateMode}
                    componentId={componentId}
                  />
                ))}

              {isBlastCampaign && renderBlastFilter()}

              {channelCode !== 'web_personalization' &&
                !isDefaultRichMenu &&
                !isThirdPartyCampaigns && (
                  <Grid
                    style={{
                      marginTop: '10px',
                      marginBottom: '20px',
                    }}
                    container
                  >
                    <Grid
                      item
                      xs={colLeftCampaign}
                      className={classNames('grid-col-left', {
                        [classes.adjustCol]: isBlastCampaign && !isViewMode,
                      })}
                    >
                      <WrapperTitleCustomInput
                        isBlastCampaign={isBlastCampaign}
                      >
                        <Title
                          style={{
                            // paddingRight: '30px',
                            marginTop: isViewMode ? '0px' : ' 5px',
                            whiteSpace: isBlastCampaign ? 'normal' : 'nowrap',
                            textAlign: isBlastCampaign ? 'left' : 'right',
                          }}
                        >
                          Delivery Hours of the Day
                        </Title>
                      </WrapperTitleCustomInput>
                    </Grid>
                    <Grid
                      container
                      item
                      xs={colRightCampaign}
                      className={classNames('grid-col-right')}
                      style={
                        isBlastCampaign && !isViewMode
                          ? {
                              flex: 1,
                            }
                          : {}
                      }
                    >
                      <WrapperSelectHoursOfDay
                        isViewMode={isViewMode}
                        onlyAllTime={false}
                        onChange={onChangeSelectHoursOfDay}
                        initData={data.timeTarget}
                        validateKey={props.validateKey}
                        isBlastCampaign={isBlastCampaign}
                      />
                    </Grid>
                  </Grid>
                )}
              {/* Custom Input Campaign */}
              {data.campaignCustomInput.workspaces.length > 0 && (
                <Grid
                  container
                  style={{
                    paddingTop: '0.8rem',
                  }}
                >
                  <Grid
                    item
                    xs={colLeftCampaign}
                    className={classNames('grid-col-left', {
                      [classes.adjustCol]: isBlastCampaign && !isViewMode,
                    })}
                  >
                    <WrapperTitleCustomInput isBlastCampaign={isBlastCampaign}>
                      <Title>Custom Input</Title>
                    </WrapperTitleCustomInput>
                  </Grid>
                  <Grid
                    xs={colRightCampaign}
                    className={classNames('grid-col-right')}
                    style={
                      isBlastCampaign && !isViewMode
                        ? {
                            flex: 1,
                          }
                        : {}
                    }
                  >
                    {data.campaignCustomInput.workspaces.length > 0 &&
                      data.campaignCustomInput.workspaces.map(row => (
                        <Grid
                          container
                          key={row.id}
                          spacing={3}
                          style={{
                            paddingBottom: '8px',
                          }}
                        >
                          {row.sections.map(cell => (
                            <Grid item xs key={cell.id}>
                              <Title className={props.classNameTitle}>
                                {cell.properties.label}
                                {!!cell.properties.isRequired && ' *'}
                              </Title>
                              <InputComponent
                                {...cell}
                                options={cell.option}
                                onChange={value => {
                                  onChangeDataCustomInputVia({
                                    value,
                                    type: 'campaign',
                                    workspaces: [
                                      ...data.campaignCustomInput.workspaces,
                                    ],
                                  });
                                  onChangeDataCustomInputVia({
                                    value,
                                    type: 'campaign',
                                    workspaces: [
                                      ...data.campaignCustomInput.workspaces,
                                    ],
                                  });
                                }}
                                value={cell.properties.value}
                                infor={{
                                  rowId: row.id,
                                  cellId: cell.id,
                                }}
                                classes={{
                                  item: '',
                                }}
                                isViewMode={isViewMode}
                                widthOfCustomInput={widthOfCustomInput}
                                isTitleAlignLeft={
                                  isBlastCampaign || isJourneyTemplateMode
                                }
                                isAdjustCol={!isBlastCampaign}
                              />
                            </Grid>
                          ))}
                        </Grid>
                      ))}
                  </Grid>
                </Grid>
              )}
            </DisplayContent>
            {step === 0 &&
              !activeNode.nodeProcessInfo &&
              !props.isBlastCampaign &&
              activeNode.catalogCode === CATALOG_CODE.ZNS && (
                <Grid container>
                  <Grid
                    item
                    xs={colLeftCampaign}
                    className={classNames('grid-col-left')}
                  >
                    <span
                      style={{
                        fontSize: '12px',
                        textAlign: 'right',
                        color: '#000000',
                        paddingRight: 30,
                        display: 'inline-block',
                        width: '100%',
                      }}
                    >
                      {getTranslateMessage(TRANSLATE_KEY._, 'ZNS Template')}
                      <span
                        style={{
                          color: '#EF3340',
                        }}
                      >
                        &nbsp;*
                      </span>
                    </span>
                  </Grid>
                  <Grid
                    xs={colRightCampaign}
                    className={classNames('grid-col-right')}
                  >
                    <WrapperListTemplateZNS isViewMode={isViewMode}>
                      <ListTemplates
                        channelId={data.channelId}
                        catalogCode={CATALOG_CODE.ZNS}
                        isXlabIconSM
                        styles={{
                          wrapperCard: {
                            padding: 15,
                          },
                          head: {},
                          wrapperIconLabel: {
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'row',
                            gap: '15px',
                          },
                          wrapperImage: {
                            width: 310,
                            height: 210,
                          },
                        }}
                        onChangeActive={({ template }) =>
                          handleChangeTemplateSample(template)
                        }
                        isUseSettingDestination
                        variantActiveId={data?.variants?.activeId}
                        destinationData={data.workflowDestination.value}
                        value={get(
                          data,
                          'destinationInput.template.value.value',
                          null,
                        )}
                        warnNoTemplate={mapTranslateMessage.warnNoTemplate}
                      />
                    </WrapperListTemplateZNS>
                  </Grid>
                </Grid>
              )}
          </Grid>

          {displayForecast ? (
            !isViewMode && !isDefaultRichMenu ? (
              <Grid
                item
                xs={4}
                style={{
                  borderLeft: '1px solid #E5E5E5',
                  maxWidth: '280px',
                }}
              >
                <PreviewForeCast
                  isBlastCampaign
                  dataCampaign={{
                    design,
                    activeCampaign: props.activeNode,
                    campaignList: props.campaignList,
                    targetAudience: targetAudienceData || props.targetAudience,
                    audienceTypeBlastCampaign: props.audienceTypeBlastCampaign,
                  }}
                  isFixedWidth
                  itemTypeId={props.itemTypeId}
                  type={props.previewForecast.type}
                  previewDisabled={
                    props.preview.main.isLoadingPreviewPie
                      ? props.previewForecast.isValid
                      : !props.previewForecast.isValid
                  }
                  modePreview="blast-campaign"
                  onClick={props.onUpdateValidateKeyBlast}
                  moduleConfig={MODULE_CONFIG}
                  componentId={componentId}
                />
              </Grid>
            ) : (
              <Grid
                item
                xs={isDefaultRichMenu ? 1 : 6}
                style={
                  isDefaultRichMenu
                    ? {
                        maxWidth: '20px',
                      }
                    : {}
                }
              />
            )
          ) : null}
        </Grid>
      );
    };

    const renderAntsomiLineConfigFields = ({ step = '' }) => {
      const { dynamicFields = [] } = data;
      const { initValue = '', errors = [] } = getObjectPropSafely(
        () => data.destinationInput[SNAPSHOT_RENDER_FIELD] || {},
      );

      const dynamicSnapFields = getObjectPropSafely(() =>
        dynamicFields.filter(each => each !== SNAPSHOT_RENDER_FIELD),
      );

      return (
        <AntsomiLineFieldRender
          key={componentId}
          moduleConfig={props.moduleConfig}
          initValue={initValue}
          errors={errors}
          componentId={componentId}
          activeNode={props.activeNode}
          observerActiveIndex={scrollIndexDebounce}
          callback={callback}
          dynamicFields={dynamicSnapFields}
          destinationInputs={data.destinationInput}
          classes={{
            ...classes,
            isCustomSpace: true,
          }}
          selectionStartIndex={selectionStartIndex}
          variants={data.variants}
          variantExtraData={data.variantExtraData}
          objectWidgetInput={data.objectWidgetInput}
          design={design}
          validateKey={props.validateKey}
          refreshComponentKey={refreshComponentKey}
          isViewMode={isViewMode}
          channelElement={channelCode}
          isNewCustomTag
          onTagRemove={handleTagRemove}
          onChangeExtraDataTagProperty={handleChangeExtraDataTagProperty}
          useAllocatedCode={props.useAllocatedCode}
          journeyNodeTagProperties={journeyNodeTagProperties}
          catalogCode={catalogCode}
          step={step + 1}
          setIsFetchData={setIsFetchData}
          configure={configure}
          setSelectionStartIndex={setSelectionStartIndex}
          isBlastCampaign={isBlastCampaign}
          itemTypeId={itemTypeId}
          destinationSettings={dataDestinationSettings}
          selectedTemplate={selectedTemplateDashboard}
          onChange={dataOut =>
            onChangeDataDesInput(SNAPSHOT_RENDER_FIELD)(dataOut)
          }
          onChangeOthers={onChangeOthersData}
        />
      );
    };

    const renderCompose = step => {
      const gridCol = isShowPreview ? 6 : isShowPreviewObjectWidget ? 8 : 12;
      const widthOfCustomInput = getWidthCustomInput(channelCode, step);
      const hideVariantActions =
        (isBlastCampaign || isDisplayDiagramBlastCampaign) &&
        triggerType === NODE_TYPE.SCHEDULED;

      if (isLoadingCompose) {
        return (
          <Flex
            justify="center"
            align="center"
            flex={1}
            style={{
              minHeight: 200,
            }}
          >
            <Spin spinning />
          </Flex>
        );
      }

      return (
        <DisplayContent
          id="main-compose"
          isShow={step === 1}
          isBlastCampaign={isBlastCampaign}
        >
          <Grid
            container
            // xs={6}
            // xs={12}
            className="pos-relative wrapper-inputs"
            style={{
              display: 'flex',
              alignItems: 'center',
              borderBottom: isBlastCampaign ? 'unset' : '1px solid #e6e6e6',
            }}
          >
            <Grid
              item
              xs={colLeftVariant}
              className={classNames('grid-col-left', {
                [classes.title]: isBlastCampaign,
                // [classes.styledChannel]: isBlastCampaign,
                [classes.adjustCol]: isBlastCampaign,
              })}
            >
              <h3
                className="m-bottom-1 m-top-1 width-100"
                style={
                  isBlastCampaign
                    ? {
                        fontSize: 14,
                        fontWeight: 'bold',
                      }
                    : {}
                }
              >
                {mapTranslateMessage.compose}
              </h3>
              {!hideVariantActions && (
                <Title>{mapTranslateMessage.maximumVariant}</Title>
              )}
            </Grid>
            <Grid
              item
              xs={colRightVariant}
              className={classNames('grid-col-right')}
              style={
                isBlastCampaign
                  ? {
                      flex: '1 1 0',
                    }
                  : {}
              }
            >
              <Grid container>
                <Grid
                  item
                  xs={10}
                  className={classNames({
                    [classes.alignVariantTab]: !isBlastCampaign,
                  })}
                >
                  <VariantTab
                    data={data.variants.list}
                    callback={callbackVariant}
                    activeId={data.variants.activeId}
                    toggleButtonProps={{
                      getIsToggle,
                      handleClick: handleClickToggleStatusVariant,
                    }}
                    quickTestButtonProps={{
                      disabled: !isEnableTesting,
                      handleClick: props.toggleDestinationModalTesting,
                    }}
                    copyButtonProps={{
                      handleClick: handleCopyVariant,
                      disabled: !(
                        data.variants.list.length + MY_SELF_VARIANT <=
                        MAX_VARIANTS
                      ),
                    }}
                    isFetchData={isFetchData}
                    isViewMode={
                      isViewMode || isLineRichMenu || isThirdPartyCampaigns
                    }
                    hideVariantActions={
                      hideVariantActions || isThirdPartyCampaigns
                    }
                  />
                </Grid>
                {!hideVariantActions && (
                  <Grid item xs={2}>
                    <WrapperTextAudiences
                      color="#005eb8"
                      onClick={() => toggleModalAlgorithms(true)}
                    >
                      <IconWrapper>
                        <UIIconXlab name="version-history" />
                      </IconWrapper>
                      {mapTranslateMessage.labelAudiences}
                    </WrapperTextAudiences>
                  </Grid>
                )}
              </Grid>
            </Grid>
            {/* {displayForecast ? <Grid item xs={!isViewMode ? 3 : 6} /> : null} */}
            {/* <VariantName
              value={data.variants.activeInfo.name || ''}
              callback={callbackVariant}
            /> */}
          </Grid>
          <Grid
            container
            style={
              isBlastCampaign
                ? {
                    borderTop: '1px solid #e5e5e5',
                  }
                : {}
            }
          >
            <Grid
              container
              item
              xs={
                gridCol
                //   || colGridWrapper({
                //   isBlastCampaign,
                //   isViewMode,
                //   defaultCol: gridCol,
                // })
              }
              className={classNames(classes.alignContentContainer, {
                [classes.paddingContainer]: !isBlastCampaign,
                [classes.relativePosition]: isSmartInbox,
                'wrapper-hard-fields': isBlastCampaign && !isViewMode,
                // isShowPreview || (isBlastCampaign && isViewMode),
              })}
              data-test="grid-inputs"
            >
              {data.variantHardFields.map(each => (
                <InputComponent
                  componentName={each}
                  {...data[each]}
                  componentKey={`${each}-${data.variants.activeId}`}
                  onChange={onChangeData}
                  onChangeOthers={onChangeOthersData}
                  key={each}
                  classes={classes}
                  colLeft={colLeftVariant}
                  colRight={colRightVariant}
                  isViewMode={isViewMode}
                  isHideQuickTest={isThirdPartyCampaigns}
                  step={step + 1}
                  channelElement={channelCode}
                  catalogCode={catalogCode}
                  toggleDestinationModalTesting={handleToggleDestModalTesting}
                  disabledQuickTest={false}
                  isAdjustCol={!isBlastCampaign}
                  isBlastCampaign={isBlastCampaign}
                  isFullWidth={
                    safeVariantHFSetting(each, 'isFullWidth', false) ||
                    isAntsomiAppPush ||
                    isFirebaseAppPush ||
                    isThirdPartyCampaigns
                  }
                  isTitleAlignLeft={
                    safeVariantHFSetting(each, 'isTitleAlignLeft', false) ||
                    isTitleAlignLeftVariant ||
                    isSmsIMedia ||
                    isZaloIMedia ||
                    isAntsomiLine ||
                    isSmartInbox ||
                    isBlastCampaign ||
                    isZaloOA ||
                    isZaloZNS ||
                    isAntsomiAppPush ||
                    isAeonMallInAppMessage ||
                    isJourneyTemplateMode
                  }
                />
              ))}
              {/* Custom Input Variant */}

              {data.variants.activeId &&
                data.variants.cacheInfo[data.variants.activeId]
                  .customInputVia &&
                data.variants.cacheInfo[data.variants.activeId].customInputVia
                  .workspaces.length > 0 && (
                  <Grid
                    container
                    style={{
                      paddingTop: '0.8rem',
                    }}
                  >
                    <Grid
                      item
                      xs={colLeftVariant}
                      className={classNames('grid-col-left', {
                        [classes.adjustCol]: isBlastCampaign && !isViewMode,
                      })}
                    >
                      <WrapperTitleCustomInput
                        isBlastCampaign={
                          isBlastCampaign || isJourneyTemplateMode
                        }
                        style={{
                          alignItems:
                            safeVariantHFSetting(
                              'variantName',
                              'isTitleAlignLeft',
                              false,
                            ) ||
                            isSmartInbox ||
                            isAntsomiLine ||
                            isAntsomiAppPush ||
                            isZaloOA ||
                            isBlastCampaign ||
                            isTitleAlignLeftVariant ||
                            isJourneyTemplateMode
                              ? 'flex-start'
                              : 'flex-end',
                        }}
                        isTitleAlignLeft={
                          isSmsIMedia || isZaloIMedia || isTitleAlignLeftVariant
                        }
                      >
                        <Title>Custom Input</Title>
                      </WrapperTitleCustomInput>
                    </Grid>

                    <Grid
                      item
                      xs={colRightVariant}
                      className={classNames('grid-col-right', {
                        [classes.adjustCol]: isBlastCampaign && !isViewMode,
                      })}
                    >
                      {data.variants.cacheInfo[
                        data.variants.activeId
                      ].customInputVia.workspaces.map(row => (
                        <Grid
                          container
                          key={row.id}
                          spacing={3}
                          style={{
                            paddingBottom: '8px',
                          }}
                        >
                          {row.sections.map(cell => (
                            <Grid item xs key={cell.id}>
                              <Title className={props.classNameTitle}>
                                {cell.properties.label}
                                {!!cell.properties.isRequired && `*`}
                              </Title>
                              <InputComponent
                                {...cell}
                                options={cell.properties.options}
                                onChange={value => {
                                  onChangeDataCustomInputVia({
                                    value,
                                    type: 'variant',
                                    workspaces: _.cloneDeep(
                                      data.variants.cacheInfo[
                                        data.variants.activeId
                                      ].customInputVia.workspaces,
                                    ),
                                  });
                                  onChangeDataCustomInputVia({
                                    value,
                                    type: 'variant',
                                    workspaces: _.cloneDeep(
                                      data.variants.cacheInfo[
                                        data.variants.activeId
                                      ].customInputVia.workspaces,
                                    ),
                                  });
                                }}
                                value={cell.properties.value}
                                infor={{
                                  rowId: row.id,
                                  cellId: cell.id,
                                }}
                                classes={{
                                  item: '',
                                }}
                                isViewMode={isViewMode}
                                widthOfCustomInput={widthOfCustomInput}
                                isAdjustCol={!isBlastCampaign}
                                isFullWidth={isBlastCampaign}
                                isBlastCampaign={isBlastCampaign}
                                isTitleAlignLeft={
                                  isBlastCampaign || isJourneyTemplateMode
                                }
                              />
                            </Grid>
                          ))}
                        </Grid>
                      ))}
                    </Grid>
                  </Grid>
                )}
              {isAntsomiLine ? (
                renderAntsomiLineConfigFields({
                  step,
                })
              ) : (
                <>
                  {renderNormalComposeFields({
                    step,
                  })}
                  {renderDynamicFieldsCompose({
                    step,
                    type: 'GROUPS',
                    isRenderLayoutTabs: isSmartInbox,
                    isRenderLayoutGroupPanel: !isSmartInbox,
                  })}
                </>
              )}
            </Grid>
            {isShowPreview && step === 1 && (
              <Grid
                item
                xs={6}
                className={`${classes.preview} ${
                  isSmartInbox ? 'pl-4' : 'p-x-4'
                }`}
                style={{
                  padding: isSmartInbox ? '0px 0px 0px 16px' : '0px 16px',
                }}
              >
                <div className={classes.maxHeight100}>
                  <div className="m-top-3">
                    <span
                      className={classNames(
                        'm-left-1 no-wrap',
                        classes.titlePreview,
                      )}
                    >
                      {mapTranslateMessage.labelActionPreview}
                    </span>
                  </div>

                  <Preview
                    data={previewData}
                    dataRealtime={data.destinationInput}
                    dynamicFields={data?.dynamicFields}
                    dataSmartInbox={{
                      webviewSettings: !_isEmpty(smartInboxConfigs)
                        ? smartInboxConfigs.webviewSettings
                        : {},
                      inputSettings: data.destinationInput || {},
                      catalogs: getObjectPropSafely(
                        () => data.destinationInput.inboxCatalogs.value,
                        [],
                      ),
                    }}
                    variantActiveId={data?.variants?.activeId}
                    isRecommendation={isRecommendation}
                    selectedTemplate={selectedTemplate}
                    deviceType={activeTabDisplay}
                    observeTypeDevicePreview={observeDeviceType}
                    setObserverIndex={setScrollIndexView}
                    channelCode={channelCode || initData.channelCode}
                    isViewMode={isViewMode}
                    isBlastCampaign={isBlastCampaign}
                    catalogCode={catalogCode}
                    destinationId={
                      data.destinationId ||
                      get(data, 'workflowDestination.value.destinationId', '')
                    }
                    // onChangeThumbnail={thumbnail => {
                    //   setData(draft => {
                    //     draft.variants.cacheInfo[draft.variants.activeId] = {
                    //       ...draft.variants.cacheInfo[draft.variants.activeId],
                    //       thumbnail,
                    //     };
                    //   });
                    // }}
                  />
                </div>
              </Grid>
            )}
            {!isShowPreview && isShowPreviewObjectWidget && (
              <PreviewObjectWidget objectWidgetInput={data.objectWidgetInput} />
            )}
          </Grid>
        </DisplayContent>
      );
    };

    return (
      <ErrorBoundary path="app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/index.jsx">
        <Grid container item xs={12}>
          {!isBlastCampaign &&
          !isJourneyTemplateMode && ( // hidden when isBlastCampaign
              // <StyledStepper nonLinear activeStep={activeStep}>
              //   {steps.map((label, index) => (
              //     <Step key={label} className={index === 0 ? 'p-left-0' : ''}>
              //       <StyledStepButton
              //         onClick={() => handleStep(index)}
              //         disableRipple
              //         disableTouchRipple
              //         isError={
              //           (index === 0 &&
              //             !isValidateFields({
              //               fields: data.defaultFields,
              //               data,
              //             })) ||
              //           (index === 1 &&
              //             !isValidateFields({
              //               fields: data.dynamicFields,
              //               data: data.destinationInput,
              //             })) ||
              //           !isValidateWithCondition({
              //             step: index,
              //             catalogCode: activeNode.catalogCode,
              //             data: data.destinationInput,
              //           })
              //         }
              //       >
              //         {label}
              //       </StyledStepButton>
              //     </Step>
              //   ))}
              // </StyledStepper>
              <Steps
                current={activeStep}
                items={[
                  {
                    title: mapTranslateMessage.settings,
                    key: 0,
                    ...(!isValidateFields({
                      fields: data.defaultFields,
                      data,
                    }) && {
                      status: 'error',
                    }),
                  },
                  {
                    title: mapTranslateMessage.compose,
                    key: 1,
                    ...(!isValidateFields({
                      fields: data.dynamicFields,
                      data: data.destinationInput,
                    }) && {
                      status: 'error',
                    }),
                  },
                ]}
                style={{
                  width: '350px',
                  paddingBottom: 20,
                }}
                onChange={handleStep}
              />
            )}
          {isJourneyTemplateMode ? (
            <>
              <DividerDash />
              {renderCompose(1)}
            </>
          ) : (
            <>
              {renderSettings(isBlastCampaign ? 0 : activeStep)}
              {isBlastCampaign && <Divider />}
              {renderCompose(isBlastCampaign ? 1 : activeStep)}
            </>
          )}
          {isChannelWebPersonalization && zoneId && zoneRenderType ? (
            <WebIframe
              storyId={testState.data.storyId}
              itemTypeId={itemTypeId}
              callback={callback}
              toggleModal={toggleModalTesting}
              insightPropertyId={insightPropertyId}
              variantExtraData={variantExtraData}
              isOpenModal={testState.isOpenModal}
              zoneId={zoneId}
              zoneRenderType={zoneRenderType}
            />
          ) : (
            testState.isOpenModal && (
              <Testing
                initData={testState.data}
                toggleModal={toggleModalTesting}
                isOpenModal={testState.isOpenModal}
                callback={callback}
                nodeDetail={detailJourney}
                configure={configure}
                triggerType={props.triggerType}
                isBlastCampaign={isBlastCampaign}
                channelCode={channelCode}
                insightPropertyId={insightPropertyId}
                variantExtraData={variantExtraData}
              />
            )
          )}
          <ModalAlgorithms
            toggle={value => toggleModalAlgorithms(value)}
            isOpen={audiencesState.isOpenModal}
            callback={callback}
            data={audiencesState.data.list}
            isViewMode={isViewMode}
          />
        </Grid>
      </ErrorBoundary>
    );
  };

  return (
    <RootContainer className={classes.root}>
      <Grid
        container
        className={classes.padding}
        style={{
          padding: !isBlastCampaign ? '15px 20px 0' : 'unset',
        }}
      >
        <Loading isLoading={isLoading} />
        {renderContent()}
      </Grid>
    </RootContainer>
  );
}

Destination.propTypes = {
  onChange: PropTypes.func,
  callback: PropTypes.func,
};
Destination.defaultProps = {
  onChange: () => {},
  callback: () => {},
};

const mapStateToProps = createStructuredSelector({
  isOpenModalDestinationTesting: makeSelectOpenModalDestinationTesting(),
  main: makeSelectMainCreateWorkflow(),
  mainReducer: makeSelectMainReducerCreateWorkflow(),
  configure: makeSelectConfigureCreateWorkflow(),
  creatingJourneyInfo: makeSelectCreatingJourneyInfo(),
  thirdPartyCampaigns: makeSelectThirdPartyCampaignInfo(),
  useAllocatedCode: makeSelectUseAllocatedCodeStatus(),
  detailJourney: (state, props) => {
    const isBlastCampaign = getObjectPropSafely(
      () => props.isBlastCampaign,
      false,
    );

    return makeSelectStoryDetailDomainMain()(state, {
      moduleConfig: {
        key: getPrefixDetail(
          props.moduleConfig.key,
          isBlastCampaign ? -13 : -7,
        ),
      },
    });
  },
  versionId: (state, props) => {
    const isBlastCampaign = getObjectPropSafely(
      () => props.isBlastCampaign,
      false,
    );

    return makeSelectStoryDetailVersionId()(state, {
      moduleConfig: {
        key: getPrefixDetail(
          props.moduleConfig.key,
          isBlastCampaign ? -13 : -7,
        ),
      },
    });
  },
  preview: makeSelectSegmentPreview(),
});

function mapDispatchToProps(dispatch, props) {
  const prefix = props.moduleConfig.key;
  return {
    toggleDestinationModalTesting: value =>
      dispatch(
        updateValue(`${prefix}@@DESTINATION_TOGGLE_MODAL_TESTING@@`, value),
      ),
    updateEnabledButtonTesting: value =>
      dispatch(
        updateValue(`${prefix}@@UPDATE_ENABLED_BUTTON_TESTING@@`, value),
      ),
    onChangeActiveNode: value =>
      dispatch(updateValue(`${prefix}@@ACTIVE_NODE@@`, value)),
    onUpdateAmountDestination: value =>
      dispatch(updateValue(`${prefix}@@UPDATE_AMOUNT_DESTINATIONS@@`, value)),
    onChangeCreatingJourneyInfo: value =>
      dispatch(
        updateValue(
          `${JOURNEY_MODULE_CONFIG.key}@@CREATING_JOURNEY_INFO`,
          value,
        ),
      ),
    updateIsOpenSubDrawer: value => {
      dispatch(
        updateValue(`${JOURNEY_MODULE_CONFIG.key}@@IS_OPEN_SUB_DRAWER`, value),
      );
    },
    updateFullScreen: value => {
      dispatch(
        updateValue(
          `${JOURNEY_MODULE_CONFIG.key}@@TOGGLE_FULL_SCREEN@@`,
          value,
        ),
      );
    },
    updateSyncExtraDataProperties: newSyncInfo => {
      dispatch(
        updateValue(
          `${prefix}@@UPDATE_SYNC_EXTRA_DATA_PROPERTIES@@`,
          newSyncInfo,
        ),
      );
    },
    onToggleButtonSaveChange: newStatus => {
      dispatch(updateValue(`${prefix}@@DISABLED_SAVE_CHANGE@@`, newStatus));
    },
    onRemoveTagProperty: payload => {
      dispatch(remove(`${prefix}@@REMOVE_TAG_PROPERTY`, payload));
    },
    onMergeListTagProperties: payload => {
      dispatch(
        updateValue(`${prefix}@@MERGE_LIST_TAG_PROPERTIES_NODE@@`, payload),
      );
    },
    onRemoveTagByIds: payload => {
      dispatch(remove(`${prefix}@@BULK_REMOVE_TAG_PROPERTIES@@`, payload));
    },
  };
}

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(Destination);

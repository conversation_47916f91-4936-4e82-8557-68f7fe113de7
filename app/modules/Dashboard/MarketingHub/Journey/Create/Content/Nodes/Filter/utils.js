/* eslint-disable no-else-return */
import { Map } from 'immutable';

import {
  FILTER_TYPE,
  toAPINodeFilter,
  toUINodeFilter,
} from '../../../../../../../../components/common/UINodeFilter/utils';
import { validateRulesAudienceAttributes } from '../../../../../../../../components/common/UINodeFilter/_UI/AudienceAttributes/utils';
import { validateRulesEventAttributes } from '../../../../../../../../components/common/UINodeFilter/_UI/EventAttributes/utils';
import { validateDataAudienceSegment } from '../../../../../../../../components/Templates/TargetAudience/utils';
import { safeParse } from '../../../../../../../../utils/common';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';
import { isEmpty } from 'lodash';

export function toNodeFilterAPI(nodeInfo) {
  const dataNodeFilter = nodeInfo.get('filter');
  return toAPINodeFilter(dataNodeFilter);
}

export const validateNodeFilter = data => {
  try {
    if (data && typeof data.get === 'function') {
      const dataNodeFilter = safeParse(data.get('filter'), {});
      const { filterType = {} } = dataNodeFilter;

      if (isEmpty(filterType)) {
        return { status: false };
      }

      if (
        filterType.value === FILTER_TYPE.CUSTOMER_ATTRIBUTE ||
        filterType.value === FILTER_TYPE.VISITOR_ATTRIBUTE
      ) {
        const dataNodeAudienceAttrs = dataNodeFilter.user_attributes;
        return validateRulesAudienceAttributes(dataNodeAudienceAttrs.rules);
      } else if (filterType.value === FILTER_TYPE.TARGET_AUDIENCE) {
        return validateDataAudienceSegment(dataNodeFilter[filterType.value]);
      } else if (filterType.value === FILTER_TYPE.PERFORMED_EVENT) {
        const dataNodeEventAttributes = dataNodeFilter[filterType.value];
        return validateRulesEventAttributes(dataNodeEventAttributes.rules);
      }
    }
  } catch (err) {
    addMessageToQueue({
      path:
        'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Filter/utils.js',
      func: 'validateNodeFilter',
      data: err.stack,
    });
    // console.log(err);
  }
  return { status: true };
  // const res = validateDataAudienceSegment(dataTargetAudience);
  // return res;
};

export function toNodeFilterUI(nodeInfo) {
  const data = Map({
    filter: toUINodeFilter(nodeInfo),
  });
  return data;
}

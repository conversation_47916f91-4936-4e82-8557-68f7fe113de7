/* eslint-disable no-nested-ternary */
/* eslint-disable no-restricted-syntax */
/* eslint-disable dot-notation */
/* eslint-disable indent */
/* eslint-disable eqeqeq */
/* eslint-disable no-param-reassign */
import { useEffect, useCallback, useRef } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _, { get, has, isArray, isFunction, set, toString } from 'lodash';
import { useImmer } from 'use-immer';
import { isMap as isImmutableMap, Map as ImmutableMap } from 'immutable';
import JourneyServices from '../../../../../../../../services/Journey';
import UserAttributesServices from '../../../../../../../../services/UserAttributes';

import { useCancellablePromise } from '../../../../../../../../utils/web/useHooks';
import {
  mapDestinationToUI,
  mapDynamicDataToUI,
  listToMap,
  codeItemToValue,
  mapCampaignSettingToFe,
  dataEntrieToList,
  initDefaultValueByChannelCode,
  handleFetchInfoDestination,
  injectInputConfigHardForAPIByChannel,
} from './utils';
import {
  adjustDynamicDataIfRelatedChange,
  collectVariantsTagProperties,
  initDefaultData,
  initVariants,
  MAP_TRANSLATE,
  reInitVariantExtraData,
  sanitizeInitDataVariantByCopyCampaign,
} from './utils.state';
import { addMessageToQueue } from '../../../../../../../../utils/web/queue';
import { safeParse, safeParseInt } from '../../../../../../../../utils/common';
import { DATA_VARIANT_WEB_CHANNEL_HARD } from './variant.config';
import { serializeDataInputAttrs } from '../utils';
import {
  EMAIL_TEMPLATE,
  EXTRA_DATA_FIELD,
  JSON_TEMPLATE,
  MEDIA_TEMPLATE,
} from '../../../../../../../../components/common/UIEditorPersonalization/utils';
import { mapCustomInputToFe } from '../TriggerEventBased/utils';
import { DESTINATION_TYPE } from './constants';
import TRANSLATE_KEY from '../../../../../../../../messages/constant';
import { getTranslateMessage } from '../../../../../../../../containers/Translate/util';
import { getObjectPropSafely } from '../../../../../../../../components/common/UIEditorPersonalization/utils.3rd';
import { toUINodeFilter } from '../../../BlastCampaign/components/UINodeFilter/utils';
import { CATALOG_CODE, NODE_TYPE } from '../constant';
import { getCurrentOwnerId } from '../../../../../../../../utils/web/cookie';
import {
  adjustWorkflowDestinationByTemplate,
  dataVariantHardUpdateInputs,
  setDynamicTemplateByCatalog,
} from './utils.variant';
// components/common/UIEditorPersonalization/utils

export const useFetchInfoData = props => {
  const {
    ServiceFn,
    initData,
    activeNode,
    ServiceOptionsFn,
    itemTypeId,
    eventValue,
    triggerType,
    isUseTemplateCampaign,
    isUseTemplateCampaignSameJr,
    ServiceCustomInput,
    selectedDesign,
    design,
    actionNode,
    versionId,
    onMergeListTagProperties,
    destinationInfo = {},
    extendListDestFilterFn = () => {},
  } = props;
  const { isBlastCampaign, current } = destinationInfo;
  const errDestinationDisabled = getTranslateMessage(
    TRANSLATE_KEY._ERROR_JOURNEY_DES_DISABLE,
    'Destination is disabled',
  );
  const errDestinationRemoved = getTranslateMessage(
    TRANSLATE_KEY._ERROR_JOURNEY_DES_REMOVE,
    'Destination does not exist',
  );

  const errDestinationNoPermission = getTranslateMessage(
    TRANSLATE_KEY._JOURNEY_DESTINATION_ERR,
    `You don't have permission to use this destination anymore`,
  );

  const [infoData, setInfoData] = useImmer({
    mapDestination: new Map(),
    isLoading: true,
    isLoadingCompose: false,
  });

  // const [dynamicData, setData] = useImmer({ basicSettings: [] });
  const [data, setData] = useImmer(initDefaultData());
  const { channelCode, catalogCode } = activeNode;
  const whatsappMsg = catalogCode === CATALOG_CODE.INFOBIP_WHATSAPP_TEMPLATE;
  const isColorText = whatsappMsg;
  // console.log('activeNode', activeNode)
  const { cancellablePromise } = useCancellablePromise();
  const nodeIdRef = useRef(activeNode.nodeId);

  // let isAppendContentWebChannel = false;
  // isAppendContentWebChannel = catalogCode === 'web_embedded';
  const isAppendContentWebChannel = channelCode === 'web_personalization';

  const fetchDataAPI = useCallback(() => {
    const params = {
      objectId: activeNode.value,
      _owner_id: getCurrentOwnerId(),
    };
    setInfoData(draft => {
      draft.isLoading = true;
    });

    const {
      role,
      lookup = {},
      isFetchInfoData = false,
      destinationId,
      // variantId,
      // variantIds,
      testingAudienceIds = [],
      isUseTemplate = false,
    } = initData;

    // console.log('initData', initData);
    // setData(() => initDefaultData());
    // let resFullConfig = {};
    const mapAllOptions = {};
    let cacheRawDataInputConfigs = {}; // USE ONLY TO READ
    const mapSourceRefToname = {};
    const selectDynamicFields = [];

    let campaignSetting = {};
    let campaignSettingTmp = {};
    let destinationInput = {};
    let objectWidgetInput = {};
    let variantExtraData = {};
    let campaignName = 'Campaign 1';
    let campaignId = '';
    let variantName = '';
    let initDataVariants = {};
    let { variantIds } = initData;
    let status = true; // int status = status campaign
    let campaignCustomInputs = {
      templateCustomInput: {},
      workspaces: [],
      customInputs: {},
    };

    // const dataWorkspaces = [];
    if (role === 'RESET') {
      variantIds = [];
      campaignId = null;
    }
    // console.log(isFetchInfoData);
    if (isFetchInfoData) {
      const initDataUpdate = initData.data;
      setData(
        () =>
          // Object.keys(initDataUpdate.destinationInput).forEach(key => {
          //   initDataUpdate.destinationInput[key].initValue =
          //     initDataUpdate.destinationInput[key].value;
          // });
          initDataUpdate,
      );
      setData(draft => {
        if (!destinationInfo.isBlastCampaign) {
          draft.variantName.activeId = initData.variants.activeId;
        }
        // draft.isInitDone = true;
        Object.keys(initDataUpdate.destinationInput).forEach(key => {
          draft.destinationInput[key].initValue =
            initDataUpdate.destinationInput[key].value;
        });

        draft.triggerFetchContent = 0;
      });
      setInfoData(draft => {
        draft.isLoading = false;
      });
    } else {
      // if (channelCode === 'web_personalization') {

      if (isAppendContentWebChannel) {
        // init element for web
        const dynamicDataWeb = mapDynamicDataToUI(
          DATA_VARIANT_WEB_CHANNEL_HARD.basicInputs,
          DATA_VARIANT_WEB_CHANNEL_HARD.inputs,
          initData, // initData sẽ là data để lookup value
          itemTypeId,
          eventValue,
          triggerType,
          activeNode,
        );

        setData(draft => {
          // create element for web : zoneId, contentPlacement
          dynamicDataWeb.dynamicFields.forEach(each => {
            draft[each] = dynamicDataWeb.destinationInput[each];
          });

          // set field for render only
          if (catalogCode === 'web_embedded') {
            const webChannelFields = [
              'zoneId',
              'contentPlacement',
              'isFitContent',
              'priority',
            ];

            if (isBlastCampaign) {
              webChannelFields.splice(2, 1);
            }

            draft.webChannelFields = webChannelFields;
          } else {
            draft.webChannelFields = ['priority'];
          }
        });
      }

      cancellablePromise(
        versionId
          ? JourneyServices.versionHistory.getCampaigns({
              data: {
                version_id: +versionId,
                campaign_ids: [
                  role === 'RESET'
                    ? safeParseInt(lookup.campaignId, 0)
                    : safeParseInt(initData.campaignId, 0),
                ],
                columns: [
                  'campaign_id',
                  'campaign_name',
                  'status',
                  'campaign_setting',
                  'custom_inputs',
                ],
              },
            })
          : handleFetchInfoDestination({
              type: 'CAMPAIGN',
              serviceFn: JourneyServices.campaigns.getByIds,
              initData,
            }),
      )
        .then(resCam => {
          // console.log(resCam);

          if (activeNode.nodeId !== nodeIdRef.current) {
            return null;
          }

          const campaignInfo = resCam.data.length > 0 ? resCam.data[0] : {};

          campaignCustomInputs = {
            ...campaignCustomInputs,
            customInputs: campaignInfo.custom_inputs,
          };
          // console.log('Then 1 => campaignInfo', campaignInfo);

          let otherCampaignName = '';

          const tempName =
            toString(destinationInfo?.current?.campaignName) || '';

          if (destinationInfo?.isBlastCampaign && design === 'create') {
            otherCampaignName = `Campaign ${destinationInfo.amount}`;

            if (tempName) {
              // For case create copy a blast campaign
              otherCampaignName = tempName;
            }
          } else if (destinationInfo?.isBlastCampaign === false) {
            const { flattenNodes = [], disabledIncreAmount, onUpdateAmount } =
              destinationInfo || {};

            const totalCampaigns = flattenNodes.filter(
              node => node.type === NODE_TYPE.DESTINATION,
            ).length;

            if (!disabledIncreAmount && isFunction(onUpdateAmount)) {
              onUpdateAmount({
                amount: totalCampaigns,
                disabledIncreAmount: true,
              });

              otherCampaignName = `Campaign ${totalCampaigns}`;
            } else {
              otherCampaignName = `Campaign ${destinationInfo.amount}`;
            }
          } else {
            otherCampaignName = tempName;
          }

          campaignName = toString(
            campaignInfo.campaign_name || otherCampaignName,
          );

          if (initData.isCopy) {
            campaignName = toString(otherCampaignName);

            if (initData.isCopyNode && role === 'RESET') {
              campaignName = toString(initData.campaignName);
            }
          }

          campaignId =
            role === 'RESET' ? null : campaignInfo.campaign_id || null;
          status = isUseTemplateCampaign
            ? 1
            : safeParseInt(campaignInfo.status, 0);
          campaignSettingTmp = { ...campaignInfo.campaign_setting };
          if (campaignSettingTmp.deliveryTimeConfig) {
            setData(draft => {
              draft.timeTarget = {
                triggerType: campaignSettingTmp.deliveryTimeConfig.type,
                dataSelected: campaignSettingTmp.deliveryTimeConfig.range,
                errors: [],
                hoursType: campaignSettingTmp.deliveryTimeConfig.mode,
              };
            });
          }
          const variants = versionId
            ? JourneyServices.versionHistory.getVariants({
                data: {
                  version_id: +versionId,
                  variant_ids:
                    role === 'RESET' ? lookup.variantIds : variantIds,
                  columns: [
                    'variant_id',
                    'variant_name',
                    'content_setting',
                    'status',
                    'custom_inputs',
                  ],
                },
              })
            : handleFetchInfoDestination({
                type: 'VARIANT',
                serviceFn: JourneyServices.variant.getByIds,
                initData,
              });

          return variants;
        })
        .then(resVariant => {
          if (activeNode.nodeId !== nodeIdRef.current) {
            return null;
          }
          // console.log('resVariant.data', resVariant.data);
          console.log(resVariant);
          initDataVariants = initVariants(role, resVariant.data, '---');
          console.log(initDataVariants);

          // In case: Create with Load existing by campaign
          if (isUseTemplateCampaign && role === 'RESET' && activeNode?.nodeId) {
            let newNodeTags = null;

            // If create a copy campaign in same journey
            if (isUseTemplateCampaignSameJr) {
              const {
                newVariants,
                newNodeTagProperties,
              } = sanitizeInitDataVariantByCopyCampaign(
                initDataVariants,
                initData?.journeyProperties ?? ImmutableMap({}),
                {
                  oldNodeId: initData?.oldNodeId,
                  newNodeId: activeNode.nodeId,
                },
              );

              initDataVariants = newVariants;
              newNodeTags = newNodeTagProperties;
            } else {
              newNodeTags = collectVariantsTagProperties({
                initData,
                list: initDataVariants?.list,
                map: initDataVariants?.cacheInfo,
              });
            }

            if (newNodeTags && _.isFunction(onMergeListTagProperties)) {
              onMergeListTagProperties({
                nodeId: activeNode.nodeId,
                tags: newNodeTags,
              });
            }
          }

          campaignSetting = mapCampaignSettingToFe({
            variant: initDataVariants,
            campaign: campaignSettingTmp,
            role,
          });
          setData(draft => {
            // set for using validate duplicate variant name
            draft.variantName.mapVariantName = initDataVariants.mapVariantName;
            draft.variantName.activeId = initDataVariants.activeId;
          });
          // console.log('Then 1.1 => initDataVariants', initDataVariants);
          variantName = initDataVariants.initVariantName;

          // xử lý cho case destination dùng channel email, chọn use template
          const paramsIds = [];
          Object.keys(initDataVariants.cacheInfo).forEach(key => {
            const tmpVariantExtraData = safeParse(
              initDataVariants.cacheInfo[key].variantExtraData,
              {},
            );
            // console.log('tmpVariantExtraData', tmpVariantExtraData);

            Object.keys(tmpVariantExtraData).forEach(key1 => {
              const tmp = tmpVariantExtraData[key1];
              // console.log('tmp', tmp);
              if (tmp.design === 'template' && tmp.fe_config_id) {
                paramsIds.push(tmp.fe_config_id);
              }
            });
            // console.log('paramsIds', paramsIds);
          });
          if (paramsIds.length === 0) {
            return { data: [] };
          }
          return UserAttributesServices.settings.get(paramsIds.toString());
        })
        .then(resFeConfigVariant => {
          if (activeNode.nodeId !== nodeIdRef.current) {
            return null;
          }
          Object.keys(initDataVariants.cacheInfo).forEach(key => {
            const tmpVariantExtraData = safeParse(
              initDataVariants.cacheInfo[key].variantExtraData,
              {},
            );
            // console.log('tmpVariantExtraData', tmpVariantExtraData);

            Object.keys(tmpVariantExtraData).forEach(key1 => {
              const tmp = tmpVariantExtraData[key1];
              // console.log('tmp', tmp);
              if (tmp.design === 'template' && tmp.fe_config_id) {
                const tmpFind = resFeConfigVariant.data.find(
                  item => item.settingId == tmp.fe_config_id,
                );
                if (tmpFind) {
                  tmpVariantExtraData[key1].emailConfig = tmpFind.properties;
                }
                initDataVariants.cacheInfo[
                  key
                ].variantExtraData = tmpVariantExtraData;
              }
            });
          });

          const isMediaTemplateVariant =
            _.get(
              initDataVariants,
              `cacheInfo[${initDataVariants.activeId}].variantExtraData.${
                EXTRA_DATA_FIELD[MEDIA_TEMPLATE]
              }.type`,
              '',
            ) === MEDIA_TEMPLATE;

          const isEmailTemplateVariant =
            _.get(
              initDataVariants,
              `cacheInfo[${initDataVariants.activeId}].variantExtraData.${
                EXTRA_DATA_FIELD[EMAIL_TEMPLATE]
              }.design`,
              '',
            ) === EMAIL_TEMPLATE;

          if (
            has(initData, 'isCopyNode') &&
            initData.isCopyNode &&
            (isEmailTemplateVariant || isMediaTemplateVariant)
          ) {
            const cacheInfoVariantList = getObjectPropSafely(
              () => initDataVariants.cacheInfo,
              {},
            );

            const variantField =
              EXTRA_DATA_FIELD[
                isMediaTemplateVariant ? MEDIA_TEMPLATE : EMAIL_TEMPLATE
              ];

            Object.keys(cacheInfoVariantList || {}).forEach(variantKey => {
              const feConfigId = get(cacheInfoVariantList[variantKey], [
                'variantExtraData',
                variantField,
                'fe_config_id',
              ]);
              set(
                cacheInfoVariantList[variantKey],
                ['variantExtraData', variantField, 'copy_id'],
                feConfigId,
              );
            });
          }

          const paramsCustomInput = {
            itemTypeId: -1011,
          };
          return ServiceCustomInput(paramsCustomInput);
        })
        .then(resCustomInputVariant => {
          if (activeNode.nodeId !== nodeIdRef.current) {
            return null;
          }
          let templateWorkspaces = [];
          const { dataOut, customInputAttributes } = serializeDataInputAttrs({
            customInputAttributes:
              resCustomInputVariant.data[0].customInputAttributes,
            positions: resCustomInputVariant.data[0].positions,
          });

          Object.keys(initDataVariants.cacheInfo).forEach(key => {
            const workspaces = _.cloneDeep(dataOut);
            initDataVariants.cacheInfo[key].customInputVia = {
              workspaces: _.cloneDeep(
                mapCustomInputToFe(
                  initDataVariants.cacheInfo[key].customInputs,
                  workspaces,
                ),
              ),
            };
            templateWorkspaces = _.cloneDeep(workspaces);
          });
          setData(draft => {
            draft.variants = initDataVariants;
            draft.mixFields = ['workspaces'];
            draft.templateWorkspaces = _.cloneDeep(templateWorkspaces);
            draft.customInputAttributes = customInputAttributes;
          });
          return ServiceFn(params);
        })
        .then(res => {
          if (activeNode.nodeId !== nodeIdRef.current) {
            return null;
          }
          const paramsOptions = {
            data: {
              // filters: {
              //   OR: [
              //     {
              //       AND: [
              //         {
              //           column: 'app_defined',
              //           data_type: 'number',
              //           operator: 'equals',
              //           value: 0,
              //         },
              //       ],
              //     },
              //   ],
              // },
            },
          };
          // const paramsOptions = {};
          if (res.code === 200 && isArray(res.data)) {
            // if (res.code === 200) {
            const { isBlastCampaign, configComponent = {} } = destinationInfo;
            const {
              meta: { catalogInfo },
            } = res;
            let resData = res.data || {};
            if (isFunction(extendListDestFilterFn)) {
              resData = extendListDestFilterFn(resData);
            }
            const mapDestination = mapDestinationToUI(resData);

            // case destination revoked
            if (!mapDestination.get(`${destinationId}`) && destinationId) {
              JourneyServices.data
                .getNameDestinationById({
                  destinationId,
                })
                .then(resDestinationDetail => {
                  const resDestination = resDestinationDetail.data;

                  const newData = mapDestinationToUI([
                    ...res.data,
                    ...resDestination,
                  ]);

                  const valueDestination = newData.get(`${destinationId}`);
                  setData(draft => {
                    draft.workflowDestination.value = valueDestination;
                    draft.workflowDestination.errors = [
                      errDestinationNoPermission,
                    ];
                    draft.workflowDestination.arrDestinationNoPermission = resDestination;
                  });
                });
            }
            // console.log('mapDestination', mapDestination);

            if (isBlastCampaign) {
              setData(draft => {
                draft.cachedWorkflowDestinationInfo = resData;
              });
            }

            setInfoData(draft => {
              draft.mapDestination = mapDestination;
            });

            const { value, channelId } = activeNode;
            // console.log('activeNode', activeNode);
            const firstEntry = isImmutableMap(mapDestination)
              ? mapDestination.entries().next().value
              : null;
            const firstDest = firstEntry ? firstEntry[1] : {};

            let workflowDestinationValue = destinationId
              ? mapDestination.get(`${destinationId}`) ?? {}
              : firstDest;

            const preSelectTemplateCatalogs = [
              CATALOG_CODE.LINE,
              CATALOG_CODE.ANTSOMI_APP_PUSH,
              CATALOG_CODE.ZALO_OA,
            ];

            // Pre-select destination by selected template
            const isAdjustWorkflowDest =
              mapDestination &&
              workflowDestinationValue &&
              preSelectTemplateCatalogs.includes(catalogCode);

            if (isAdjustWorkflowDest) {
              const adjustedWorkflowDest = adjustWorkflowDestinationByTemplate({
                mapDestination,
                destinationInfo,
                currentWorkflowDestination: workflowDestinationValue,
              });

              if (adjustedWorkflowDest) {
                workflowDestinationValue = adjustedWorkflowDest;
              }
            }

            if (
              isBlastCampaign &&
              get(
                configComponent,
                'updateComponent.workflowDestination.options',
                [],
              ).length
            ) {
              const options = get(
                configComponent,
                'updateComponent.workflowDestination.options',
                [],
              );
              const destination = options.find(
                option =>
                  option.catalogId === workflowDestinationValue.catalogId,
              );

              if (destination && destination.destinationName) {
                workflowDestinationValue.destinationName =
                  destination.destinationName;
              }
            }
            const { destinationSetting = {} } = workflowDestinationValue;
            // console.log(workflowDestinationValue);
            const { method = 'send' } = destinationSetting;
            // debugger;
            const {
              catalogInput: { inputConfig },
              channelCode: destChannelCode,
              catalogCode: destCatalogCode,
            } = catalogInfo[0];

            /*
             * NOTE: inject hard some fields for a little channel
             */
            injectInputConfigHardForAPIByChannel({
              inputConfig,
              channelCode: destChannelCode,
              catalogCode: destCatalogCode,
            });

            if (['smart_inbox'].includes(destChannelCode)) {
              const initValues = initDefaultValueByChannelCode(destChannelCode);

              const inputs = _.get(inputConfig, 'send.inputs', {});

              for (const key of Object.keys(inputs)) {
                if (initValues[key]) {
                  Object.assign(inputConfig.send.inputs[key], initValues[key]);
                }
              }
            }

            const isMediaTemplateVariant =
              _.get(
                initDataVariants,
                `cacheInfo[${initDataVariants.activeId}].variantExtraData.${
                  EXTRA_DATA_FIELD[MEDIA_TEMPLATE]
                }.type`,
                '',
              ) === MEDIA_TEMPLATE;

            const isEmailTemplateVariant =
              _.get(
                initDataVariants,
                `cacheInfo[${initDataVariants.activeId}].variantExtraData.${
                  EXTRA_DATA_FIELD[EMAIL_TEMPLATE]
                }.design`,
                '',
              ) === EMAIL_TEMPLATE;
            const isJsonTemplateVariant =
              _.get(
                initDataVariants,
                `cacheInfo[${
                  initDataVariants.activeId
                }].variantExtraData.content.type`,
                '',
              ) === JSON_TEMPLATE;

            const templateDesign = _.get(
              initDataVariants,
              `cacheInfo[${
                initDataVariants.activeId
              }].variantExtraData.content.type`,
              '',
            );

            setData(draft => {
              draft.templateDesign = templateDesign;
            });
            // Hard mediaTemplate fields
            if (
              // selectedDesign = create new node, isMediaTemplateVariant = update node (refactor chi dung selectedDesign sau)
              selectedDesign === DESTINATION_TYPE.MEDIA_TEMPLATE ||
              isMediaTemplateVariant
            ) {
              inputConfig.display.inputs.content.inputType = MEDIA_TEMPLATE;
              inputConfig.display.inputs.content.label = 'Select template';
              // Hard jsonTemplate fields
            } else if (
              // selectedDesign = create new node, isJsonTemplateVariant = update node
              selectedDesign === DESTINATION_TYPE.JSON_TEMPLATE ||
              isJsonTemplateVariant
            ) {
              inputConfig.display.inputs.content.inputType = JSON_TEMPLATE;
              inputConfig.display.inputs.content.label = 'Select template';
            }

            // Hard emailTemplate fields
            else if (
              // selectedDesign = create new node, isEmailTemplateVariant = update node
              selectedDesign === DESTINATION_TYPE.EMAIL_TEMPLATE ||
              isEmailTemplateVariant
            ) {
              inputConfig.send.inputs.body.inputType = EMAIL_TEMPLATE;
              inputConfig.send.inputs.body.label = 'Select template';
            }
            // set info variant
            let cacheInfoVariant =
              initDataVariants.cacheInfo[initDataVariants.activeId] || {};
            // console.log(initDataVariants);
            destinationInput = safeParse(cacheInfoVariant.destinationInput, {});
            objectWidgetInput = safeParse(
              cacheInfoVariant.objectWidgetInput,
              {},
            );

            const { basicInputs = [], inputs = {} } = inputConfig[method] || {};
            cacheRawDataInputConfigs = inputs;
            const dynamicData = mapDynamicDataToUI(
              basicInputs,
              inputs,
              destinationInput,
              itemTypeId,
              eventValue,
              triggerType,
              activeNode,
            );

            // Some fields in dynamic data must be set by catalog like: template, ...
            setDynamicTemplateByCatalog({
              inputs,
              dynamicData,
              destinationInfo,
              catalogCode: destCatalogCode,
              cacheInfoDestInput: destinationInput,
              currentWorkflowDestination: workflowDestinationValue,
            });

            // Update dynamic data in case if it has relatedOnChange property
            adjustDynamicDataIfRelatedChange({ dynamicData });

            const editorFields = [];
            const htmlEditorFields = [];
            const mediaTemplateFields = [];
            Object.keys(inputs).forEach(key => {
              if (['editor'].includes(inputs[key].inputType))
                editorFields.push(key);

              if (['html_editor'].includes(inputs[key].inputType))
                htmlEditorFields.push(key);

              if (
                [MEDIA_TEMPLATE, JSON_TEMPLATE, EMAIL_TEMPLATE].includes(
                  inputs[key].inputType,
                )
              )
                mediaTemplateFields.push(key);
            });
            reInitVariantExtraData(initDataVariants, {
              editorFields,
              htmlEditorFields,
              mediaTemplateFields,
            });

            // set info variant
            cacheInfoVariant =
              initDataVariants.cacheInfo[initDataVariants.activeId] || {};
            variantExtraData = safeParse(cacheInfoVariant.variantExtraData, {});

            //
            setData(draft => {
              draft.inputFields = {
                editorFields,
                htmlEditorFields,
                mediaTemplateFields,
              };
              // draft.isFetchInfoData = true; // hard for API update trước
              // console.log('role', { role, variantIds, campaignId });
              draft.status = status;
              draft.objectWidgetInput = objectWidgetInput;
              draft.variantExtraData = variantExtraData;
              draft.inputConfig = inputConfig; // important
              draft.isExistedDestination = true;
              draft.catalogId = value;
              draft.channelId = channelId;
              draft.channelCode = channelCode;
              draft.catalogCode = catalogCode;
              // draft.variantId = variantId;
              draft.variantIds = variantIds;
              draft.variants = initDataVariants;

              draft.campaignId = campaignId;
              draft.testingAudienceIds = testingAudienceIds;
              draft.dynamicFields = dynamicData.dynamicFields;
              dynamicData.dynamicFields.forEach(each => {
                draft.destinationInput[each] =
                  dynamicData.destinationInput[each];
              });
              const options = [...mapDestination.values()];
              draft.campaignName.value = campaignName;
              draft.variantName.value = variantName;
              draft.variantName.colorText = isColorText ? '#000000' : undefined;
              draft.workflowDestination.options = options;
              // console.log(workflowDestinationValue)
              draft.workflowDestination.value = workflowDestinationValue;
              draft.workflowDestination.isValidate = true;
              draft.workflowDestination.disabled = false;
              draft.algorithms.value = {
                value: 'random',
                name: 'random',
                label: MAP_TRANSLATE._DELIVERY_ALGORITHM_RANDOMIZATION,
              };
              draft.campaignSetting = campaignSetting;
            });
            basicInputs.forEach(each => {
              const item = inputs[each];
              const isInitListOptsDone = get(
                dynamicData,
                ['destinationInput', each, 'isInitListOptsDone'],
                false,
              );

              if (
                item.inputType === 'select' &&
                item.sourceType === 'dynamic'
              ) {
                selectDynamicFields.push(item.sourceRef);
                mapSourceRefToname[item.sourceRef] = each;
                //
              } else if (
                item.inputType === 'select' &&
                item.sourceType === 'static' &&
                !isInitListOptsDone
              ) {
                const options = codeItemToValue(item.options);
                mapAllOptions[each] = {
                  list: options,
                  map: listToMap(options),
                };
              } else if (
                item.inputType === 'select' &&
                Array.isArray(item.options) &&
                item.appendOptions
              ) {
                mapAllOptions[each] = {
                  list: item.options,
                  map: listToMap(item.options),
                };
              }
            });
            if (isAppendContentWebChannel) {
              // for web channel
              DATA_VARIANT_WEB_CHANNEL_HARD.basicInputs.forEach(each => {
                const item = DATA_VARIANT_WEB_CHANNEL_HARD.inputs[each];
                if (
                  item.inputType === 'select' &&
                  item.sourceType === 'dynamic'
                ) {
                  selectDynamicFields.push(item.sourceRef);
                  mapSourceRefToname[item.sourceRef] = each;
                  //
                } else if (
                  item.inputType === 'select' &&
                  item.sourceType === 'static'
                ) {
                  const options = codeItemToValue(item.options);
                  mapAllOptions[each] = {
                    list: options,
                    map: listToMap(options),
                  };
                }
              });
            }

            if (
              !mapDestination.get(`${destinationId}`) &&
              res.meta.disableDestinations.length > 0 &&
              actionNode === 'update'
            ) {
              const resData1 = res.meta.disableDestinations;
              const mapDestination1 = mapDestinationToUI(resData1);
              const workflowDestinationValue1 = mapDestination1.get(
                `${destinationId}`,
              );
              const options = [...mapDestination1.values()];
              setData(draft => {
                // draft.workflowDestination.options = options;
                // console.log(workflowDestinationValue1)
                draft.workflowDestination.value = workflowDestinationValue1;
                // draft.workflowDestination.disabled = workflowDestinationValue1;

                draft.workflowDestination.disabledOptions = options;
                // draft.isExistedDestination = false;
                draft.workflowDestination.errors = [errDestinationDisabled];
                // draft.workflowDestination.isValidate = false;
              });
            }
            paramsOptions.extendUrl = selectDynamicFields.join();
          } else if (
            res.code === 200 &&
            !_isEmpty(res.meta) &&
            !_isEmpty(res.meta.disableDestinations)
          ) {
            const disableDestinations = getObjectPropSafely(
              () => res.meta.disableDestinations,
            );
            const destinationDisabled = disableDestinations.find(
              item => item.destinationId === destinationId,
            );
            if (destinationDisabled && destinationDisabled.status === '2') {
              setData(draft => {
                const mapDestinationDiable = mapDestinationToUI(
                  disableDestinations,
                );
                const workflowDestinationValueDisabled = mapDestinationDiable.get(
                  `${destinationId}`,
                );
                const options = [...mapDestinationDiable.values()];

                draft.workflowDestination.value = workflowDestinationValueDisabled;

                draft.workflowDestination.disabledOptions = options;
                draft.workflowDestination.errors = [errDestinationDisabled];
              });
            }
          }
          // else if (actionNode === 'update' && res.code === 200) {
          //   const resData = res.meta.disableDestinations;
          //   const mapDestination = mapDestinationToUI(resData);
          //   const workflowDestinationValue = mapDestination.get(
          //     `${destinationId}`,
          //   );
          //   console.log(workflowDestinationValue);
          //   const options = [...mapDestination.values()];
          //   setData(draft => {
          //     draft.workflowDestination.options = options;
          //     draft.workflowDestination.value = workflowDestinationValue;
          //     draft.workflowDestination.disabled = true;
          //     draft.isExistedDestination = true;
          //     draft.workflowDestination.errors = ['Destination is disabled'];
          //     draft.workflowDestination.isValidate = true;
          //   });
          // }
          else {
            setData(draft => {
              draft.isExistedDestination = false;
              draft.workflowDestination.errors = [errDestinationRemoved];
              draft.workflowDestination.isValidate = false;
              draft.workflowDestination.disabled = false;
            });
          }
          return selectDynamicFields.length === 0
            ? { data: [] }
            : ServiceOptionsFn({ ...paramsOptions });
        })
        .then(res => {
          if (activeNode.nodeId !== nodeIdRef.current) {
            return null;
          }
          // HARD
          // console.log('Then 2 => ServiceOptionsFn', { res, selectDynamicFields });
          selectDynamicFields.forEach(each => {
            const aliasName = mapSourceRefToname[each];
            mapAllOptions[aliasName] = {
              list: dataEntrieToList(res.data),
              map: listToMap(dataEntrieToList(res.data)),
              // list: res.data[each],
              // map: listToMap(res.data[each]),
            };
          });

          // NOTE: Hard to update some config of the variant inputs
          dataVariantHardUpdateInputs({
            channelCode,
            catalogCode,
            setData,
            destinationInput,
          });

          setData(draft => {
            draft.isFetchInfoData = true;
            draft.isOpened = true;
            draft.dynamicFields.forEach(each => {
              if (mapAllOptions[each]) {
                let initValue = (destinationInput || {})[each] || '';

                // For case init data if input type === 'select' when create
                if (
                  !_isEmpty(cacheRawDataInputConfigs) &&
                  (design === 'create' ||
                    (isBlastCampaign && _isEmpty(String(initValue))))
                ) {
                  const { default: defaultValue = '', inputType = '' } =
                    cacheRawDataInputConfigs[each] || {};

                  if (!_isEmpty(defaultValue) && inputType === 'select') {
                    initValue = defaultValue;
                  }
                } else if (
                  _isEmpty(String(initValue)) &&
                  !_isEmpty(cacheRawDataInputConfigs) &&
                  !_isEmpty(cacheRawDataInputConfigs[each])
                ) {
                  const {
                    appendOptions = false,
                    default: defaultValue = '',
                    inputType = '',
                  } = cacheRawDataInputConfigs[each];
                  let defaultValueTemp = defaultValue;
                  if (_.isObject(defaultValueTemp)) {
                    defaultValueTemp = defaultValueTemp.value;
                  }

                  if (appendOptions && inputType === 'select') {
                    initValue = defaultValueTemp;
                  }
                }

                draft.destinationInput[each].options = mapAllOptions[each].list;
                draft.destinationInput[each].mapOptions =
                  mapAllOptions[each].map;
                if (['string', 'number'].includes(typeof initValue)) {
                  draft.destinationInput[each].value =
                    mapAllOptions[each].map[initValue];
                }
              }
            });
            const piningZone = ((mapAllOptions.zoneId || {}).list || []).find(
              item => item.label === 'Pining Zone',
            );

            DATA_VARIANT_WEB_CHANNEL_HARD.basicInputs.forEach(each => {
              if (mapAllOptions[each]) {
                const initValue = (initData || {})[each] || '';
                draft[each].options = mapAllOptions[each].list;
                draft[each].mapOptions = mapAllOptions[each].map;
                draft[each].value = mapAllOptions[each].map[initValue];

                if (
                  piningZone &&
                  [
                    DESTINATION_TYPE.MEDIA_TEMPLATE,
                    DESTINATION_TYPE.JSON_TEMPLATE,
                  ].includes(selectedDesign)
                ) {
                  if (initValue) {
                    draft[each].value = mapAllOptions[each].map[initValue];
                  } else if (each === 'zoneId') {
                    draft[each].value = piningZone;
                  } else if (each === 'contentPlacement') {
                    const contentPlacementPiningzone = (
                      piningZone.zoneSetting || {}
                    ).contentPlacement;

                    // default contentPlacement of display zone
                    draft.contentPlacement.value =
                      mapAllOptions.contentPlacement.map[
                        contentPlacementPiningzone
                      ];
                  } else {
                    // eslint-disable-next-line prefer-destructuring
                    draft[each].value = mapAllOptions[each].list[0];
                  }
                }
              }
            });

            // update values blast campaigns
            if (isBlastCampaign) {
              if (triggerType === NODE_TYPE.EVENT_BASED) {
                const filter = getObjectPropSafely(() => current['filter'], {});
                const initValue = toUINodeFilter(filter) || {};

                draft['filter'].value = initValue;
                draft['filter'].initValue = initValue;
              }
            }
            draft.isInitDone = true;
          });

          setInfoData(draft => {
            draft.isLoading = false;
          });
        })
        .then(() => {
          if (activeNode.nodeId !== nodeIdRef.current) {
            return null;
          }
          const paramsCustomInput = {
            itemTypeId: -1010,
          };
          return ServiceCustomInput(paramsCustomInput);
        })
        .then(resCustomInputCampaign => {
          if (activeNode.nodeId !== nodeIdRef.current) {
            return null;
          }
          const { dataOut, customInputAttributes } = serializeDataInputAttrs({
            customInputAttributes:
              resCustomInputCampaign.data[0].customInputAttributes,
            positions: resCustomInputCampaign.data[0].positions,
          });
          const templateWorkspaces = _.cloneDeep(dataOut);

          const workspacesToFE = _.cloneDeep(
            mapCustomInputToFe(
              campaignCustomInputs.customInputs,
              templateWorkspaces,
            ),
          );

          setData(draft => {
            draft.campaignCustomInput = {
              templateWorkspaces: _.cloneDeep(templateWorkspaces),
              workspaces: workspacesToFE,
              customInputs: campaignCustomInputs.customInputs,
              customInputAttributes,
            };
          });
        })
        .catch(err => {
          if (!err.isCanceled) {
            addMessageToQueue({
              path:
                'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/useFetchData.js',
              func: 'fetchDataAPI',
              data: err.stack,
            });
            console.warn('err', err);
          }
        });
    }
  }, [activeNode.nodeId]);

  useEffect(() => {
    nodeIdRef.current = activeNode.nodeId;
    // get info campaign vs variant vs .... để  convert API Data -> FE Data
    fetchDataAPI();
  }, [activeNode.nodeId]);

  const fetchDataContent = useCallback(() => {
    // console.log('fetchDataContent initData', initData, activeNode, data);
    const mapAllOptions = {};
    const mapSourceRefToname = {};
    const selectDynamicFields = [];
    const paramsOptions = {};

    const { destinationSetting } = data.workflowDestination.value;
    const { method = 'send' } = destinationSetting;
    const isMethodChange = data.destinationMethod !== method;
    const { inputConfig } = data;
    const { basicInputs = [], inputs = {} } = inputConfig[method] || {};
    const dynamicData = mapDynamicDataToUI(
      basicInputs,
      inputs,
      data.destinationInput,
      itemTypeId,
      eventValue,
      triggerType,
      activeNode,
    );
    // console.log(
    //   'dynamicData',
    //   dynamicData,
    //   basicInputs,
    //   inputs,
    //   inputConfig[method],
    // );

    // method giữ nguyên thì dùng lại data cũ
    setData(draft => {
      draft.destinationMethod = method;
      draft.isExistedDestination = true;
      draft.dynamicFields = dynamicData.dynamicFields;
      dynamicData.dynamicFields.forEach(each => {
        draft.destinationInput[each] = dynamicData.destinationInput[each];
        // if (isMethodChange) {
        draft.destinationInput[each].value = '';
        draft.destinationInput[each].initValue = '';
        // }
      });
    });
    basicInputs.forEach(each => {
      const item = inputs[each];
      if (item.inputType === 'select' && item.sourceType === 'dynamic') {
        selectDynamicFields.push(item.sourceRef);
        mapSourceRefToname[item.sourceRef] = each;
        //
      } else if (item.inputType === 'select' && item.sourceType === 'static') {
        const options = codeItemToValue(item.options);
        mapAllOptions[each] = {
          list: options,
          map: listToMap(options),
        };
      }
    });

    // for web channel
    DATA_VARIANT_WEB_CHANNEL_HARD.basicInputs.forEach(each => {
      const item = DATA_VARIANT_WEB_CHANNEL_HARD.inputs[each];
      if (item.inputType === 'select' && item.sourceType === 'dynamic') {
        selectDynamicFields.push(item.sourceRef);
        mapSourceRefToname[item.sourceRef] = each;
        //
      } else if (item.inputType === 'select' && item.sourceType === 'static') {
        const options = codeItemToValue(item.options);
        mapAllOptions[each] = {
          list: options,
          map: listToMap(options),
        };
      }
    });
    paramsOptions.extendUrl = selectDynamicFields.join();
    cancellablePromise(
      selectDynamicFields.length === 0
        ? Promise.resolve({ data: [] })
        : ServiceOptionsFn({ ...paramsOptions }),
    )
      .then(res => {
        if (activeNode.nodeId !== nodeIdRef.current) {
          return null;
        }
        // console.log('res: ', res);
        // HARD
        selectDynamicFields.forEach(each => {
          const aliasName = mapSourceRefToname[each];
          mapAllOptions[aliasName] = {
            list: res.data[each],
            map: listToMap(res.data[each]),
          };
        });
        setData(draft => {
          draft.dynamicFields.forEach(each => {
            if (mapAllOptions[each]) {
              const initValue = (initData.destinationInput || {})[each] || '';

              draft.destinationInput[each].options = mapAllOptions[each].list;
              draft.destinationInput[each].mapOptions = mapAllOptions[each].map;
              draft.destinationInput[each].value =
                mapAllOptions[each].map[initValue];
            }
          });
          draft.webChannelFields.forEach(each => {
            if (mapAllOptions[each]) {
              const initValue = (initData || {})[each] || '';

              draft[each].options = mapAllOptions[each].list;
              draft[each].mapOptions = mapAllOptions[each].map;
              draft[each].value = mapAllOptions[each].map[initValue];
            }
          });
          draft.isInitDone = true;
        });
        setInfoData(draft => {
          draft.isLoading = false;
        });
      })
      .catch(err => {
        if (!err.isCanceled) {
          addMessageToQueue({
            path:
              'app/modules/Dashboard/MarketingHub/Journey/Create/Content/Nodes/Destination/useFetchData.js',
            func: 'fetchDataAPI',
            data: err.stack,
          });
          console.warn('err', err);
        }
      });
  }, [data.triggerFetchContent]);

  const fetchDataCatalogInfo = async () => {
    setInfoData(draft => {
      draft.isLoadingCompose = true;
    });
    const { catalogId, destinationId } = data;

    const res = await ServiceFn({
      objectId: catalogId,
      _owner_id: getCurrentOwnerId(),
    });
    if (activeNode.nodeId !== nodeIdRef.current) {
      return null;
    }
    const newCatalogCode = getObjectPropSafely(
      () => res.meta.catalogInfo[0].catalogCode,
      '',
    );
    const inputConfig = getObjectPropSafely(
      () => res.meta.catalogInfo[0].catalogInput.inputConfig,
      {},
    );
    const resData = getObjectPropSafely(() => res.data, {});
    const mapDestination = mapDestinationToUI(resData);
    const workflowDestinationValue =
      mapDestination.get(`${destinationId}`) ||
      mapDestination.entries().next().value[1];
    const { destinationSetting } = workflowDestinationValue;
    const { method = 'send' } = destinationSetting;

    // Need to change input config if user is changing the catalog or method
    if (
      +data.previousCatalogId !== +catalogId ||
      (data.previousMethod && data.previousMethod !== method)
    ) {
      injectInputConfigHardForAPIByChannel({
        inputConfig,
        channelCode,
        catalogCode: data?.catalogCode || catalogCode,
      });

      if (
        // selectedDesign = create new node, isEmailTemplateVariant = update node
        selectedDesign === DESTINATION_TYPE.EMAIL_TEMPLATE
      ) {
        inputConfig.send.inputs.body.inputType = EMAIL_TEMPLATE;
        inputConfig.send.inputs.body.label = 'Select template';
      }
      const { basicInputs = [], inputs = {} } = inputConfig[method] || {};

      const dynamicData = mapDynamicDataToUI(
        basicInputs,
        inputs,
        {},
        itemTypeId,
        eventValue,
        triggerType,
        activeNode,
        isBlastCampaign,
      );

      const destinationInput = {};

      // Some fields in dynamic data must be set by catalog like: template, ...
      setDynamicTemplateByCatalog({
        inputs,
        dynamicData,
        destinationInfo,
        catalogCode: newCatalogCode,
        cacheInfoDestInput: {}, // Reset data => not using cache
        currentWorkflowDestination: workflowDestinationValue,
      });

      dynamicData.dynamicFields.forEach(each => {
        destinationInput[each] = dynamicData.destinationInput[each];
      });

      setData(draft => {
        draft.inputConfig = inputConfig;
        draft.dynamicFields = dynamicData.dynamicFields;
        draft.workflowDestination.value = workflowDestinationValue;
        draft.destinationInput = destinationInput;
        draft.catalogCodeBeforeFetchInfo = newCatalogCode;

        draft.triggerFetchCatalogInfo = 0;
      });
    } else {
      setData(draft => {
        draft.workflowDestination.value = workflowDestinationValue;
        draft.triggerFetchCatalogInfo = 0;
        draft.catalogCodeBeforeFetchInfo = newCatalogCode;
      });
    }

    setInfoData(draft => {
      draft.isLoadingCompose = false;
    });
  };

  useEffect(() => {
    if (data.triggerFetchContent > 0) {
      fetchDataContent();
    }
  }, [data.triggerFetchContent]);

  useEffect(() => {
    if (data.triggerFetchCatalogInfo > 0) {
      fetchDataCatalogInfo();
    }
  }, [data.triggerFetchCatalogInfo]);

  return [{ ...infoData, data }, { setData }];
};

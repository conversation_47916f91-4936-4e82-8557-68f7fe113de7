/* eslint-disable no-console */
/* eslint-disable camelcase */
/* eslint-disable no-undef */
/* eslint-disable no-param-reassign */
import axios from 'axios';
import { safeParse } from 'utils/common';

import {
  URL_API,
  URL_INSIGHT,
  URL_MONITOR,
  URL_TEMPLATE_MEDIA_API,
} from '../../config/common';
import HugeUploader from './hugeUploader';
import {
  getCurrentAccessUserId,
  getCurrentOwnerId,
  getPortalId,
  getToken,
} from './web/cookie';
import { getUserLocaleLanguage } from './web/portalSetting';
import { addMessageToQueue } from './web/queue';
import { isNumeric } from './web/utils';

const serviceUnavailbale = {
  status: true,
  data: {
    code: 503,
    message: 'FE Service Unavailable',
  },
};

export function urlHasQueryString(url) {
  const arr = url.split('?');
  if (arr.length > 1 && arr[1] !== '') {
    return true;
  }
  return false;
}

/**
 * Call API
 * @param {*} endpoint
 * @param {*} method
 * @param {*} body
 * @param {*} headers
 * @returns
 */
export function callApi(endpoint, method = 'GET', body, headers) {
  if (typeof headers === 'object') {
    headers['Content-Type'] = 'application/json; charset=utf-8';
  }

  return axios({
    method,
    url: `${URL_API}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    console.log({ err });
    return serviceUnavailbale;
  });
}

/**
 * Main utils for project
 * @param {*} endpoint
 * @param {*} method
 * @param {*} body
 * @param {*} others data for pass some data, currently is use for case pass _user_id and override it
 * @returns
 */
export async function callApiWithAuth(
  endpoint,
  method = 'GET',
  body,
  others = {},
  domain = URL_API,
) {
  const apiPid = getPortalId();
  const apiToken = getToken();
  const _user_id = getCurrentAccessUserId();

  let _owner_id = getCurrentOwnerId();
  // get from other params
  if (Object.prototype.hasOwnProperty.call(others, '_owner_id')) {
    _owner_id = safeParse(others._owner_id, '');
  }

  const isJourney = window.location.href.includes(
    `${_user_id}/marketing-hub/journeys`,
  );
  const isWebPersonalizeSettings = window.location.href.includes(
    'web-personalization',
  );

  if (_owner_id === 'all' && !isJourney && !isWebPersonalizeSettings) {
    _owner_id = _user_id;
  }
  // === all mà đi getCurrentOwnerId() là ko hợp lý cho case chưa chọn owner breadcrumb, vì lúc này này API báo rằng ko chọn là ko truyền
  // if (_owner_id === 'all') {
  //   // read from local session if value equal "all"
  //   _owner_id = getCurrentOwnerId();
  // }

  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };

  if (urlHasQueryString(endpoint)) {
    endpoint += `&portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&_user_id=${_user_id}`;
  } else if (body && body.types) {
    endpoint += `?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&_user_id=${_user_id}&types=${
      body.types
    }`;
  } else {
    endpoint += `?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&_user_id=${_user_id}`;
  }

  if (isNumeric(_owner_id)) {
    endpoint += `&_owner_id=${_owner_id}`;
  }

  if (endpoint.includes('journey-histories')) {
    endpoint += `&token=${apiToken}`;
  }

  return axios({
    method,
    url: `${domain}/${endpoint}`,
    data: body,
    headers,
    signal: others?.signal,
    cancelToken: others?.cancelToken,
  }).catch(err => {
    console.log(err);
    if (others?.responseError) {
      return err?.response;
    }
    return serviceUnavailbale;
  });
}

export async function callApiWithAuthV2({
  endpoint,
  method = 'GET',
  body,
  others = {},
  domain = URL_API,
}) {
  const apiPid = getPortalId();
  const apiToken = getToken();
  const _user_id = getCurrentAccessUserId();

  let _owner_id = getCurrentOwnerId();
  // get from other params
  if (Object.prototype.hasOwnProperty.call(others, '_owner_id')) {
    _owner_id = safeParse(others._owner_id, '');
  }

  const isJourney = window.location.href.includes('marketing-hub/journeys');
  if (_owner_id === 'all' && !isJourney) {
    _owner_id = _user_id;
  }
  // === all mà đi getCurrentOwnerId() là ko hợp lý cho case chưa chọn owner breadcrumb, vì lúc này này API báo rằng ko chọn là ko truyền
  // if (_owner_id === 'all') {
  //   // read from local session if value equal "all"
  //   _owner_id = getCurrentOwnerId();
  // }
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };

  if (urlHasQueryString(endpoint)) {
    endpoint += `&portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&_user_id=${_user_id}`;
  } else {
    endpoint += `?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&_user_id=${_user_id}`;
  }

  if (isNumeric(_owner_id)) {
    endpoint += `&_owner_id=${_owner_id}`;
  }

  // console.log({ _owner_id, bool: isNumeric(_owner_id), endpoint, body });

  return axios({
    method,
    url: `${domain}/${endpoint}`,
    data: body,
    headers,
    signal: others.signal,
    cancelToken: others.cancelToken,
  }).catch(err => {
    console.log(err);
    return serviceUnavailbale;
  });
}

export function callApiWithoutHeaders({
  endpoint,
  method = 'GET',
  body,
  others = {},
  domain = URL_API,
  isAddAccountId = false,
}) {
  const apiPid = getPortalId();
  const apiToken = getToken();
  const _user_id = getCurrentAccessUserId();

  let _owner_id = getCurrentOwnerId();
  if (Object.prototype.hasOwnProperty.call(others, '_owner_id')) {
    _owner_id = safeParse(others._owner_id, '');
  }

  if (urlHasQueryString(endpoint)) {
    endpoint += `&portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&_user_id=${_user_id}&_token=${apiToken}`;
  } else {
    endpoint += `?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&_user_id=${_user_id}&_token=${apiToken}`;
  }

  if (isAddAccountId) {
    endpoint += `&_account_id=${_user_id}`;
  }

  if (isNumeric(_owner_id)) {
    endpoint += `&_owner_id=${_owner_id}`;
  }

  return axios({
    method,
    url: `${domain}/${endpoint}`,
    data: body,
  }).catch(err => {
    console.log(err);
    return serviceUnavailbale;
  });
}

export function callApiUploadHugeFile(endpoint, _method = 'POST', body) {
  const apiPid = getPortalId();
  const apiToken = getToken();
  const headers = {
    // 'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };
  const _user_id = getCurrentAccessUserId();
  let _owner_id = getCurrentOwnerId();

  const isJourney = window.location.href.includes('marketing-hub/journeys');
  if (_owner_id === 'all' && !isJourney) {
    _owner_id = _user_id;
  }

  if (urlHasQueryString(endpoint)) {
    endpoint += `&portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}`;
  } else {
    endpoint += `?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}`;
  }

  if (isNumeric(_owner_id)) {
    endpoint += `&_owner_id=${_owner_id}`;
  }

  const uploader = new HugeUploader({
    headers,
    endpoint: `${URL_API}/${endpoint}`,
    file: body,
  });
  return uploader;
  // subscribe to events
  // uploader.on('error', err => {
  //   console.error('Something bad happened', err.detail);
  // });

  // uploader.on('progress', progress => {
  //   console.log(`The upload is at ${progress.detail}%`);
  // });

  // uploader.on('finish', finish => {
  //   console.log('yeahhh - last response body:', {
  //     finish,
  //     detail: finish.detail
  //   });
  // });
}

/**
 * Call API Media Template
 * @param {*} endpoint
 * @param {*} method
 * @param {*} body
 * @returns
 */
export function callApiMediaTemplateWithAuth(endpoint, method = 'GET', body) {
  const apiPid = getPortalId();
  const apiToken = getToken();
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };

  if (urlHasQueryString(endpoint)) {
    endpoint += `&portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}`;
  } else {
    endpoint += `?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}`;
  }

  return axios({
    method,
    url: `${URL_TEMPLATE_MEDIA_API}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    // addMessageToQueue({
    //   path: 'app/utils/request.js',
    //   func: 'callApiWithAuth',
    //   data: err.stack,
    // });
    console.log(err);
    return serviceUnavailbale;
  });
}

/**
 * Call API Media Template
 * @param {*} endpoint
 * @param {*} method
 * @param {*} body
 * @returns
 */
export function callMediaTemplateUploadFile(
  endpoint,
  method = 'GET',
  body,
  domain = PORTAL_CONFIG.URL_MEDIA_TEMPLATE,
) {
  // const apiPid = getPortalId();
  const apiToken = getToken();
  const _user_id = getCurrentAccessUserId();

  const headers = {
    'Content-Type': 'multipart/form-data',
    token: apiToken,
  };

  if (urlHasQueryString(endpoint)) {
    endpoint += `&_token=${apiToken}&_user_id=${_user_id}&_account_id=${_user_id}`;
  } else {
    endpoint += `?&_token=${apiToken}&_user_id=${_user_id}&_account_id=${_user_id}`;
  }

  return axios({
    method,
    url: `${domain}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    addMessageToQueue({
      path: 'app/utils/request.js',
      func: 'callApiWithAuth',
      data: err.stack,
    });
    return serviceUnavailbale;
  });
}

/**
 * Call API Media Template
 * @param {*} endpoint
 * @param {*} method
 * @param {*} body
 * @returns
 */
export function callMediaTemplateUploadBase64(
  endpoint,
  method = 'GET',
  body,
  domain = PORTAL_CONFIG.URL_MEDIA_TEMPLATE,
) {
  // const apiPid = getPortalId();
  const apiToken = getToken();
  const _user_id = getCurrentAccessUserId();

  const headers = {
    'Content-Type': 'application/json',
    token: apiToken,
  };

  if (urlHasQueryString(endpoint)) {
    endpoint += `&_token=${apiToken}&_user_id=${_user_id}&_account_id=${_user_id}`;
  } else {
    endpoint += `?&_token=${apiToken}&_user_id=${_user_id}&_account_id=${_user_id}`;
  }

  return axios({
    method,
    url: `${domain}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    addMessageToQueue({
      path: 'app/utils/request.js',
      func: 'callApiWithAuth',
      data: err.stack,
    });
    console.log(err);
    return serviceUnavailbale;
  });
}

// OptinTemplate
export function callApiMediaTemplateWithAuthV2({
  endpoint,
  method = 'GET',
  body,
  domain = PORTAL_CONFIG.URL_MEDIA_TEMPLATE,
}) {
  const apiPid = getPortalId();
  const apiToken = getToken();
  const _user_id = getCurrentAccessUserId();

  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    // token: apiToken,
  };

  if (urlHasQueryString(endpoint)) {
    endpoint += `&_token=${apiToken}&_user_id=${_user_id}&_account_id=${_user_id}`;
  } else {
    endpoint += `?&_token=${apiToken}&_user_id=${_user_id}&_account_id=${_user_id}`;
  }

  // if (isNumeric(_owner_id)) {
  //   endpoint += `&_owner_id=${_owner_id}`;
  // }

  if (urlHasQueryString(endpoint)) {
    endpoint += `&portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}`;
  } else {
    endpoint += `?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}`;
  }

  return axios({
    method,
    url: `${domain}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    // addMessageToQueue({
    //   path: 'app/utils/request.js',
    //   func: 'callApiWithAuth',
    //   data: err.stack,
    // });
    console.log(err);
    return serviceUnavailbale;
  });
}

export function callApiWithPortalId(
  portalId = null,
  endpoint,
  method = 'GET',
  body,
) {
  let apiPid = portalId;
  if (apiPid === null || apiPid === undefined) {
    apiPid = getPortalId();
  }

  const apiToken = getToken();
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };

  if (urlHasQueryString(endpoint)) {
    endpoint += `&portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}`;
  } else {
    endpoint += `?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}`;
  }

  return axios({
    method,
    url: `${URL_API}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    console.log(err);
    return serviceUnavailbale;
  });
}

/**
 * Call in case option token and portalId
 * @param {*}} params {token?}
 * @param {*} endpoint
 * @param {*} method
 * @param {*} body
 * @returns
 */
export function callApiBasic(params, endpoint, method = 'GET', body) {
  const { token: apiToken } = params;

  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };

  return axios({
    method,
    url: `${URL_API}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    console.log(err);
    return serviceUnavailbale;
  });
}

/**
 * API for call create token network
 * @param {*}} endpoint
 * @param {*} method
 * @param {*} body
 * @returns
 */
export function callApiWithRootToken(endpoint, method = 'GET', body) {
  const apiToken = getToken();
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };

  return axios({
    method,
    url: `${URL_API}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    console.log(err);
    return serviceUnavailbale;
  });
}

export function callApiWithOnlyToken(endpoint, method = 'GET', body) {
  const apiToken = getToken();
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };

  return axios({
    method,
    url: `${URL_API}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    // addMessageToQueue({
    //   path: 'app/utils/request.js',
    //   func: 'callApiWithOnlyToken',
    //   data: err.stack,
    // });
    console.log(err);
    return serviceUnavailbale;
  });
}

export function callApiWithAuthAndCancel(
  source,
  endpoint,
  method = 'GET',
  body,
) {
  const apiPid = getPortalId();
  const apiToken = getToken();
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };
  const _user_id = getCurrentAccessUserId();
  let _owner_id = getCurrentOwnerId();

  if (_owner_id === 'all') {
    _owner_id = _user_id;
  }

  if (urlHasQueryString(endpoint)) {
    endpoint += `&portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&_user_id=${_user_id}`;
  } else {
    endpoint += `?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&_user_id=${_user_id}`;
  }

  if (isNumeric(_owner_id)) {
    endpoint += `&_owner_id=${_owner_id}`;
  }

  return axios({
    method,
    url: `${URL_API}/${endpoint}`,
    data: body,
    headers,
    cancelToken: source.token,
    timeout: 120000,
    // timeout: 10000,
    // cancelToken: new axios.CancelToken(function executor(c) {
    // An executor function receives a cancel function as a parameter
    // cancel = c;
    // }),
  }).catch(err => {
    if (axios.isCancel(err)) {
      return null;
    }
    console.log(err);
    return serviceUnavailbale;
  });
}

export function buildUrlWithLanguageCode(url) {
  let temp = url;
  if (urlHasQueryString(temp)) {
    temp += `&languageCode=${getUserLocaleLanguage()}`;
  } else {
    temp += `?languageCode=${getUserLocaleLanguage()}`;
  }
  return temp;
}

export function callApiWithMockup(startpoint, endpoint, method = 'GET', body) {
  const apiPid = getPortalId();
  const apiToken = getToken();
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };

  if (urlHasQueryString(endpoint)) {
    endpoint += `&portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}`;
  } else {
    endpoint += `?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}`;
  }

  return axios({
    method,
    url: `${startpoint}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    // addMessageToQueue({
    //   path: 'app/utils/request.js',
    //   func: 'buildUrlWithLanguageCode',
    //   data: err.stack,
    // });
    console.log(err);
    return serviceUnavailbale;
  });
}

export function callApiWithMockupOut(url, endpoint, method, body) {
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: endpoint,
  };

  return axios({
    method,
    url: `${url}`,
    data: body,
    headers,
  }).catch(err => {
    // addMessageToQueue({
    //   path: 'app/utils/request.js',
    //   func: 'callApiWithMockupOut',
    //   data: err.stack,
    // });
    console.log(err);
    return serviceUnavailbale;
  });
}

const headersWhoAmI = {
  'Content-Type': 'application/json; charset=utf-8',
};
export function callApiWhoAmI(endpoint, method = 'GET', body) {
  return axios({
    method,
    url: `${PORTAL_CONFIG.URL_IAM_API}/${endpoint}`,
    data: body,
    headers: headersWhoAmI,
  }).catch(err => {
    addMessageToQueue({
      path: 'app/utils/request.js',
      func: 'callApiWhoAmI',
      data: err.stack,
    });
    console.log(err);
    return serviceUnavailbale;
  });
}

export function callApiLogout(endpoint, method = 'GET', body) {
  return axios({
    method,
    url: `${PORTAL_CONFIG.URL_IAM_API}/${endpoint}?_token=${body._token}`,
    headers: headersWhoAmI,
  }).catch(err => {
    addMessageToQueue({
      path: 'app/utils/request.js',
      func: 'callApiWhoAmI',
      data: err.stack,
    });
    console.log(err);
    return serviceUnavailbale;
  });
}

export function callApiPermisison(endpoint, method = 'GET', body) {
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    // token: endpoint,
  };
  return axios({
    method,
    url: `${PORTAL_CONFIG.URL_PERMISSION_API}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    addMessageToQueue({
      path: 'app/utils/request.js',
      func: 'callApiPermisison',
      data: err.stack,
    });
    console.log(err);
    return serviceUnavailbale;
  });
}

export function callApiWithDownload(
  endpoint,
  method = 'POST',
  body,
  config,
  others = {},
  domain = URL_API,
) {
  const apiPid = getPortalId();
  const apiToken = getToken();
  const _user_id = getCurrentAccessUserId();
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };
  let _owner_id = getCurrentOwnerId();
  // get from other params
  if (Object.prototype.hasOwnProperty.call(others, '_owner_id')) {
    _owner_id = safeParse(others._owner_id, getCurrentOwnerId());
  }

  const isJourney = window.location.href.includes('marketing-hub/journeys');
  if (_owner_id === 'all' && !isJourney) {
    _owner_id = _user_id;
  }
  if (urlHasQueryString(endpoint)) {
    endpoint += `&portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&_user_id=${_user_id}`;
  } else {
    endpoint += `?portalId=${apiPid}&languageCode=${getUserLocaleLanguage()}&_user_id=${_user_id}`;
  }
  if (isNumeric(_owner_id)) {
    endpoint += `&_owner_id=${_owner_id}`;
  }

  return axios({
    method,
    url: `${domain}/${endpoint}`,
    data: body,
    // timeout: 10 * 60 * 1000,
    headers,
    responseType: 'blob', // Important
    // file: body.fileFormat,
  })
    .then(res => {
      res.code = res.status;
      if (res.code === 200 || res.code === 201) {
        const blob = new Blob([res.data], {
          type: res.headers['content-type'],
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        console.log({ config, body });
        console.log(config.fileFormat === 'csv' && !body.compressFile);
        link.download =
          config.fileFormat === 'csv' && !body.compressFile
            ? `${config.fileName}.${config.fileFormat}`
            : `${config.fileName}`;
        // .${
        //   body.compressFile ? 'zip' : config.fileFormat
        // }`;
        link.click();
      }
      return res;
    })
    .catch(err => {
      console.log(err);
      return serviceUnavailbale;
    });
}

export const callApiMonitor = (endpoint, method = 'GET', body) => {
  const portalId = getPortalId();
  const apiToken = getToken();

  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    token: apiToken,
  };

  if (urlHasQueryString(endpoint)) {
    endpoint += `&portalId=${portalId}&languageCode=${getUserLocaleLanguage()}`;
  } else {
    endpoint += `?portalId=${portalId}&languageCode=${getUserLocaleLanguage()}`;
  }

  return axios({
    method,
    data: body,
    headers,
    url: `${URL_MONITOR}/${endpoint}`,
  }).catch(_err => serviceUnavailbale);
};

export function callApiWithPublicNetWorkInsight(
  endpoint,
  method = 'GET',
  body,
  // others = {},
) {
  // const apiPid = getPortalId();
  const apiToken = getToken();
  const _user_id = getCurrentAccessUserId();

  // let _owner_id = getCurrentOwnerId();
  // get from other params
  // if (Object.prototype.hasOwnProperty.call(others, '_owner_id')) {
  //   _owner_id = safeParse(others._owner_id, '');
  // }

  // === all mà đi getCurrentOwnerId() là ko hợp lý cho case chưa chọn owner breadcrumb, vì lúc này này API báo rằng ko chọn là ko truyền
  // if (_owner_id === 'all') {
  //   // read from local session if value equal "all"
  //   _owner_id = getCurrentOwnerId();
  // }
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    // token: apiToken,
  };

  if (urlHasQueryString(endpoint)) {
    endpoint += `&_token=${apiToken}&_user_id=${_user_id}&_account_id=${_user_id}`;
  } else {
    endpoint += `?&_token=${apiToken}&_user_id=${_user_id}&_account_id=${_user_id}`;
  }

  // if (isNumeric(_owner_id)) {
  //   endpoint += `&_owner_id=${_owner_id}`;
  // }
  return axios({
    method,
    url: `${URL_INSIGHT}/${endpoint}`,
    data: body,
    headers,
  }).catch(err => {
    console.log(err);
    return serviceUnavailbale;
  });
}
